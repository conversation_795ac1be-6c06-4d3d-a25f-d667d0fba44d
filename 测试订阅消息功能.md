# 互助消息订阅功能测试指南

## 功能概述

已为互助模块添加了微信小程序订阅消息功能，包括：

1. **接单提醒** - 有人响应了您的互助需求
2. **确认提醒** - 您的响应已被选中  
3. **完成通知** - 互助已完成，请进行评价
4. **评价完成通知** - 您收到了新的评价

## 已配置的模板ID

```javascript
const templateIds = [
  'FEYS961Oa874QfZkrEi_RvV6YZEg2j7yyNboQpOTstM', // 接单提醒
  'WAfNxInXVQd_PI16Xztbf8eNYBIc7FcfwVFQ72GCYCQ', // 确认提醒  
  'y3fn6LXtrwr-cgwXAlwZpqOTFPts5QUN-z4TEbenMyQ', // 完成通知
  'XEqnmes7-pxAwKZxphZ2DIKWVxl6Eyctst6ydppr6ZY'  // 评价完成通知
]
```

## 订阅触发时机

### 1. 发布互助需求后
- 位置：`前端插件/pages/help/publish/publish.js`
- 触发：发布成功后1.5秒弹出订阅对话框
- 用户可选择订阅或暂不订阅

### 2. 响应互助需求后  
- 位置：`前端插件/pages/help/detail/detail.js`
- 触发：响应提交成功后1.5秒弹出订阅对话框
- 用户可选择订阅或暂不订阅

## 消息数据格式

### 接单提醒 (response)
```javascript
{
  'character_string1': { 'value': 'HLP123' }, // 订单号
  'time2': { 'value': '2024年1月1日 10:30' }, // 时间
  'thing3': { 'value': '张三响应了您的互助需求' } // 备注
}
```

### 确认提醒 (selected)
```javascript
{
  'time1': { 'value': '2024年1月1日 10:30' }, // 时间
  'phrase2': { 'value': '已确认' }, // 状态
  'thing3': { 'value': '您的响应已被选中，请及时联系需求方' }, // 备注
  'character_string4': { 'value': 'HLP123' } // 订单号码
}
```

### 完成通知 (completed)
```javascript
{
  'time1': { 'value': '2024年1月1日 10:30' }, // 完成时间
  'thing2': { 'value': '感谢您的帮助，请对本次互助进行评价' }, // 备注
  'character_string3': { 'value': 'HLP123' }, // 工单编号
  'phrase4': { 'value': '已完成' } // 检测结果
}
```

### 评价完成通知 (rated)
```javascript
{
  'time1': { 'value': '2024年1月1日 10:30' }, // 时间
  'phrase2': { 'value': '5星' }, // 评价结果
  'thing3': { 'value': '服务很好，非常满意' }, // 评价内容
  'phrase4': { 'value': '满意' } // 满意度
}
```

## 测试步骤

### 1. 测试发布订阅
1. 打开小程序，进入互助发布页面
2. 填写完整的互助需求信息
3. 点击发布
4. 等待1.5秒后应该弹出订阅对话框
5. 点击"订阅"按钮
6. 小程序会请求订阅权限
7. 同意后会调用后端API保存订阅状态

### 2. 测试响应订阅
1. 用另一个账号查看互助详情
2. 点击"我要帮助"按钮
3. 填写响应内容并提交
4. 等待1.5秒后应该弹出订阅对话框
5. 重复订阅流程

### 3. 测试消息发送
1. 完成上述订阅后
2. 进行互助流程：响应 → 选择 → 完成 → 评价
3. 每个步骤都应该触发相应的订阅消息

## 数据库检查

### 检查订阅记录
```sql
SELECT * FROM wp_minapper_ext 
WHERE extype = 'subscribeMessage' 
AND extkey = 'help_notifications';
```

### 检查用户openid
```sql
SELECT * FROM wp_minapper_weixin_users 
WHERE userid IN (订阅用户的ID);
```

## 故障排除

### 1. 订阅对话框不出现
- 检查前端代码中的setTimeout是否正确
- 确认用户已登录
- 查看浏览器控制台是否有错误

### 2. 订阅权限请求失败
- 确认模板ID是否正确
- 检查小程序是否有订阅消息权限
- 查看微信开发者工具的错误信息

### 3. 后端保存失败
- 检查API参数是否正确传递
- 确认数据库表结构是否正确
- 查看WordPress错误日志

### 4. 消息发送失败
- 确认用户已订阅且有剩余次数
- 检查模板ID和字段格式是否匹配
- 查看后端错误日志

## 注意事项

1. **一次性订阅**：微信小程序的订阅消息是一次性的，每次发送会消耗一次订阅次数
2. **用户授权**：用户必须主动同意授权才能收到消息
3. **模板限制**：消息内容必须严格按照模板格式，字段类型和长度都有限制
4. **发送频率**：避免频繁发送，遵循微信的使用规范

## 开发日志

- ✅ 配置固定模板ID
- ✅ 修改消息数据格式匹配模板字段
- ✅ 在发布页面添加订阅提示
- ✅ 在详情页面添加订阅提示  
- ✅ 修改后端API支持help_notifications订阅类型
- ✅ 完善前端订阅逻辑和错误处理
