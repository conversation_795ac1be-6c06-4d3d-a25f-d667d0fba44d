<?php
/**
 * 发布功能修复验证
 */

// 引入WordPress环境
require_once('wp-config.php');

echo "<h1>发布功能修复验证</h1>";

// 1. 检查最近的发布记录
echo "<h2>1. 最近发布记录检查</h2>";
global $wpdb;
$table_name = $wpdb->prefix . 'minapper_help_requests';

$recent_requests = $wpdb->get_results("
    SELECT hr.*, p.post_title, p.post_content, u.display_name 
    FROM {$table_name} hr
    LEFT JOIN {$wpdb->posts} p ON hr.topic_id = p.ID
    LEFT JOIN {$wpdb->users} u ON hr.user_id = u.ID
    ORDER BY hr.created_at DESC 
    LIMIT 5
");

if ($recent_requests) {
    echo "<p style='color: green;'>✓ 找到最近的发布记录</p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>标题</th><th>类型</th><th>紧急程度</th><th>位置信息</th><th>发布时间</th><th>发布者</th></tr>";
    
    foreach ($recent_requests as $request) {
        echo "<tr>";
        echo "<td>{$request->id}</td>";
        echo "<td>" . htmlspecialchars($request->post_title) . "</td>";
        echo "<td>{$request->help_type}</td>";
        echo "<td>{$request->urgency}</td>";
        echo "<td>" . htmlspecialchars($request->location_info) . "</td>";
        echo "<td>{$request->created_at}</td>";
        echo "<td>{$request->display_name}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠ 没有找到发布记录</p>";
}

// 2. 检查位置信息格式
echo "<h2>2. 位置信息格式检查</h2>";
$location_requests = $wpdb->get_results("
    SELECT id, location_info, created_at 
    FROM {$table_name} 
    WHERE location_info IS NOT NULL AND location_info != ''
    ORDER BY created_at DESC 
    LIMIT 10
");

if ($location_requests) {
    echo "<p style='color: green;'>✓ 找到包含位置信息的记录</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>位置信息</th><th>格式类型</th><th>发布时间</th></tr>";
    
    foreach ($location_requests as $request) {
        $location = $request->location_info;
        $format_type = '';
        
        // 判断位置信息格式
        if (empty($location)) {
            $format_type = '空';
        } elseif (json_decode($location)) {
            $format_type = 'JSON格式';
        } else {
            $format_type = '文本格式';
        }
        
        echo "<tr>";
        echo "<td>{$request->id}</td>";
        echo "<td>" . htmlspecialchars(substr($location, 0, 50)) . (strlen($location) > 50 ? '...' : '') . "</td>";
        echo "<td>{$format_type}</td>";
        echo "<td>{$request->created_at}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠ 没有找到包含位置信息的记录</p>";
}

// 3. 前端测试指南
echo "<h2>3. 前端测试指南</h2>";

echo "<h3>位置选择功能测试</h3>";
echo "<ol>";
echo "<li><strong>正常流程测试</strong>：";
echo "<ul>";
echo "<li>进入发布页面</li>";
echo "<li>点击位置选择</li>";
echo "<li>如果弹出权限请求，点击允许</li>";
echo "<li>选择一个位置</li>";
echo "<li>检查位置信息是否正确显示</li>";
echo "</ul></li>";

echo "<li><strong>权限拒绝测试</strong>：";
echo "<ul>";
echo "<li>在系统设置中关闭小程序的位置权限</li>";
echo "<li>点击位置选择</li>";
echo "<li>应该弹出权限说明弹窗</li>";
echo "<li>测试'去设置'和'手动输入'两个选项</li>";
echo "</ul></li>";

echo "<li><strong>手动输入测试</strong>：";
echo "<ul>";
echo "<li>选择手动输入位置</li>";
echo "<li>输入位置信息</li>";
echo "<li>检查是否正确保存</li>";
echo "</ul></li>";
echo "</ol>";

echo "<h3>内容清空功能测试</h3>";
echo "<ol>";
echo "<li><strong>发布成功后清空测试</strong>：";
echo "<ul>";
echo "<li>填写完整的发布表单</li>";
echo "<li>点击发布并等待成功</li>";
echo "<li>返回列表页面</li>";
echo "<li>重新进入发布页面</li>";
echo "<li>检查表单是否为空</li>";
echo "</ul></li>";

echo "<li><strong>草稿保存测试</strong>：";
echo "<ul>";
echo "<li>填写部分表单内容</li>";
echo "<li>点击保存草稿</li>";
echo "<li>退出页面再重新进入</li>";
echo "<li>检查内容是否恢复</li>";
echo "</ul></li>";

echo "<li><strong>页面切换测试</strong>：";
echo "<ul>";
echo "<li>填写表单内容但不保存草稿</li>";
echo "<li>直接退出页面</li>";
echo "<li>重新进入发布页面</li>";
echo "<li>检查是否自动保存了草稿</li>";
echo "</ul></li>";
echo "</ol>";

// 4. 常见问题解决
echo "<h2>4. 常见问题解决</h2>";

echo "<h3>位置选择问题</h3>";
echo "<ul>";
echo "<li><strong>权限被拒绝</strong>：引导用户到设置中开启位置权限</li>";
echo "<li><strong>隐私协议错误</strong>：确认app.json中的隐私声明配置正确</li>";
echo "<li><strong>选择失败</strong>：提供手动输入作为备选方案</li>";
echo "</ul>";

echo "<h3>内容清空问题</h3>";
echo "<ul>";
echo "<li><strong>发布后内容仍存在</strong>：检查是否成功调用了resetFormData方法</li>";
echo "<li><strong>草稿意外丢失</strong>：检查onUnload方法是否正常工作</li>";
echo "<li><strong>页面状态异常</strong>：检查onShow方法的逻辑</li>";
echo "</ul>";

// 5. 配置检查
echo "<h2>5. 配置检查</h2>";

echo "<h3>app.json配置</h3>";
echo "<p>确认以下配置存在：</p>";
echo "<pre>";
echo '"permission": {
  "scope.userLocation": {
    "desc": "你的位置信息将用于小程序位置接口的效果展示"
  }
},
"requiredPrivateInfos": [
  "chooseLocation"
]';
echo "</pre>";

echo "<h3>隐私协议</h3>";
echo "<p>确认在小程序管理后台的隐私设置中已添加位置相关的隐私说明。</p>";

echo "<hr>";
echo "<p><strong>测试完成</strong></p>";
echo "<p>请按照上述指南进行测试，如有问题请提供具体的错误信息。</p>";
echo "<p><small>测试完成后请删除此文件：test_publish_fixes.php</small></p>";
?>
