/**
 * 验证登录方法是否正确添加
 * 在小程序开发者工具的控制台中运行此代码
 */

// 获取当前页面实例
const currentPage = getCurrentPages()[getCurrentPages().length - 1];

console.log('=== 登录方法验证 ===');

// 检查方法是否存在
const methods = ['agreeGetUser', 'closeLoginPopup', 'openLoginPopup'];
methods.forEach(method => {
  if (typeof currentPage[method] === 'function') {
    console.log(`✓ ${method} 方法存在`);
  } else {
    console.log(`✗ ${method} 方法不存在`);
  }
});

// 检查数据字段
const dataFields = ['isLoginPopup', 'userInfo', 'userSession', 'hasWechatInstall'];
dataFields.forEach(field => {
  if (currentPage.data.hasOwnProperty(field)) {
    console.log(`✓ ${field} 数据字段存在:`, currentPage.data[field]);
  } else {
    console.log(`✗ ${field} 数据字段不存在`);
  }
});

console.log('=== 验证完成 ===');

// 测试打开登录弹窗（仅用于验证，实际使用时请谨慎）
// currentPage.openLoginPopup();
// console.log('登录弹窗状态:', currentPage.data.isLoginPopup);
