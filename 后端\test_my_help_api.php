<?php
// 测试我的求助API的调试脚本
require_once('../../../wp-load.php');

echo "=== 我的求助API调试测试 ===\n";

// 检查表是否存在
global $wpdb;
$requests_table = $wpdb->prefix . 'minapper_help_requests';
$responses_table = $wpdb->prefix . 'minapper_help_responses';

echo "=== 表存在性检查 ===\n";
echo "需求表 ($requests_table): " . ($wpdb->get_var("SHOW TABLES LIKE '$requests_table'") == $requests_table ? "存在" : "不存在") . "\n";
echo "响应表 ($responses_table): " . ($wpdb->get_var("SHOW TABLES LIKE '$responses_table'") == $responses_table ? "存在" : "不存在") . "\n\n";

// 检查测试数据
echo "=== 测试数据检查 ===\n";
$requests = $wpdb->get_results("SELECT id, user_id, title, status FROM $requests_table LIMIT 5");
echo "需求数据:\n";
foreach ($requests as $req) {
    echo "ID: {$req->id}, 用户: {$req->user_id}, 标题: {$req->title}, 状态: {$req->status}\n";
}

$responses = $wpdb->get_results("SELECT id, request_id, user_id, status FROM $responses_table LIMIT 5");
echo "\n响应数据:\n";
foreach ($responses as $resp) {
    echo "ID: {$resp->id}, 需求ID: {$resp->request_id}, 用户: {$resp->user_id}, 状态: {$resp->status}\n";
}

// 测试API调用
echo "\n=== API调用测试 ===\n";

// 创建模拟的WP_REST_Request对象
class MockRequest {
    private $params;
    
    public function __construct($params) {
        $this->params = $params;
    }
    
    public function get_param($key) {
        return isset($this->params[$key]) ? $this->params[$key] : null;
    }
}

// 实例化控制器
$controller = new RAW_REST_Help_Controller();

// 测试1: 获取用户1发布的需求
echo "测试1: 获取用户1发布的需求\n";
$request1 = new MockRequest([
    'page' => 1,
    'per_page' => 10,
    'user_id' => 1
]);

$result1 = $controller->get_help_list($request1);
if (is_wp_error($result1)) {
    echo "错误: " . $result1->get_error_message() . "\n";
} else {
    $data1 = $result1->get_data();
    echo "返回数据: " . json_encode($data1, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
}

// 测试2: 获取用户2响应的需求
echo "\n测试2: 获取用户2响应的需求\n";
$request2 = new MockRequest([
    'page' => 1,
    'per_page' => 10,
    'response_user_id' => 2
]);

$result2 = $controller->get_help_list($request2);
if (is_wp_error($result2)) {
    echo "错误: " . $result2->get_error_message() . "\n";
} else {
    $data2 = $result2->get_data();
    echo "返回数据: " . json_encode($data2, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
}

// 测试3: 获取所有需求（不筛选用户）
echo "\n测试3: 获取所有需求\n";
$request3 = new MockRequest([
    'page' => 1,
    'per_page' => 5
]);

$result3 = $controller->get_help_list($request3);
if (is_wp_error($result3)) {
    echo "错误: " . $result3->get_error_message() . "\n";
} else {
    $data3 = $result3->get_data();
    echo "返回数据: " . json_encode($data3, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
}

echo "\n=== 测试完成 ===\n";
?>
