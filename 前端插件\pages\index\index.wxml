<!-- * 微慕小程序
* author: jianbo
* organization:  微慕 www.minapper.com 
* 技术支持微信号：Jianbo
* Copyright (c) 2018 https://www.minapper.com All rights reserved. -->
<import src="../../templates/loading/threepoint.wxml" />
<import src="../../templates/login-popup.wxml" />

<!-- 首次进入引导添加到“我的小程序” -->
<view class="addMyMiniapp" wx:if="{{minappAddTip}}" catchtap="shutAddMyMiniapp">
  <view>点击加入我的小程序 ↑</view>
  <text>了解{{shareTitle}}小程序最新功能</text>
</view>

<!-- 顶部Tab菜单 -->
<view class='tab-box' wx:if="{{columns.length != 0}}">
  <scroll-view class="tab-menu" scroll-into-view="tab-{{currentColumn-2>0?currentColumn-2:0}}" scroll-x="true">
    <view bindtap="switchColumn" class="tab-menu-item {{currentColumn==idx?'active':''}}" data-idx="{{idx}}" id="tab-{{idx}}" wx:for="{{columns}}" data-categoryid="{{item.id}}" data-categoryids="{{item.ids}}" data-categoryNames="{{item.names}}" wx:for-index="idx" wx:key="id" data-cover="{{item.category_thumbnail_image}}" data-categoryname="{{item.name}}">
      <text class="{{(system != 'iOS' && (item.catYearIntegral || item.catyearprice)) ? 'pay-tip' : ''}}">{{item.name}}</text>
    </view>
  </scroll-view>
  <!-- 搜索和下拉设置图标 -->
  <text bindtap="openSearch" class="iconfont icon-search tab-search-btn" />

  <text bindtap="openCustomPannel" catchtouchmove="consumeTouchmove" class="iconfont icon-arrow-down tab-custom-btn {{isColumnPannelOpen?'tab-custom-btn-open':''}}" />
</view>
<!-- 点击下拉图标展示出的设置项 -->
<view class="columnPannelHeader" wx:if="{{isColumnPannelOpen}}">
  <text class="columnPannelTitle">所有栏目 </text>
  <text bindtap="openCustomPannel" catchtouchmove="consumeTouchmove" class="iconfont icon-arrow-down tab-custom-btn tab-custom-btn-open" />
</view>
<view catchtouchmove="consumeTouchmove" class="columnPannel full-height" style="height:{{windowHeight}}px;" wx:if="{{isColumnPannelOpen}}">
  <view class="selected-pannel">
    <view bindtap="selectColumn" bindtouchend="columnItemTouchEnd" bindtouchstart="columnItemTouchStart" catchtouchmove="columnItemMoving" class="column-item-selected {{columnSelectedIndex==moveToIndex?'column-item-moving':''}}" data-index="{{columnSelectedIndex}}" disableScroll="true" style="height: {{columnItemHeight}}rpx;line-height: {{columnItemHeight}}rpx;" wx:for="{{columns}}" wx:for-index="columnSelectedIndex" wx:key="id" data-categoryid="{{item.id}}" data-categoryids="{{item.ids}}" data-categorynames="{{item.names}}" data-categoryname="{{item.name}}" data-cover="{{item.category_thumbnail_image}}">
      <text class="column-item-selected-text {{currentColumn==columnSelectedIndex?'column-item-selected-active':''}}">{{item.name}}</text>
    </view>
  </view>
  <view class="me-item-gap"></view>


  <view class="me-item-gap"></view>

  <!-- 列表展示方式 -->
  <text class="columnPannelTitle">列表展示方式
  </text>
  <view class="unselected-pannel">
    <view class='settings-container'>
      <view class="section-choice">
        <radio-group name="radioGroup" bindchange="changeListStyle">
          <label>
            <radio value="1" checked="{{articleStyle == 1}}" />
            左图右文
          </label>
          <label>
            <radio value="2" checked="{{articleStyle == 2}}" />
            右图左文
          </label>
          <label>
            <radio value="3" checked="{{articleStyle == 3}}" />
            大图列表
          </label>
          <label>
            <radio value="4" checked="{{articleStyle == 4}}" />
            多图列表
          </label>
          <label>
            <radio value="5" checked="{{articleStyle == 5}}" />
            瀑布流列表
          </label>
          <label>
            <radio value="6" checked="{{articleStyle == 6}}" />
            无图列表
          </label>
        </radio-group>
      </view>

    </view>
  </view>
</view>

<view wx:if="{{!isColumnPannelOpen}}" class='index-container'>

  <!--  以下内容只在首页查看-->
  <view wx:if="{{currentColumn ==0}}">

    <!-- 轮播图 -->
    <view class="index_top">
      <view class="swiper-container">
        <swiper wx:if="{{swiperArticles.length}}" class="swiper-box" interval="10000" autoplay="true" easing-function="easeInOutCubic" circular="true" current="{{swiperCurrent}}" bindchange="swiperChange">
          <swiper-item wx:for="{{swiperArticles}}" wx:key="id" class="swiper-item" id="{{item.id}}" index="{{index}}" data-type="{{item.type}}" data-appid="{{item.appid}}" data-url="{{item.url}}" data-path="{{item.path}}" data-jumptype="{{item.jumptype}}" data-unassociated="{{item.unassociated}}" bindtap="toDetail">
            <image mode="aspectFill" src="{{item.image}}" class="swiper-image {{swiperIndex == index ? 'active' : ''}}" />
            <view class="swiper-desc">
              <text>{{item.title}}</text>
            </view>
          </swiper-item>
        </swiper>
        <view class="dots-container">
          <block wx:for="{{swiperArticles}}" wx:key="id">
            <view class="dot{{index == swiperCurrent ? ' active' : ''}}" bind:tap="onDotTap" mark:index="{{index}}"></view>
          </block>
        </view>
      </view>

      <!-- 公告 -->
      <view class="notice" wx:if="{{billboardList.length>0}}">
        <image class="notice_img" src="/images/notice.png" mode="aspectFill" />
        <swiper vertical="true" autoplay='true' display-multiple-items='2' interval="5000" class="notice-swiper">
          <block wx:for="{{billboardList}}" wx:key="id">
            <swiper-item class="notice-swiper-item" bindtap="redictDetail" id="{{item.id}}" index="{{index}}">
              <text>{{item.title.rendered}}</text>
            </swiper-item>
          </block>
        </swiper>
      </view>

      <!-- 精选栏目 -->
      <view class="selected-nav" wx:if="{{topNav.length}}">
        <nice-column list="{{topNav}}" />
      </view>
    </view>

    <!-- 自定义广告 -->
    <custom-ad wx:if="{{banner.enable == 'yes'}}" info="{{banner}}" type="{{banner.adstyle}}" />

    <!-- 精选商品 -->
    <!-- #if MP -->
    <view class="hot-goods" wx:if="{{wechatShopSelectProducts.length}}">
      <hot-goods goods-list="{{wechatShopSelectProducts}}"  title="精选商品" list-type="4" from="home" />
    </view>      
    <!-- #endif -->

    <!-- 活跃用户 -->
    <hot-recommend type="recommendUser" title="活跃用户" />

    <!-- 媒体中心 -->
    <view class="media-center" wx:if="{{homepageMediaDisplay}}">
      <view class='common-subtitle'>
        <view class='common-subtitle-left'>媒体中心</view>
        <view class='common-subtitle-right'>MEDIA</view>
      </view>

      <view class="media-content">
        <!-- #if MP -->
        <view class="media-item video" bindtap="govideo">
          <text class="iconfont icon-media-video" />
          <view>视频</view>
        </view>
        <!-- #endif -->

        <!-- #if IOS -->
        <view class="media-item video" bindtap="govideo">
          <text class="iconfont icon-media-video" />
          <view>视频</view>
        </view>
        <!-- #endif -->
        <view class="media-item img-text" bindtap="goimage">
          <text class="iconfont icon-media-img-text" />
          <view>图文</view>
        </view>

        <view class="media-item album" bindtap="goalbum">
          <text class="iconfont icon-media-album" />
          <view>相册</view>
        </view>

        <view class="media-item audio" bindtap="goaudio">
          <text class="iconfont icon-media-audio" />
          <view>音频</view>
        </view>

        <view class="media-item file" bindtap="gofile">
          <text class="iconfont icon-media-file" />
          <view>文件</view>
        </view>

      </view>

    </view>

    <!-- 推荐功能 -->
    <view class='common-subtitle'>
      <view class='common-subtitle-left'>更多功能</view>
      <view class='common-subtitle-right'> </view>
    </view>
    <view class="official-account" wx:if="{{showAfficialAccount}}">
      <official-account bindload="officialSucc" binderror="officialFail"></official-account>
    </view>

    <view class="grid-box">

      <!-- 积分商城组件 -->
      <!-- #if MP -->
      <view wx:if="{{enableMinapperShop}}" bindtap="gopointsmall" hover-class="relatedNavigator">
        <view class="point">
          <view class="point-info">
            <view class="point-title">积分商城</view>
            <view class="point-des">积分兑换丰富好物</view>
          </view>
          <view class="point-img">
            <text class="iconfont icon-mall-point" />
          </view>
        </view>
      </view>
      <!-- #endif -->
      <!-- 视频号组件 -->
      <video-channels wx:if="{{enableChannels}}" />

      <!-- 付费专栏 -->
      <!-- #if MP -->
      <view   open-type="redirect" bindtap="gopaytopic" hover-class="relatedNavigator">
        <view class="subscribe-card">
          <view class="subscribe-wrap ">
            <view>
              <view class="subscribe-tip">付费专栏</view>
              <view class="subscribe-info">
                <view class="subscribe-btn">查看专栏</view>
              </view>
            </view>
            <view class="point-img">
              <text class="iconfont icon-note" />
            </view>
          </view>
        </view>
      </view>
      <!-- #endif -->

      <!-- 公众号关注组件 -->
      <block  >
        <custom-follow />
      </block>

    </view>


       <!--  推荐分类 -->
       
    <hot-recommend type="recommendCate" title="推荐分类" />

    <!-- 最新文章 -->
    <view class='common-subtitle' style="margin-bottom: -24rpx;">
      <view class='common-subtitle-left'>最新文章</view>
      <view class='common-subtitle-right'> </view>
    </view>
  </view>
  <!-- 以上内容只在首页显示 -->

  <!-- 订阅 -->
  <view class="subscribe" wx:if="{{currentColumn !=0 && currentColumn !=1}}">
    <text>已订阅了 {{columns[currentColumn].categorySubscribeCount}} 篇</text>
    <!-- #if MP -->
    <view data-subid="{{columns[currentColumn].subscribemessagesid}}" data-id="{{columns[currentColumn].id}}" catchtap="postsub">订阅</view>
    <!-- #endif -->
  </view>



  <!-- 上拉加载动画、到底了和版权信息 -->
  <view style="display:{{isArticlesList?'block':'none'}}">

    <view class="list-container">
      <view wx:if="{{currentColumn == 1}}" style="width: 100%;height: 24rpx;"></view>
      <article-item list="{{articlesList}}" type="{{articleStyle}}" system="{{system}}" show-action="{{memberUserInfo.member == '00'}}" bind:submitPage="submitPage" bind:submitContent="submitContent" bind:deleteTopic="deleteTopic" bind:sendSubscribeMessage="sendSubscribeMessage" />

      <view style='display:{{isLoading?"block":"none"}}'>
        <template is="threepoint" />
      </view>
      <view class="no-more" style="display:{{isLastPage?'block':'none'}}">- 到底啦 -</view>

      <copyright />
    </view>
  </view>
</view>

<!-- 发布文章按钮 -->
<!-- <view wx:if="{{showAddbtn}}" class="addarticle" bindtap="addArticle">
  <text class="iconfont icon-release" />
</view> -->

<!-- 授权手机号 -->
<van-dialog show="{{ showPopPhone }}" title="提示" message="发布内容需要授权绑定手机号" showCancelButton="{{ true }}" confirmButtonText="确认绑定" confirm-button-color="#2f80ed" confirm-button-open-type="getPhoneNumber" bind:getphonenumber="getPhoneNumber" />

<template is="login-popup" data="{{show:isLoginPopup,userInfo:userInfo,hasWechatInstall:hasWechatInstall}}"></template>

<!-- 扩展链接和邀请好友组件 -->
<extend-link link-id="0" path="pages/index/index" page-type="index" source-url="{{sourceUrl}}" show-add-article="{{showAddbtn}}" bind:onAddArticle="addArticle" />

<!-- 弹窗广告 -->
<popup-ad type="index" />

<!-- 确认弹窗 -->
<z-dialog />

<!-- 登录弹窗 -->
<z-login />

<!-- 隐私同意弹窗 -->
<privacy />