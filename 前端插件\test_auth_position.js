// 授权弹框位置测试脚本
// 在小程序控制台中运行此脚本来测试授权功能

console.log('=== 授权弹框位置修复测试 ===');

// 1. 检查当前页面
const pages = getCurrentPages();
const currentPage = pages[pages.length - 1];
console.log('当前页面:', currentPage.route);

if (currentPage.route === 'pages/help/list/list') {
  console.log('✅ 在邻里互助页面，可以进行测试');
  
  // 2. 检查页面数据
  const pageData = currentPage.data;
  console.log('登录弹窗状态:', pageData.isLoginPopup);
  console.log('授权状态:', pageData.isAuthenticating);
  console.log('待执行操作:', pageData.pendingAction);
  
  // 3. 检查必要的方法是否存在
  const methods = ['agreeGetUser', 'closeLoginPopup', 'goToPublish'];
  methods.forEach(method => {
    if (typeof currentPage[method] === 'function') {
      console.log(`✅ ${method} 方法存在`);
    } else {
      console.log(`❌ ${method} 方法不存在`);
    }
  });
  
  // 4. 模拟测试登录流程（仅检查，不实际执行）
  console.log('\n=== 测试建议 ===');
  console.log('1. 点击发布按钮测试登录弹窗');
  console.log('2. 点击立即登录测试授权弹框位置');
  console.log('3. 检查授权弹框是否在当前页面显示');
  console.log('4. 测试授权成功后的跳转功能');
  
  // 5. 提供手动测试函数
  console.log('\n=== 手动测试函数 ===');
  console.log('测试显示登录弹窗: currentPage.setData({isLoginPopup: true, pendingAction: "publish"})');
  console.log('测试关闭登录弹窗: currentPage.closeLoginPopup()');
  console.log('检查页面状态: console.log(currentPage.data)');
  
} else {
  console.log('❌ 请在邻里互助页面运行此测试');
  console.log('当前页面:', currentPage.route);
  console.log('请导航到: pages/help/list/list');
}

// 6. 检查微信API可用性
if (typeof wx !== 'undefined') {
  console.log('\n=== 微信API检查 ===');
  
  // 检查getUserProfile是否可用
  if (typeof wx.getUserProfile === 'function') {
    console.log('✅ wx.getUserProfile 可用');
  } else {
    console.log('❌ wx.getUserProfile 不可用');
  }
  
  // 检查login是否可用
  if (typeof wx.login === 'function') {
    console.log('✅ wx.login 可用');
  } else {
    console.log('❌ wx.login 不可用');
  }
  
  // 检查本地存储
  try {
    const wxLoginInfo = wx.getStorageSync('wxLoginInfo');
    console.log('本地登录信息:', wxLoginInfo ? '存在' : '不存在');
    
    const userSession = wx.getStorageSync('userSession');
    console.log('用户会话信息:', userSession ? '存在' : '不存在');
  } catch (e) {
    console.log('检查本地存储失败:', e.message);
  }
} else {
  console.log('❌ 微信API不可用，请在小程序环境中运行');
}

console.log('\n=== 测试完成 ===');
console.log('请按照测试指南进行手动测试');
