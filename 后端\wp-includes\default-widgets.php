<?php
/**
 * Widget API: Default core widgets
 *
 * @package WordPress
 * @subpackage Widgets
 * @since 2.8.0
 */

// Don't load directly.
if ( ! defined( 'ABSPATH' ) ) {
	die( '-1' );
}

/** WP_Widget_Pages class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-pages.php';

/** WP_Widget_Links class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-links.php';

/** WP_Widget_Search class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-search.php';

/** WP_Widget_Archives class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-archives.php';

/** WP_Widget_Media class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-media.php';

/** WP_Widget_Media_Audio class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-media-audio.php';

/** WP_Widget_Media_Image class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-media-image.php';

/** WP_Widget_Media_Video class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-media-video.php';

/** WP_Widget_Media_Gallery class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-media-gallery.php';

/** WP_Widget_Meta class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-meta.php';

/** WP_Widget_Calendar class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-calendar.php';

/** WP_Widget_Text class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-text.php';

/** WP_Widget_Categories class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-categories.php';

/** WP_Widget_Recent_Posts class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-recent-posts.php';

/** WP_Widget_Recent_Comments class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-recent-comments.php';

/** WP_Widget_RSS class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-rss.php';

/** WP_Widget_Tag_Cloud class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-tag-cloud.php';

/** WP_Nav_Menu_Widget class */
require_once ABSPATH . WPINC . '/widgets/class-wp-nav-menu-widget.php';

/** WP_Widget_Custom_HTML class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-custom-html.php';

/** WP_Widget_Block class */
require_once ABSPATH . WPINC . '/widgets/class-wp-widget-block.php';
