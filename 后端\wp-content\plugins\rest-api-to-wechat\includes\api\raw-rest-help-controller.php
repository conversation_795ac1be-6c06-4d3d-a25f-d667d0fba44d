<?php

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class RAW_REST_Help_Controller extends WP_REST_Controller {

    public function __construct() {
        $this->namespace     = 'minapper/v1';
        $this->resource_name = 'help';
    }

    public function register_routes() {
        // 获取互助列表
        register_rest_route( $this->namespace, '/' . $this->resource_name . '/list', array(
            array(
                'methods'   => 'GET',
                'callback'  => array( $this, 'get_help_list' ),
                'permission_callback' => array( $this, 'get_item_permissions_check' ),
                'args' => array(
                    'page' => array(
                        'default' => 1,
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'per_page' => array(
                        'default' => 10,
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'urgency' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return in_array( $param, array('urgent', 'normal', 'low') );
                        }
                    ),
                    'help_type' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_string( $param );
                        }
                    ),
                    'keyword' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_string( $param );
                        }
                    )
                )
            ),
        ) );

        // 发布互助需求
        register_rest_route( $this->namespace, '/' . $this->resource_name . '/publish', array(
            array(
                'methods'   => 'POST',
                'callback'  => array( $this, 'publish_help_request' ),
                'permission_callback' => array( $this, 'create_item_permissions_check' ),
                'args' => array(
                    'title' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return !empty( $param );
                        }
                    ),
                    'content' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return !empty( $param );
                        }
                    ),
                    'urgency' => array(
                        'default' => 'normal',
                        'validate_callback' => function( $param, $request, $key ) {
                            return in_array( $param, array('urgent', 'normal', 'low') );
                        }
                    ),
                    'help_type' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return !empty( $param );
                        }
                    ),
                    'points_reward' => array(
                        'default' => 0,
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param ) && $param >= 0;
                        }
                    ),
                    'location_info' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_string( $param );
                        }
                    ),
                    'contact_info' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_string( $param );
                        }
                    ),
                    'userid' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'sessionid' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return !empty( $param );
                        }
                    )
                )
            ),
        ) );

        // 获取互助详情
        register_rest_route( $this->namespace, '/' . $this->resource_name . '/(?P<id>\d+)', array(
            array(
                'methods'   => 'GET',
                'callback'  => array( $this, 'get_help_detail' ),
                'permission_callback' => array( $this, 'get_item_permissions_check' ),
                'args' => array(
                    'id' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    )
                )
            ),
        ) );

        // 提交响应
        register_rest_route( $this->namespace, '/' . $this->resource_name . '/(?P<id>\d+)/respond', array(
            array(
                'methods'   => 'POST',
                'callback'  => array( $this, 'submit_response' ),
                'permission_callback' => array( $this, 'create_item_permissions_check' ),
                'args' => array(
                    'id' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'content' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return !empty( trim( $param ) );
                        }
                    ),
                    'estimated_time' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_string( $param );
                        }
                    ),
                    'userid' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'sessionid' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return !empty( $param );
                        }
                    )
                )
            ),
        ) );

        // 选择帮助者
        register_rest_route( $this->namespace, '/' . $this->resource_name . '/(?P<id>\d+)/select', array(
            array(
                'methods'   => 'POST',
                'callback'  => array( $this, 'select_helper' ),
                'permission_callback' => array( $this, 'create_item_permissions_check' ),
                'args' => array(
                    'id' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'response_id' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'userid' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'sessionid' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return !empty( $param );
                        }
                    )
                )
            ),
        ) );

        // 完成确认
        register_rest_route( $this->namespace, '/' . $this->resource_name . '/(?P<id>\d+)/complete', array(
            array(
                'methods'   => 'POST',
                'callback'  => array( $this, 'complete_help_request' ),
                'permission_callback' => array( $this, 'create_item_permissions_check' ),
                'args' => array(
                    'userid' => array('required' => true),
                    'sessionid' => array('required' => true)
                )
            ),
        ) );

        // 提交评价
        register_rest_route( $this->namespace, '/' . $this->resource_name . '/(?P<id>\d+)/rate', array(
            array(
                'methods'   => 'POST',
                'callback'  => array( $this, 'submit_rating' ),
                'permission_callback' => array( $this, 'create_item_permissions_check' ),
                'args' => array(
                    'rating' => array('required' => true),
                    'comment' => array(),
                    'userid' => array('required' => true),
                    'sessionid' => array('required' => true)
                )
            ),
        ) );

        // 获取用户评价
        register_rest_route( $this->namespace, '/ratings/(?P<user_id>\d+)', array(
            array(
                'methods'   => 'GET',
                'callback'  => array( $this, 'get_user_ratings' ),
                'permission_callback' => array( $this, 'get_item_permissions_check' )
            ),
        ) );

        // 取消需求
        register_rest_route( $this->namespace, '/' . $this->resource_name . '/(?P<id>\d+)/cancel', array(
            array(
                'methods'   => 'POST',
                'callback'  => array( $this, 'cancel_request' ),
                'permission_callback' => array( $this, 'create_item_permissions_check' ),
                'args' => array(
                    'id' => array(
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'userid' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return is_numeric( $param );
                        }
                    ),
                    'sessionid' => array(
                        'required' => true,
                        'validate_callback' => function( $param, $request, $key ) {
                            return !empty( $param );
                        }
                    )
                )
            ),
        ) );
    }

    public function get_item_permissions_check( $request ) {
        return true; // 暂时允许所有访问，后续可以添加权限检查
    }

    public function create_item_permissions_check( $request ) {
        // 检查用户是否登录
        $userid = $request->get_param('userid');
        $sessionid = $request->get_param('sessionid');
        
        if ( empty( $userid ) || empty( $sessionid ) ) {
            return new WP_Error( 'rest_forbidden', '需要用户登录', array( 'status' => 401 ) );
        }
        
        // 验证session（这里可以复用现有的session验证逻辑）
        return true;
    }

    public function get_help_list( $request ) {
        global $wpdb;

        // 检查表是否存在
        $table_name = $wpdb->prefix . 'minapper_help_requests';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if ( !$table_exists ) {
            // 返回空列表而不是错误，这样前端可以正常显示
            return rest_ensure_response( array(
                'list' => array(),
                'total' => 0,
                'message' => '邻里互助表不存在，请先执行数据库创建脚本'
            ) );
        }

        $page = $request->get_param('page');
        $per_page = $request->get_param('per_page');
        $urgency = $request->get_param('urgency');
        $help_type = $request->get_param('help_type');
        $keyword = $request->get_param('keyword');

        $offset = ( $page - 1 ) * $per_page;
        
        // 构建查询条件
        $where_conditions = array();
        $where_params = array();
        
        if ( !empty( $urgency ) ) {
            $where_conditions[] = "hr.urgency = %s";
            $where_params[] = $urgency;
        }
        
        if ( !empty( $help_type ) ) {
            $where_conditions[] = "hr.help_type = %s";
            $where_params[] = $help_type;
        }
        
        if ( !empty( $keyword ) ) {
            $where_conditions[] = "(p.post_title LIKE %s OR p.post_content LIKE %s)";
            $where_params[] = '%' . $wpdb->esc_like( $keyword ) . '%';
            $where_params[] = '%' . $wpdb->esc_like( $keyword ) . '%';
        }
        
        $where_clause = '';
        if ( !empty( $where_conditions ) ) {
            $where_clause = 'AND ' . implode( ' AND ', $where_conditions );
        }
        
        // 查询互助列表
        $sql = "
            SELECT 
                hr.id,
                hr.topic_id,
                hr.user_id,
                hr.urgency,
                hr.help_type,
                hr.points_reward,
                hr.location_info,
                hr.status,
                hr.created_at,
                p.post_title,
                p.post_content,
                u.display_name as user_name
            FROM {$wpdb->prefix}minapper_help_requests hr
            LEFT JOIN {$wpdb->posts} p ON hr.topic_id = p.ID
            LEFT JOIN {$wpdb->users} u ON hr.user_id = u.ID
            WHERE hr.status != 'cancelled' {$where_clause}
            ORDER BY 
                CASE hr.urgency 
                    WHEN 'urgent' THEN 1 
                    WHEN 'normal' THEN 2 
                    WHEN 'low' THEN 3 
                END,
                hr.created_at DESC
            LIMIT %d OFFSET %d
        ";
        
        $where_params[] = $per_page;
        $where_params[] = $offset;
        
        $results = $wpdb->get_results( $wpdb->prepare( $sql, $where_params ) );
        
        // 获取总数
        $count_sql = "
            SELECT COUNT(*) 
            FROM {$wpdb->prefix}minapper_help_requests hr
            LEFT JOIN {$wpdb->posts} p ON hr.topic_id = p.ID
            WHERE hr.status != 'cancelled' {$where_clause}
        ";
        
        $total = $wpdb->get_var( $wpdb->prepare( $count_sql, array_slice( $where_params, 0, -2 ) ) );
        
        $response_data = array(
            'list' => $results,
            'total' => intval( $total ),
            'page' => $page,
            'per_page' => $per_page,
            'total_pages' => ceil( $total / $per_page )
        );
        
        return rest_ensure_response( $response_data );
    }

    public function publish_help_request( $request ) {
        global $wpdb;

        // 检查表是否存在
        $table_name = $wpdb->prefix . 'minapper_help_requests';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if ( !$table_exists ) {
            return new WP_Error( 'table_not_exists', '邻里互助表不存在，请先创建数据库表', array( 'status' => 500 ) );
        }

        $title = sanitize_text_field( $request->get_param('title') );
        $content = wp_kses_post( $request->get_param('content') );
        $urgency = $request->get_param('urgency');
        $help_type = sanitize_text_field( $request->get_param('help_type') );
        $points_reward = intval( $request->get_param('points_reward') );
        $location_info = sanitize_text_field( $request->get_param('location_info') );
        $contact_info = sanitize_text_field( $request->get_param('contact_info') );
        $userid = intval( $request->get_param('userid') );
        
        // 首先创建bbPress topic
        if ( function_exists( 'bbp_insert_topic' ) ) {
            // 获取邻里互助论坛ID（需要先创建）
            $forum_id = $this->get_or_create_help_forum();
            
            $topic_data = array(
                'post_parent'    => $forum_id,
                'post_status'    => 'publish',
                'post_type'      => 'topic',
                'post_author'    => $userid,
                'post_title'     => $title,
                'post_content'   => $content,
            );
            
            $topic_id = wp_insert_post( $topic_data );
            
            if ( is_wp_error( $topic_id ) ) {
                return new WP_Error( 'topic_creation_failed', '创建话题失败', array( 'status' => 500 ) );
            }
            
            // 插入互助需求记录
            $result = $wpdb->insert(
                $wpdb->prefix . 'minapper_help_requests',
                array(
                    'topic_id' => $topic_id,
                    'user_id' => $userid,
                    'urgency' => $urgency,
                    'help_type' => $help_type,
                    'points_reward' => $points_reward,
                    'location_info' => $location_info,
                    'contact_info' => $contact_info,
                    'status' => 'pending'
                ),
                array( '%d', '%d', '%s', '%s', '%d', '%s', '%s', '%s' )
            );
            
            if ( $result === false ) {
                // 如果插入失败，删除已创建的topic
                wp_delete_post( $topic_id, true );
                return new WP_Error( 'request_creation_failed', '创建互助需求失败', array( 'status' => 500 ) );
            }
            
            $help_request_id = $wpdb->insert_id;
            
            return rest_ensure_response( array(
                'success' => true,
                'id' => $help_request_id,
                'topic_id' => $topic_id,
                'message' => '互助需求发布成功'
            ) );
        } else {
            return new WP_Error( 'bbpress_not_available', 'bbPress插件未激活', array( 'status' => 500 ) );
        }
    }

    public function get_help_detail( $request ) {
        global $wpdb;

        // 检查表是否存在
        $table_name = $wpdb->prefix . 'minapper_help_requests';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if ( !$table_exists ) {
            return new WP_Error( 'table_not_exists', '邻里互助表不存在，请先创建数据库表', array( 'status' => 500 ) );
        }

        $id = intval( $request->get_param('id') );

        // 获取互助需求详情
        $sql = "
            SELECT 
                hr.*,
                p.post_title,
                p.post_content,
                u.display_name as user_name
            FROM {$wpdb->prefix}minapper_help_requests hr
            LEFT JOIN {$wpdb->posts} p ON hr.topic_id = p.ID
            LEFT JOIN {$wpdb->users} u ON hr.user_id = u.ID
            WHERE hr.id = %d
        ";
        
        $help_request = $wpdb->get_row( $wpdb->prepare( $sql, $id ) );
        
        if ( !$help_request ) {
            return new WP_Error( 'help_not_found', '互助需求不存在', array( 'status' => 404 ) );
        }
        
        // 获取响应列表
        $responses_sql = "
            SELECT 
                hr.*,
                u.display_name as user_name
            FROM {$wpdb->prefix}minapper_help_responses hr
            LEFT JOIN {$wpdb->users} u ON hr.user_id = u.ID
            WHERE hr.request_id = %d
            ORDER BY hr.created_at ASC
        ";
        
        $responses = $wpdb->get_results( $wpdb->prepare( $responses_sql, $id ) );
        
        $response_data = array(
            'request' => $help_request,
            'responses' => $responses
        );
        
        return rest_ensure_response( $response_data );
    }

    private function get_or_create_help_forum() {
        // 检查是否存在邻里互助论坛，如果不存在则创建
        $forum = get_page_by_title( '邻里互助', OBJECT, 'forum' );
        
        if ( !$forum ) {
            $forum_data = array(
                'post_title'    => '邻里互助',
                'post_content'  => '邻里互助论坛，邻里之间相互帮助的温暖社区',
                'post_status'   => 'publish',
                'post_type'     => 'forum',
                'post_author'   => 1, // 管理员
            );
            
            $forum_id = wp_insert_post( $forum_data );
            return $forum_id;
        }
        
        return $forum->ID;
    }

    // 提交响应
    public function submit_response( $request ) {
        global $wpdb;

        // 检查表是否存在
        $requests_table = $wpdb->prefix . 'minapper_help_requests';
        $responses_table = $wpdb->prefix . 'minapper_help_responses';

        if ( $wpdb->get_var("SHOW TABLES LIKE '$requests_table'") != $requests_table ||
             $wpdb->get_var("SHOW TABLES LIKE '$responses_table'") != $responses_table ) {
            return new WP_Error( 'table_not_exists', '数据表不存在', array( 'status' => 500 ) );
        }

        $help_id = intval( $request->get_param('id') );
        $content = sanitize_textarea_field( $request->get_param('content') );
        $estimated_time = sanitize_text_field( $request->get_param('estimated_time') );
        $userid = intval( $request->get_param('userid') );
        $sessionid = sanitize_text_field( $request->get_param('sessionid') );

        // 验证用户会话
        if ( !$this->validate_user_session( $userid, $sessionid ) ) {
            return new WP_Error( 'invalid_session', '用户会话无效', array( 'status' => 401 ) );
        }

        // 检查互助需求是否存在且状态为pending
        $help_request = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$requests_table} WHERE id = %d AND status = 'pending'",
            $help_id
        ) );

        if ( !$help_request ) {
            return new WP_Error( 'help_not_found', '互助需求不存在或已关闭', array( 'status' => 404 ) );
        }

        // 检查是否是需求发布者（不能响应自己的需求）
        if ( $help_request->user_id == $userid ) {
            return new WP_Error( 'cannot_respond_own', '不能响应自己发布的需求', array( 'status' => 400 ) );
        }

        // 检查是否已经响应过
        $existing_response = $wpdb->get_var( $wpdb->prepare(
            "SELECT id FROM {$responses_table} WHERE request_id = %d AND user_id = %d",
            $help_id, $userid
        ) );

        if ( $existing_response ) {
            return new WP_Error( 'already_responded', '您已经响应过此需求', array( 'status' => 400 ) );
        }

        // 插入响应记录
        $result = $wpdb->insert(
            $responses_table,
            array(
                'request_id' => $help_id,
                'user_id' => $userid,
                'content' => $content,
                'estimated_time' => $estimated_time,
                'status' => 'pending'
            ),
            array( '%d', '%d', '%s', '%s', '%s' )
        );

        if ( $result === false ) {
            return new WP_Error( 'response_creation_failed', '提交响应失败', array( 'status' => 500 ) );
        }

        $response_id = $wpdb->insert_id;

        return array(
            'success' => true,
            'message' => '响应提交成功',
            'data' => array(
                'response_id' => $response_id
            )
        );
    }

    // 选择帮助者
    public function select_helper( $request ) {
        global $wpdb;

        // 检查表是否存在
        $requests_table = $wpdb->prefix . 'minapper_help_requests';
        $responses_table = $wpdb->prefix . 'minapper_help_responses';

        if ( $wpdb->get_var("SHOW TABLES LIKE '$requests_table'") != $requests_table ||
             $wpdb->get_var("SHOW TABLES LIKE '$responses_table'") != $responses_table ) {
            return new WP_Error( 'table_not_exists', '数据表不存在', array( 'status' => 500 ) );
        }

        $help_id = intval( $request->get_param('id') );
        $response_id = intval( $request->get_param('response_id') );
        $userid = intval( $request->get_param('userid') );
        $sessionid = sanitize_text_field( $request->get_param('sessionid') );

        // 验证用户会话
        if ( !$this->validate_user_session( $userid, $sessionid ) ) {
            return new WP_Error( 'invalid_session', '用户会话无效', array( 'status' => 401 ) );
        }

        // 检查互助需求是否存在且用户是发布者
        $help_request = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$requests_table} WHERE id = %d AND user_id = %d AND status = 'pending'",
            $help_id, $userid
        ) );

        if ( !$help_request ) {
            return new WP_Error( 'help_not_found', '互助需求不存在或您无权操作', array( 'status' => 404 ) );
        }

        // 检查响应是否存在
        $response = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$responses_table} WHERE id = %d AND request_id = %d AND status = 'pending'",
            $response_id, $help_id
        ) );

        if ( !$response ) {
            return new WP_Error( 'response_not_found', '响应不存在或已处理', array( 'status' => 404 ) );
        }

        // 开始事务处理
        $wpdb->query('START TRANSACTION');

        try {
            // 更新选中的响应状态
            $update_selected = $wpdb->update(
                $responses_table,
                array(
                    'status' => 'selected',
                    'selected_at' => current_time('mysql')
                ),
                array( 'id' => $response_id ),
                array( '%s', '%s' ),
                array( '%d' )
            );

            if ( $update_selected === false ) {
                throw new Exception('更新响应状态失败');
            }

            // 更新其他响应为rejected
            $update_others = $wpdb->update(
                $responses_table,
                array( 'status' => 'rejected' ),
                array(
                    'request_id' => $help_id,
                    'status' => 'pending'
                ),
                array( '%s' ),
                array( '%d', '%s' )
            );

            // 更新需求状态为进行中
            $update_request = $wpdb->update(
                $requests_table,
                array( 'status' => 'in_progress' ),
                array( 'id' => $help_id ),
                array( '%s' ),
                array( '%d' )
            );

            if ( $update_request === false ) {
                throw new Exception('更新需求状态失败');
            }

            $wpdb->query('COMMIT');

            return array(
                'success' => true,
                'message' => '选择帮助者成功',
                'data' => array(
                    'helper_id' => $response->user_id
                )
            );

        } catch ( Exception $e ) {
            $wpdb->query('ROLLBACK');
            return new WP_Error( 'selection_failed', '选择帮助者失败: ' . $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    // 取消需求
    public function cancel_request( $request ) {
        global $wpdb;

        // 检查表是否存在
        $requests_table = $wpdb->prefix . 'minapper_help_requests';
        $responses_table = $wpdb->prefix . 'minapper_help_responses';

        if ( $wpdb->get_var("SHOW TABLES LIKE '$requests_table'") != $requests_table ) {
            return new WP_Error( 'table_not_exists', '数据表不存在', array( 'status' => 500 ) );
        }

        $help_id = intval( $request->get_param('id') );
        $userid = intval( $request->get_param('userid') );
        $sessionid = sanitize_text_field( $request->get_param('sessionid') );

        // 验证用户会话
        if ( !$this->validate_user_session( $userid, $sessionid ) ) {
            return new WP_Error( 'invalid_session', '用户会话无效', array( 'status' => 401 ) );
        }

        // 检查互助需求是否存在且用户是发布者
        $help_request = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$requests_table} WHERE id = %d AND user_id = %d",
            $help_id, $userid
        ) );

        if ( !$help_request ) {
            return new WP_Error( 'help_not_found', '互助需求不存在或您无权操作', array( 'status' => 404 ) );
        }

        // 检查状态是否可以取消
        if ( $help_request->status == 'cancelled' ) {
            return new WP_Error( 'already_cancelled', '需求已经取消', array( 'status' => 400 ) );
        }

        if ( $help_request->status == 'completed' ) {
            return new WP_Error( 'cannot_cancel_completed', '已完成的需求不能取消', array( 'status' => 400 ) );
        }

        // 开始事务处理
        $wpdb->query('START TRANSACTION');

        try {
            // 更新需求状态为已取消
            $update_request = $wpdb->update(
                $requests_table,
                array( 'status' => 'cancelled' ),
                array( 'id' => $help_id ),
                array( '%s' ),
                array( '%d' )
            );

            if ( $update_request === false ) {
                throw new Exception('更新需求状态失败');
            }

            // 如果有响应表，更新所有相关响应为rejected
            if ( $wpdb->get_var("SHOW TABLES LIKE '$responses_table'") == $responses_table ) {
                $wpdb->update(
                    $responses_table,
                    array( 'status' => 'rejected' ),
                    array(
                        'request_id' => $help_id,
                        'status' => 'pending'
                    ),
                    array( '%s' ),
                    array( '%d', '%s' )
                );
            }

            $wpdb->query('COMMIT');

            return array(
                'success' => true,
                'message' => '需求已取消',
                'data' => array(
                    'request_id' => $help_id
                )
            );

        } catch ( Exception $e ) {
            $wpdb->query('ROLLBACK');
            return new WP_Error( 'cancellation_failed', '取消需求失败: ' . $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    // 验证用户会话的辅助方法
    private function validate_user_session( $userid, $sessionid ) {
        // 这里应该实现具体的会话验证逻辑
        // 可以检查数据库中的会话记录或使用WordPress的用户验证

        // 简单验证：检查用户是否存在
        $user = get_user_by( 'ID', $userid );
        if ( !$user ) {
            return false;
        }

        // 这里可以添加更复杂的会话验证逻辑
        // 例如检查sessionid是否有效、是否过期等

        return !empty( $sessionid );
    }

    // 完成确认
    public function complete_help_request( $request ) {
        global $wpdb;

        // 检查表是否存在
        $requests_table = $wpdb->prefix . 'minapper_help_requests';
        $responses_table = $wpdb->prefix . 'minapper_help_responses';

        if ( $wpdb->get_var("SHOW TABLES LIKE '$requests_table'") != $requests_table ) {
            return new WP_Error( 'table_not_exists', '数据表不存在', array( 'status' => 500 ) );
        }

        $help_id = intval( $request->get_param('id') );
        $userid = intval( $request->get_param('userid') );
        $sessionid = sanitize_text_field( $request->get_param('sessionid') );

        // 验证用户会话
        if ( !$this->validate_user_session( $userid, $sessionid ) ) {
            return new WP_Error( 'invalid_session', '用户会话无效', array( 'status' => 401 ) );
        }

        // 检查互助需求是否存在
        $help_request = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$requests_table} WHERE id = %d",
            $help_id
        ) );

        if ( !$help_request ) {
            return new WP_Error( 'help_not_found', '互助需求不存在', array( 'status' => 404 ) );
        }

        // 检查用户权限（发布者或选中的帮助者可以确认完成）
        $is_requester = $help_request->user_id == $userid;
        $selected_response = null;

        if ( $wpdb->get_var("SHOW TABLES LIKE '$responses_table'") == $responses_table ) {
            $selected_response = $wpdb->get_row( $wpdb->prepare(
                "SELECT * FROM {$responses_table} WHERE request_id = %d AND status = 'selected'",
                $help_id
            ) );
        }

        $is_selected_helper = $selected_response && $selected_response->user_id == $userid;

        if ( !$is_requester && !$is_selected_helper ) {
            return new WP_Error( 'no_permission', '您无权操作此需求', array( 'status' => 403 ) );
        }

        // 检查状态是否可以完成
        if ( $help_request->status == 'completed' ) {
            return new WP_Error( 'already_completed', '需求已经完成', array( 'status' => 400 ) );
        }

        if ( $help_request->status != 'in_progress' ) {
            return new WP_Error( 'invalid_status', '只有进行中的需求才能确认完成', array( 'status' => 400 ) );
        }

        // 开始事务处理
        $wpdb->query('START TRANSACTION');

        try {
            // 更新需求状态为已完成
            $update_request = $wpdb->update(
                $requests_table,
                array(
                    'status' => 'completed',
                    'updated_at' => current_time('mysql')
                ),
                array( 'id' => $help_id ),
                array( '%s', '%s' ),
                array( '%d' )
            );

            if ( $update_request === false ) {
                throw new Exception('更新需求状态失败');
            }

            // 处理积分转账（如果有积分奖励）
            if ( $help_request->points_reward > 0 && $selected_response ) {
                $this->process_points_transfer( $help_request->user_id, $selected_response->user_id, $help_request->points_reward );
            }

            $wpdb->query('COMMIT');

            return array(
                'success' => true,
                'message' => '需求已确认完成',
                'data' => array(
                    'request_id' => $help_id,
                    'status' => 'completed',
                    'points_transferred' => $help_request->points_reward
                )
            );

        } catch ( Exception $e ) {
            $wpdb->query('ROLLBACK');
            return new WP_Error( 'completion_failed', '确认完成失败: ' . $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    // 处理积分转账
    private function process_points_transfer( $from_user_id, $to_user_id, $points ) {
        // 这里应该实现具体的积分转账逻辑
        // 可以调用现有的积分系统API或直接操作数据库

        // 简单实现：记录积分变动日志
        error_log("积分转账: 从用户{$from_user_id}转账{$points}积分给用户{$to_user_id}");

        // TODO: 实现具体的积分转账逻辑
        return true;
    }

    // 真正的积分转账实现
    private function process_points_transfer_real( $from_user_id, $to_user_id, $points, $help_id ) {
        try {
            // 1. 从发布者扣除积分
            $deduct_data = array(
                'userid' => $from_user_id,
                'itemid' => $help_id,
                'itemname' => '互助需求积分支付',
                'integraltype' => 'help_payment',
                'integral' => $points,
                'flag' => 'minus'
            );

            $deduct_result = RAW_Util::inserIntegral($deduct_data);
            if ( $deduct_result != 1 ) {
                throw new Exception('扣除发布者积分失败');
            }

            // 2. 给帮助者增加积分
            $add_data = array(
                'userid' => $to_user_id,
                'itemid' => $help_id,
                'itemname' => '互助完成积分奖励',
                'integraltype' => 'help_reward',
                'integral' => $points,
                'flag' => 'add'
            );

            $add_result = RAW_Util::inserIntegral($add_data);
            if ( $add_result != 1 ) {
                throw new Exception('增加帮助者积分失败');
            }

            error_log("积分转账成功: 从用户{$from_user_id}转账{$points}积分给用户{$to_user_id}");
            return $points;

        } catch ( Exception $e ) {
            error_log('积分转账失败: ' . $e->getMessage());
            throw $e;
        }
    }

    // 更新用户完成统计
    private function update_user_completion_stats( $user_id, $rating_type ) {
        global $wpdb;

        try {
            // 根据评价类型确定统计字段
            if ( $rating_type == 'requester_to_helper' ) {
                // 帮助者完成了一次互助
                $meta_key = 'help_completed_count';
            } else {
                // 发布者完成了一次需求
                $meta_key = 'help_requested_count';
            }

            // 获取当前统计数
            $current_count = get_user_meta( $user_id, $meta_key, true );
            $current_count = intval( $current_count );

            // 增加计数
            $new_count = $current_count + 1;
            update_user_meta( $user_id, $meta_key, $new_count );

            error_log("用户{$user_id}的{$meta_key}更新为{$new_count}");
            return true;

        } catch ( Exception $e ) {
            error_log('更新用户完成统计失败: ' . $e->getMessage());
            throw $e;
        }
    }

    // 提交评价
    public function submit_rating( $request ) {
        global $wpdb;

        // 检查表是否存在
        $requests_table = $wpdb->prefix . 'minapper_help_requests';
        $responses_table = $wpdb->prefix . 'minapper_help_responses';
        $ratings_table = $wpdb->prefix . 'minapper_help_ratings';

        if ( $wpdb->get_var("SHOW TABLES LIKE '$requests_table'") != $requests_table ) {
            return new WP_Error( 'table_not_exists', '需求表不存在', array( 'status' => 500 ) );
        }

        if ( $wpdb->get_var("SHOW TABLES LIKE '$responses_table'") != $responses_table ) {
            return new WP_Error( 'responses_table_not_exists', '响应表不存在', array( 'status' => 500 ) );
        }

        if ( $wpdb->get_var("SHOW TABLES LIKE '$ratings_table'") != $ratings_table ) {
            return new WP_Error( 'ratings_table_not_exists', '评价表不存在', array( 'status' => 500 ) );
        }

        $help_id = intval( $request->get_param('id') );
        $rating = intval( $request->get_param('rating') );
        $comment = sanitize_textarea_field( $request->get_param('comment') );
        $userid = intval( $request->get_param('userid') );
        $sessionid = sanitize_text_field( $request->get_param('sessionid') );

        // 验证评分范围
        if ( $rating < 1 || $rating > 5 ) {
            return new WP_Error( 'invalid_rating', '评分必须在1-5之间', array( 'status' => 400 ) );
        }

        // 验证用户会话
        if ( !$this->validate_user_session( $userid, $sessionid ) ) {
            return new WP_Error( 'invalid_session', '用户会话无效', array( 'status' => 401 ) );
        }

        // 检查互助需求是否存在且已完成
        $help_request = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$requests_table} WHERE id = %d",
            $help_id
        ) );

        if ( !$help_request ) {
            return new WP_Error( 'help_not_found', '互助需求不存在', array( 'status' => 404 ) );
        }

        if ( $help_request->status != 'completed' ) {
            return new WP_Error( 'not_completed', '只有已完成的需求才能评价', array( 'status' => 400 ) );
        }

        // 确定评价类型和被评价者
        $is_requester = $help_request->user_id == $userid;
        $selected_response = null;

        if ( $wpdb->get_var("SHOW TABLES LIKE '$responses_table'") == $responses_table ) {
            $selected_response = $wpdb->get_row( $wpdb->prepare(
                "SELECT * FROM {$responses_table} WHERE request_id = %d AND status = 'selected'",
                $help_id
            ) );
        }

        if ( !$selected_response ) {
            return new WP_Error( 'no_selected_helper', '没有找到选中的帮助者', array( 'status' => 400 ) );
        }

        $is_selected_helper = $selected_response->user_id == $userid;

        if ( !$is_requester && !$is_selected_helper ) {
            return new WP_Error( 'no_permission', '您无权评价此需求', array( 'status' => 403 ) );
        }

        // 确定评价类型和被评价者ID
        if ( $is_requester ) {
            $rating_type = 'requester_to_helper';
            $rated_id = $selected_response->user_id;
        } else {
            $rating_type = 'helper_to_requester';
            $rated_id = $help_request->user_id;
        }

        // 检查是否已经评价过
        $existing_rating = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$ratings_table} WHERE request_id = %d AND rater_id = %d AND rating_type = %s",
            $help_id, $userid, $rating_type
        ) );

        if ( $existing_rating ) {
            return new WP_Error( 'already_rated', '您已经评价过了', array( 'status' => 400 ) );
        }

        // 插入评价记录
        $insert_result = $wpdb->insert(
            $ratings_table,
            array(
                'request_id' => $help_id,
                'rater_id' => $userid,
                'rated_id' => $rated_id,
                'rating' => $rating,
                'comment' => $comment,
                'type' => $rating_type
            ),
            array( '%d', '%d', '%d', '%d', '%s', '%s' )
        );

        if ( $insert_result === false ) {
            // 记录详细错误信息
            error_log('评价插入失败: ' . $wpdb->last_error);
            error_log('插入数据: ' . json_encode(array(
                'request_id' => $help_id,
                'rater_id' => $userid,
                'rated_id' => $rated_id,
                'rating' => $rating,
                'comment' => $comment,
                'rating_type' => $rating_type
            )));

            return new WP_Error( 'rating_failed', '评价提交失败: ' . $wpdb->last_error, array( 'status' => 500 ) );
        }

        // 评价插入成功后，处理积分转账和统计更新
        $rating_id = $wpdb->insert_id;
        $points_transferred = 0;
        $completion_updated = false;

        try {
            // 开始事务处理
            $wpdb->query('START TRANSACTION');

            // 1. 处理积分转账（如果有积分奖励）
            if ( $help_request->points_reward > 0 ) {
                $points_transferred = $this->process_points_transfer_real(
                    $help_request->user_id,
                    $rated_id,
                    $help_request->points_reward,
                    $help_id
                );
            }

            // 2. 更新用户完成统计
            $completion_updated = $this->update_user_completion_stats( $rated_id, $rating_type );

            $wpdb->query('COMMIT');

        } catch ( Exception $e ) {
            $wpdb->query('ROLLBACK');
            error_log('评价后处理失败: ' . $e->getMessage());
            // 即使后处理失败，评价记录已经插入成功，所以不返回错误
        }

        return array(
            'success' => true,
            'message' => '评价提交成功',
            'data' => array(
                'rating_id' => $rating_id,
                'request_id' => $help_id,
                'rating' => $rating,
                'comment' => $comment,
                'type' => $rating_type,
                'points_transferred' => $points_transferred,
                'completion_updated' => $completion_updated
            )
        );
    }

    // 获取用户评价
    public function get_user_ratings( $request ) {
        global $wpdb;

        $ratings_table = $wpdb->prefix . 'minapper_help_ratings';

        if ( $wpdb->get_var("SHOW TABLES LIKE '$ratings_table'") != $ratings_table ) {
            return new WP_Error( 'ratings_table_not_exists', '评价表不存在', array( 'status' => 500 ) );
        }

        $user_id = intval( $request->get_param('user_id') );

        if ( !$user_id ) {
            return new WP_Error( 'invalid_user_id', '用户ID无效', array( 'status' => 400 ) );
        }

        // 获取用户收到的评价
        $received_ratings = $wpdb->get_results( $wpdb->prepare(
            "SELECT r.*, req.title as request_title, u.display_name as rater_name
             FROM {$ratings_table} r
             LEFT JOIN {$wpdb->prefix}minapper_help_requests req ON r.request_id = req.id
             LEFT JOIN {$wpdb->users} u ON r.rater_id = u.ID
             WHERE r.rated_id = %d
             ORDER BY r.created_at DESC",
            $user_id
        ) );

        // 计算平均评分
        $avg_rating = 0;
        $total_ratings = count($received_ratings);

        if ( $total_ratings > 0 ) {
            $sum_ratings = array_sum( array_column( $received_ratings, 'rating' ) );
            $avg_rating = round( $sum_ratings / $total_ratings, 1 );
        }

        // 按评价类型分组
        $ratings_by_type = array(
            'as_helper' => array(),
            'as_requester' => array()
        );

        foreach ( $received_ratings as $rating ) {
            if ( $rating->rating_type == 'requester_to_helper' ) {
                $ratings_by_type['as_helper'][] = $rating;
            } else {
                $ratings_by_type['as_requester'][] = $rating;
            }
        }

        return array(
            'success' => true,
            'data' => array(
                'user_id' => $user_id,
                'average_rating' => $avg_rating,
                'total_ratings' => $total_ratings,
                'ratings_by_type' => $ratings_by_type,
                'all_ratings' => $received_ratings
            )
        );
    }
}
