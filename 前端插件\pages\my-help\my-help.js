// pages/my-help/my-help.js
const app = getApp()
const config = require('../../utils/config.js')
const Auth = require('../../utils/auth.js')
const API = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    activeTab: 'published', // 当前激活的标签页
    userId: '', // 当前用户ID
    
    // 我发布的列表
    publishedList: [],
    publishedPage: 1,
    publishedHasMore: true,
    
    // 我接单的列表
    acceptedList: [],
    acceptedPage: 1,
    acceptedHasMore: true,
    
    // 筛选条件
    statusFilter: '', // 需求状态筛选
    responseStatusFilter: '', // 响应状态筛选
    typeFilter: '', // 类型筛选
    
    // 筛选选项
    statusOptions: [
      { text: '全部状态', value: '' },
      { text: '待响应', value: 'pending' },
      { text: '进行中', value: 'in_progress' },
      { text: '已完成', value: 'completed' },
      { text: '已取消', value: 'cancelled' }
    ],
    responseStatusOptions: [
      { text: '全部状态', value: '' },
      { text: '待选择', value: 'pending' },
      { text: '已选中', value: 'selected' },
      { text: '未选中', value: 'rejected' }
    ],
    typeOptions: [
      { text: '全部类型', value: '' },
      { text: '家居维修', value: '家居维修' },
      { text: '照顾宠物', value: '照顾宠物' },
      { text: '搬运物品', value: '搬运物品' },
      { text: '技能咨询', value: '技能咨询' },
      { text: '其他', value: '其他' }
    ],
    
    per_page: 10,
    hasMore: true,
    loading: false
  },

  onLoad: function (options) {
    // 获取用户ID
    const userId = options.userid || ''
    this.setData({ userId })
    
    // 检查登录状态
    Auth.checkSession(app, API, this, 'isLoginLater', util)
    
    // 加载数据
    this.loadCurrentTabData()
  },

  onShow: function () {
    // 页面显示时刷新当前标签页数据
    this.refreshCurrentTab()
  },

  onPullDownRefresh: function () {
    this.refreshCurrentTab()
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 标签页切换
  onTabChange: function (e) {
    const activeTab = e.detail.name
    this.setData({ 
      activeTab,
      hasMore: activeTab === 'published' ? this.data.publishedHasMore : this.data.acceptedHasMore
    })
    this.loadCurrentTabData()
  },

  // 加载当前标签页数据
  loadCurrentTabData: function () {
    if (this.data.activeTab === 'published') {
      this.loadPublishedList()
    } else {
      this.loadAcceptedList()
    }
  },

  // 刷新当前标签页
  refreshCurrentTab: function () {
    if (this.data.activeTab === 'published') {
      this.setData({
        publishedPage: 1,
        publishedList: [],
        publishedHasMore: true,
        hasMore: true
      })
      this.loadPublishedList()
    } else {
      this.setData({
        acceptedPage: 1,
        acceptedList: [],
        acceptedHasMore: true,
        hasMore: true
      })
      this.loadAcceptedList()
    }
  },

  // 加载我发布的互助列表
  loadPublishedList: function () {
    if (this.data.loading) return

    this.setData({ loading: true })

    const params = {
      page: this.data.publishedPage,
      per_page: this.data.per_page,
      user_id: this.data.userId // 筛选当前用户发布的
    }

    if (this.data.statusFilter) {
      params.status = this.data.statusFilter
    }
    if (this.data.typeFilter) {
      params.help_type = this.data.typeFilter
    }

    const domain = config.default.getDomain
    const apiUrl = `https://${domain}/wp-json/minapper/v1/help/list`

    wx.request({
      url: apiUrl,
      method: 'GET',
      data: params,
      success: (res) => {
        if (res.statusCode === 200 && res.data) {
          const newList = this.data.publishedPage === 1 ? res.data.list : [...this.data.publishedList, ...res.data.list]
          
          this.setData({
            publishedList: newList,
            publishedHasMore: res.data.list.length === this.data.per_page,
            publishedPage: this.data.publishedPage + 1,
            hasMore: res.data.list.length === this.data.per_page
          })
        } else {
          wx.showToast({
            title: '加载失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('加载我发布的互助列表失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ loading: false })
        wx.stopPullDownRefresh()
      }
    })
  },

  // 加载我接单的互助列表
  loadAcceptedList: function () {
    if (this.data.loading) return

    this.setData({ loading: true })

    const params = {
      page: this.data.acceptedPage,
      per_page: this.data.per_page,
      response_user_id: this.data.userId // 筛选当前用户响应的
    }

    if (this.data.responseStatusFilter) {
      params.response_status = this.data.responseStatusFilter
    }
    if (this.data.typeFilter) {
      params.help_type = this.data.typeFilter
    }

    const domain = config.default.getDomain
    const apiUrl = `https://${domain}/wp-json/minapper/v1/help/list`

    wx.request({
      url: apiUrl,
      method: 'GET',
      data: params,
      success: (res) => {
        if (res.statusCode === 200 && res.data) {
          const newList = this.data.acceptedPage === 1 ? res.data.list : [...this.data.acceptedList, ...res.data.list]
          
          this.setData({
            acceptedList: newList,
            acceptedHasMore: res.data.list.length === this.data.per_page,
            acceptedPage: this.data.acceptedPage + 1,
            hasMore: res.data.list.length === this.data.per_page
          })
        } else {
          wx.showToast({
            title: '加载失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('加载我接单的互助列表失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ loading: false })
        wx.stopPullDownRefresh()
      }
    })
  },

  // 加载更多
  loadMore: function () {
    this.loadCurrentTabData()
  },

  // 状态筛选变化
  onStatusChange: function (e) {
    this.setData({
      statusFilter: e.detail
    })
    this.refreshCurrentTab()
  },

  // 响应状态筛选变化
  onResponseStatusChange: function (e) {
    this.setData({
      responseStatusFilter: e.detail
    })
    this.refreshCurrentTab()
  },

  // 类型筛选变化
  onTypeChange: function (e) {
    this.setData({
      typeFilter: e.detail
    })
    this.refreshCurrentTab()
  },

  // 跳转到详情页
  goToDetail: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/help/detail/detail?id=${id}`
    })
  },

  // 跳转到发布页
  goToPublish: function () {
    wx.navigateTo({
      url: '/pages/help/publish/publish'
    })
  }
})
