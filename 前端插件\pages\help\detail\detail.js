// pages/help/detail/detail.js
const app = getApp()
const API = require('../../../utils/api.js')
const Auth = require('../../../utils/auth.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 互助详情数据
    helpDetail: {},

    // 响应列表数据
    responses: [],

    // 用户信息
    userInfo: {},
    userSession: {},

    // 用户信息缓存
    userInfoCache: {},

    // 界面状态
    loading: true,
    showResponseModal: false,
    showSelectModal: false,

    // 响应表单数据
    responseForm: {
      content: '',
      estimated_time: ''
    },

    // 选择的响应ID
    selectedResponseId: '',

    // 状态映射
    statusMap: {
      'pending': '待响应',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    },

    urgencyMap: {
      'urgent': '紧急',
      'normal': '一般',
      'low': '不急'
    },

    // 当前用户角色
    userRole: '', // 'requester' | 'helper' | 'visitor'

    // 页面参数
    helpId: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('=== 互助详情页面加载 ===', options);

    if (options.id) {
      this.setData({ helpId: options.id });
      this.initPage();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 初始化页面
  initPage() {
    this.getUserInfo();
    this.loadHelpDetail();
  },

  // 获取用户信息
  getUserInfo() {
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userSession = wx.getStorageSync('userSession') || {};

    this.setData({
      userInfo: userInfo,
      userSession: userSession
    });
  },

  // 获取用户头像
  getUserAvatar(userId) {
    // 如果是当前用户，返回当前用户头像
    if (userId == this.data.userSession.userId && this.data.userInfo.avatarUrl) {
      return this.data.userInfo.avatarUrl;
    }

    // 否则返回空字符串，使用默认头像
    return '';
  },

  // 获取用户真实信息
  getUserRealInfo(userId) {
    return new Promise((resolve, reject) => {
      // 检查缓存
      const cacheKey = `user_info_${userId}`;
      const cachedInfo = this.data.userInfoCache && this.data.userInfoCache[cacheKey];

      if (cachedInfo) {
        resolve(cachedInfo);
        return;
      }

      // 调用API获取用户信息
      const args = {
        userId: userId,
        curLoginUserId: this.data.userSession.userId || '',
        curLoginSessionId: this.data.userSession.sessionId || ''
      };

      API.getUserInfo(args).then(res => {
        if (res && res.data) {
          const userInfo = {
            rating: res.data.rating || res.data.user_rating || '4.8',
            completed: res.data.completed_count || res.data.help_completed || 0,
            avatar: res.data.avatar || res.data.user_avatar || '',
            profession: res.data.profession || res.data.user_profession || '',
            experience: res.data.experience || res.data.user_experience || ''
          };

          // 缓存用户信息
          const userInfoCache = this.data.userInfoCache || {};
          userInfoCache[cacheKey] = userInfo;
          this.setData({ userInfoCache: userInfoCache });

          resolve(userInfo);
        } else {
          // 返回默认值
          resolve({
            rating: '4.8',
            completed: 0,
            avatar: '',
            profession: '',
            experience: ''
          });
        }
      }).catch(error => {
        console.error('获取用户信息失败:', error);
        // 返回默认值
        resolve({
          rating: '4.8',
          completed: 0,
          avatar: '',
          profession: '',
          experience: ''
        });
      });
    });
  },

  // 加载互助详情
  loadHelpDetail() {
    wx.showLoading({ title: '加载中...' });

    API.getHelpDetail(this.data.helpId).then(res => {
      wx.hideLoading();
      console.log('API返回数据:', res);

      // 适配后端返回的数据结构：{request: {...}, responses: [...]}
      if (res && res.request) {
        const rawDetail = res.request;
        const responses = res.responses || [];

        // 映射字段名称，适配前端期望的数据结构
        const helpDetail = {
          id: rawDetail.id,
          topic_id: rawDetail.topic_id,
          user_id: rawDetail.user_id,
          urgency: rawDetail.urgency,
          help_type: rawDetail.help_type,
          points_reward: rawDetail.points_reward,
          location_info: rawDetail.location_info,
          contact_info: rawDetail.contact_info,
          status: rawDetail.status,
          created_at: rawDetail.created_at,
          updated_at: rawDetail.updated_at,
          // 映射标题和内容字段
          title: rawDetail.post_title || rawDetail.title,
          content: rawDetail.post_content || rawDetail.content,
          // 用户信息
          author_name: rawDetail.user_name,
          author_avatar: rawDetail.user_avatar || rawDetail.avatar || this.getUserAvatar(rawDetail.user_id),
          author_rating: rawDetail.user_rating || '4.8',
          author_completed: rawDetail.user_completed || 12,
          // 其他字段
          images: rawDetail.images || [],
          responses: responses
        };

        // 确定用户角色
        let userRole = 'visitor';
        if (this.data.userSession.userId) {
          if (helpDetail.user_id == this.data.userSession.userId) {
            userRole = 'requester'; // 发布者
          } else {
            // 检查是否是响应者
            const userResponse = responses.find(r =>
              r.user_id == this.data.userSession.userId
            );
            if (userResponse) {
              userRole = 'helper'; // 帮助者
            }
          }
        }

        // 处理选中的帮助者信息
        const selectedResponse = responses.find(r => r.status === 'selected');
        if (selectedResponse) {
          helpDetail.selected_helper = {
            user_name: selectedResponse.user_name,
            estimated_time: selectedResponse.estimated_time
          };
        }

        this.setData({
          helpDetail: helpDetail,
          responses: responses,
          userRole: userRole,
          loading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: helpDetail.title || '互助详情'
        });

        // 获取发布者真实数据
        this.loadPublisherRealInfo(helpDetail);

        // 获取响应者真实数据
        this.loadResponsesRealInfo(responses);

        console.log('处理后的数据:', {
          helpDetail: helpDetail,
          responses: responses,
          userRole: userRole
        });

      } else {
        console.error('数据结构错误:', res);
        this.handleLoadError('数据格式错误');
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('加载互助详情失败:', error);
      this.handleLoadError('网络错误，请重试');
    });
  },

  // 处理加载错误
  handleLoadError(message) {
    this.setData({ loading: false });
    wx.showToast({
      title: message,
      icon: 'none'
    });
    setTimeout(() => {
      wx.navigateBack();
    }, 2000);
  },

  // 加载发布者真实信息
  loadPublisherRealInfo(helpDetail) {
    if (!helpDetail.user_id) return;

    this.getUserRealInfo(helpDetail.user_id).then(userInfo => {
      this.setData({
        'helpDetail.author_rating': userInfo.rating,
        'helpDetail.author_completed': userInfo.completed,
        'helpDetail.author_avatar': userInfo.avatar || helpDetail.author_avatar
      });

      console.log('发布者真实数据更新:', userInfo);
    });
  },

  // 加载响应者真实信息
  loadResponsesRealInfo(responses) {
    if (!responses || responses.length === 0) return;

    // 为每个响应者获取真实信息
    responses.forEach((response, index) => {
      if (response.user_id) {
        this.getUserRealInfo(response.user_id).then(userInfo => {
          const updateKey = `responses[${index}]`;
          this.setData({
            [`${updateKey}.user_rating`]: userInfo.rating,
            [`${updateKey}.user_completed`]: userInfo.completed,
            [`${updateKey}.user_profession`]: userInfo.profession || '专业维修',
            [`${updateKey}.user_experience`]: userInfo.experience || '5',
            [`${updateKey}.user_avatar`]: userInfo.avatar || response.user_avatar
          });

          console.log(`响应者${index}真实数据更新:`, userInfo);
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果从其他页面返回，刷新数据
    if (this.data.helpId && !this.data.loading) {
      this.loadHelpDetail();
    }
  },

  // ==================== 响应相关方法 ====================

  // 显示响应弹窗
  showResponseModal() {
    // 检查登录状态
    if (!this.data.userSession.userId) {
      this.showLoginModal();
      return;
    }

    if (this.data.userRole === 'requester') {
      wx.showToast({
        title: '不能响应自己的需求',
        icon: 'none'
      });
      return;
    }

    if (this.data.helpDetail.status !== 'pending') {
      wx.showToast({
        title: '该需求已不接受响应',
        icon: 'none'
      });
      return;
    }

    this.setData({ showResponseModal: true });
  },

  // 显示登录弹窗
  showLoginModal() {
    wx.showModal({
      title: '需要登录',
      content: '请先登录后再进行响应操作',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.navigateToLogin();
        }
      }
    });
  },

  // 跳转到登录页面
  navigateToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 隐藏响应弹窗
  hideResponseModal() {
    this.setData({
      showResponseModal: false,
      responseForm: {
        content: '',
        estimated_time: ''
      }
    });
  },

  // 响应内容输入
  onResponseContentChange(e) {
    this.setData({
      'responseForm.content': e.detail.value
    });
  },

  // 预计时间输入
  onEstimatedTimeChange(e) {
    this.setData({
      'responseForm.estimated_time': e.detail.value
    });
  },

  // 提交响应
  submitResponse() {
    const { content, estimated_time } = this.data.responseForm;

    if (!content.trim()) {
      wx.showToast({
        title: '请填写响应内容',
        icon: 'none'
      });
      return;
    }

    if (!estimated_time.trim()) {
      wx.showToast({
        title: '请填写预计完成时间',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '提交中...' });

    const args = {
      content: content.trim(),
      estimated_time: estimated_time.trim(),
      userid: this.data.userSession.userId,
      sessionid: this.data.userSession.sessionId
    };

    API.submitHelpResponse(this.data.helpId, args).then(res => {
      wx.hideLoading();

      if (res && res.success) {
        wx.showToast({
          title: '响应提交成功',
          icon: 'success'
        });

        this.hideResponseModal();
        this.loadHelpDetail(); // 刷新数据

      } else {
        wx.showToast({
          title: res.message || '提交失败，请重试',
          icon: 'none'
        });
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('提交响应失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  // ==================== 选择帮助者相关方法 ====================

  // 选择帮助者
  selectHelper(e) {
    const responseId = e.currentTarget.dataset.id;

    if (!responseId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认选择',
      content: '确定选择这位帮助者吗？选择后将无法更改。',
      success: (res) => {
        if (res.confirm) {
          this.confirmSelectHelper(responseId);
        }
      }
    });
  },

  // 确认选择帮助者
  confirmSelectHelper(responseId) {
    wx.showLoading({ title: '处理中...' });

    const args = {
      response_id: responseId,
      userid: this.data.userSession.userId,
      sessionid: this.data.userSession.sessionId
    };

    API.selectHelper(this.data.helpId, args).then(res => {
      wx.hideLoading();

      if (res && res.success) {
        wx.showToast({
          title: '选择成功',
          icon: 'success'
        });

        this.loadHelpDetail(); // 刷新数据

      } else {
        wx.showToast({
          title: res.message || '选择失败，请重试',
          icon: 'none'
        });
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('选择帮助者失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // ==================== 需求管理相关方法 ====================

  // 取消需求
  cancelRequest() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个互助需求吗？取消后无法恢复。',
      success: (res) => {
        if (res.confirm) {
          this.confirmCancelRequest();
        }
      }
    });
  },

  // 确认取消需求
  confirmCancelRequest() {
    wx.showLoading({ title: '处理中...' });

    const args = {
      userid: this.data.userSession.userId,
      sessionid: this.data.userSession.sessionId
    };

    API.cancelHelpRequest(this.data.helpId, args).then(res => {
      wx.hideLoading();

      if (res && res.success) {
        wx.showToast({
          title: '取消成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);

      } else {
        wx.showToast({
          title: res.message || '取消失败，请重试',
          icon: 'none'
        });
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('取消需求失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 完成确认
  completeRequest() {
    wx.showModal({
      title: '确认完成',
      content: '确认服务已完成吗？完成后积分将转给帮助者。',
      success: (res) => {
        if (res.confirm) {
          this.confirmCompleteRequest();
        }
      }
    });
  },

  // 确认完成请求
  confirmCompleteRequest() {
    wx.showLoading({ title: '处理中...' });

    API.completeHelpRequest(this.data.helpId, {}).then(res => {
      wx.hideLoading();

      if (res && res.success) {
        wx.showToast({
          title: '完成确认成功',
          icon: 'success'
        });

        // 跳转到评价页面
        wx.navigateTo({
          url: `/pages/help/rate/rate?id=${this.data.helpId}&type=requester_to_helper`
        });

      } else {
        wx.showToast({
          title: res.message || '操作失败，请重试',
          icon: 'none'
        });
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('完成确认失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // ==================== 其他功能方法 ====================

  // 预览图片
  previewImage(e) {
    const src = e.currentTarget.dataset.src;
    const urls = e.currentTarget.dataset.urls;

    wx.previewImage({
      current: src,
      urls: urls
    });
  },



  // ==================== 页面事件处理 ====================

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadHelpDetail();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 如果需要分页加载响应列表，可以在这里实现
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: this.data.helpDetail.title || '邻里互助',
      path: `/pages/help/detail/detail?id=${this.data.helpId}`,
      imageUrl: this.data.helpDetail.images && this.data.helpDetail.images[0] || ''
    };
  }
})