<view wx:if="{{true}}" class="extend-link" style="bottom: {{ bottom }}">
  <block wx:if="{{showSidePop}}">
      <!-- 视频号扩展链接 -->
    <!-- <view wx:if="{{showVideoChannel}}" catchtap="onCreat" class="extend-link-item">
      <text class="iconfont icon-play" />
      <view class="des">视频号</view>
    </view> -->
    <!-- 推荐好友获取积分 -->
    <view bindtap="onInvite" class="extend-link-item">
      <text class="iconfont icon-points" />
      <view class="des">赚积分</view>
    </view>
    <!-- #if MP -->
    <view class="extend-link-item">
      <text class="iconfont icon-shop-service" />
      <view class="des">客   服</view>
      <button class='item' open-type='contact' hover-class='none' 	show-message-card="{{true}}"	send-message-title="{{send_message_title}}"  send-message-path="{{send_message_path}}" 	send-message-img="{{send_message_img}}" bindcontact="handleContact">
      </button>
    </view>
    <view wx:if="{{showCart}}" class="extend-link-item" bindtap="openMyCart">
      <text class="iconfont icon-shop-cart" />
      <view class="des">购物车</view>
    </view>
    <view wx:if="{{showSubscribe}}" class="extend-link-item" bindtap="onSubscribe">
      <text class="iconfont icon-subscribe" />
      <view class="des">订阅</view>
    </view>
    <!-- #endif -->
    <!-- 发布文章 -->
    <view wx:if="{{showAddArticle}}" class="extend-link-item" bindtap="addArticle">
      <text class="iconfont icon-release" />
      <view class="des">写文章</view>
    </view>
    <!-- 发布话题 -->
    <view wx:if="{{showAddTopic}}" class="extend-link-item" bindtap="addTopic">
      <text class="iconfont icon-release" />
      <view class="des">发话题</view>
    </view>
  </block>

  <view class="extend-link-item itemone" catchtap="onOpenSide">
    <text class="{{showSidePop ? 'line-active' : (pageType=='index'|| pageType=='social'?'iconfont icon-release':'line')}}" />
  </view>
</view>

<!-- <view
  wx:if="{{show}}"
  class="{{ isBorder ? 'extend-link extend-link-border' : 'extend-link' }}"
  style="padding: {{ padding }}"
>
  <view wx:if="{{showVideoChannel}}" class="video-channel extend-item" catchtap="onCreat">
    <view>视频号扩展链接</view>
    <text class="iconfont icon-arrow-right-fill" />
  </view>

  <view class="{{showVideoChannel ? 'invite extend-item' : 'invite invite-only extend-item'}}" bindtap="onInvite">
    <view>推荐好友获取积分</view>
    <text class="iconfont icon-arrow-right-fill" />
  </view>
</view> -->

<!-- <van-popup
  show="{{showSidePop}}"
  z-index="{{9999}}"
  position="right"
  custom-style="height: 100%"
  bind:close="onCloseSide"
>
  <view class='pupop-side'>
    <view wx:if="{{showVideoChannel}}" catchtap="onCreat" class="pupop-side-item">
      <text class="iconfont icon-link" />
      视频号扩展链接
    </view>

    <view bindtap="onInvite" class="pupop-side-item">
      <text class="iconfont icon-recommend" />
      推荐好友获取积分
    </view>
  </view>
</van-popup> -->


<!-- 嵌入视频号弹出层 -->
<van-popup show="{{showVideoPop}}" round closeable z-index="{{9999}}" bind:close="onClose">
  <view class='pupop-insert'>
    <view class="header">嵌入视频号</view>

    <view class="pupop-body">
      <view class="content">
        <text class="label">扩展链接：</text>
        <view class="val">{{link}}</view>
        <view class="btn-copy" catchtap="onCopy">复制</view>
      </view>
    </view>
  </view>
</van-popup>

<!-- 登录弹窗 -->
<z-login />