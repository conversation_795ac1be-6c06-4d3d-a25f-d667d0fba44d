<?php

if (!defined('ABSPATH')) {
	exit;
}

class RAW_REST_Users_Controller  extends WP_REST_Controller
{
	public function __construct()
	{
		$this->namespace     = 'minapper/v1';
		$this->resource_name = 'users';
	}

	public function register_routes()
	{
		register_rest_route($this->namespace, '/' . $this->resource_name . '/login', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'login'),
				'permission_callback' => array($this, 'get_users_login_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'js_code' => array(
						'required' => true
					),
					'encryptedData' => array(
						'required' => true
					),
					'iv' => array(
						'required' => true
					),
				)
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/wxlogin', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'wxlogin'),
				'permission_callback' => array($this, 'get_users_login_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'js_code' => array(
						'required' => true
					),
					'encryptedData' => array(
						'required' => true
					),
					'iv' => array(
						'required' => true
					),
				)
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/dountwxlogin', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'dountwxlogin'),
				'permission_callback' => array($this, 'get_users_login_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'code' => array(
						'required' => true
					),
				)
			),
			'schema' => array($this, 'get_public_item_schema'),
		));


		register_rest_route($this->namespace, '/' . $this->resource_name . '/userlogin', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'userlogin'),
				'permission_callback' => array($this, 'userlogin_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'userInfo' => array(
						'required' => true
					),
					'js_code' => array(
						'required' => true
					)
				)
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/webchatuserlogin', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'userlogin'),
				'permission_callback' => array($this, 'userlogin_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'js_code' => array(
						'required' => true
					)
				)
			),
			'schema' => array($this, 'get_public_item_schema'),
		));


		register_rest_route($this->namespace, '/' . $this->resource_name . '/openid', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'getOpenId'),
				'permission_callback' => array($this, 'get_users_login_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'js_code' => array(
						'required' => true
					)

				)
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/session', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'getMemberUserInfo'),
				'permission_callback' => array($this, 'get_weixinUserInfo_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'sessionId' => array(
						'required' => true
					),
					'userId' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/(?P<id>\d+)', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'getUserInfo'),
				'permission_callback' => array($this, 'get_userInfo_permissions_check'),

			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/updatesession', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'update_session'),
				'permission_callback' => array($this, 'update_session_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/signin', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'user_sign_in'),
				'permission_callback' => array($this, 'user_sign_in_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));
		register_rest_route($this->namespace, '/' . $this->resource_name . '/(?P<id>\d+)' . '/follow', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'user_follow'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'id' => array(
						'required' => true
					),
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/(?P<id>\d+)' . '/unfollow', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'user_unfollow'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'id' => array(
						'required' => true
					),
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));


		register_rest_route($this->namespace, '/' . $this->resource_name . '/myFollowAuthors', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_my_follow_authors'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/followmeAuthors', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_follow_me_authors'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/myZanImage', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_my_zan_images'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/authorlist', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_author_list'),
				'permission_callback' => array($this, 'get_author_list_permissions_check'),


			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/mytask', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_my_task'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/openAdVideo', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'open_Ad_Video'),
				'permission_callback' => array($this, 'open_Ad_Video_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		// register_rest_route( $this->namespace, '/' . $this->resource_name . '/shareapp', array(
		//     array(
		//         'methods'             => 'GET',
		//         'callback'            => array( $this, 'share_app' ),
		//         'permission_callback' => array( $this, 'share_app_permissions_check' ),
		//         'args'                => array( 					            
		//             'sessionid' => array(
		//                 'required' => true
		//             ),
		//             'userid' => array(
		//                 'required' => true
		//             ),
		//         ),
		//     ),
		//     'schema' => array( $this, 'get_public_item_schema' ),
		// ) );

		register_rest_route($this->namespace, '/' . $this->resource_name . '/myIntegral', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_my_integral'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));


		register_rest_route($this->namespace, '/' . $this->resource_name . '/integralRanking', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_integralRanking'),
				'permission_callback' => array($this, 'get_users_locations_permissions_check'),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/hot', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_hot_users'),
				'permission_callback' => array($this, 'get_users_locations_permissions_check'),

			),
			'schema' => array($this, 'get_public_item_schema'),
		));


		register_rest_route($this->namespace, '/' . $this->resource_name . '/followRanking', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_followRanking'),
				'permission_callback' => array($this, 'get_users_locations_permissions_check'),

			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/locations', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_users_locations'),
				'permission_callback' => array($this, 'get_users_locations_permissions_check'),

			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/rand', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_users_rand'),
				'permission_callback' => array($this, 'get_users_locations_permissions_check'),

			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/recentlogin', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_users_recent_login'),
				'permission_callback' => array($this, 'get_users_locations_permissions_check'),

			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/recentcomment', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_users_recent_comment'),
				'permission_callback' => array($this, 'get_users_locations_permissions_check'),

			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/subscribeMessage', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'subscribe_message'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
					'subscribetype' => array(
						'required' => true
					),
					'subscribeflag' => array(
						'required' => true
					)

				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/getphonenumber', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'getPhoneMumber'),
				'permission_callback' => array($this, 'get_users_login_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'js_code' => array(
						'required' => true
					),
					'encryptedData' => array(
						'required' => true
					),
					'iv' => array(
						'required' => true
					),
				)
			),
			'schema' => array($this, 'get_public_item_schema'),
		));


		register_rest_route($this->namespace, '/' . $this->resource_name . '/getuserphonenumber', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'getUserPhoneMumber'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'code' => array(
						'required' => true
					),
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				)
			),
			'schema' => array($this, 'get_public_item_schema'),
		));
		register_rest_route($this->namespace, '/' . $this->resource_name . "/(?P<id>\d+)/qrcode", array(
			// Here we register the readable endpoint for collections.
			array(
				'methods'   => 'GET',
				'callback'  => array($this, 'get_user_qrcode'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'               => array(
					'id' => array(
						'required' => true
					)

				)
			),
			// Register our schema callback.
			'schema' => array($this, 'post_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . "/invitelog", array(
			// Here we register the readable endpoint for collections.
			array(
				'methods'   => 'GET',
				'callback'  => array($this, 'set_invite_log'),
				'permission_callback' => array($this, 'set_invite_log_permissions_check'),
				'args'               => array(
					'openid' => array(
						'required' => true
					),
					'invitecode' => array(
						'required' => true
					)

				)
			),
			// Register our schema callback.
			'schema' => array($this, 'post_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/inviteme', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_invite_me'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/myinvite', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_my_invite'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/myshareapp', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_my_shareapp'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/mycardcode', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'get_my_cardcode'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/updatedescription', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'update_user_description'),
				'permission_callback' => array($this, 'update_user_description_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/updatewechatshopinfo', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'update_user_wechatshop_info'),
				'permission_callback' => array($this, 'get_author_list_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
					'storeappid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/despending', array(
			array(
				'methods'             => 'GET',
				'callback'            => array($this, 'user_despending'),
				'permission_callback' => array($this, 'user_despending_permissions_check'),
				'args'                => array(
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));

		register_rest_route($this->namespace, '/' . $this->resource_name . '/approveuserdec', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'approve_user_des'),
				'permission_callback' => array($this, 'approve_user_des_permissions_check'),
				'args'                => array(
					'id' => array(
						'required' => true
					),
					'despending' => array(
						'required' => true
					),
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));
		register_rest_route($this->namespace, '/' . $this->resource_name . '/updatenickname', array(
            array(
                'methods'             => 'POST',
                'callback'            => array($this, 'updateNickname'),
                'permission_callback' => array($this, 'check_user_permissions_check'),
                'args'                => array(
                    'context' => $this->get_context_param(array('default' => 'view')),
                    'userid' => array(
                        'required' => true
                    ),
                    'sessionid' => array(
                        'required' => true
                    ),
                    'nickname' => array(
                        'required' => true
                    )
                )
            ),
            'schema' => array($this, 'get_public_item_schema'),
        ));
		register_rest_route($this->namespace, '/' . $this->resource_name . '/updateavatar', array(
            array(
                'methods'             => 'POST',
                'callback'            => array($this, 'updateAvatar'),
                'permission_callback' => array($this, 'check_user_permissions_check'),
                'args'                => array(
                    'context' => $this->get_context_param(array('default' => 'view')),
                    'userid' => array(
                        'required' => true
                    ),
                    'sessionid' => array(
                        'required' => true
                    ),
                    'avatarUrl' => array(
                        'required' => true
                    )
                )
            ),
            'schema' => array($this, 'get_public_item_schema'),
        )); 

		register_rest_route($this->namespace, '/' . $this->resource_name . '/delete', array(
			array(
				'methods'             => 'POST',
				'callback'            => array($this, 'delete_item'),
				'permission_callback' => array($this, 'check_user_permissions_check'),
				'args'                => array(
					'context' => $this->get_context_param(array('default' => 'view')),
					'sessionid' => array(
						'required' => true
					),
					'userid' => array(
						'required' => true
					),
				),
			),
			'schema' => array($this, 'get_public_item_schema'),
		));
	}


	public function delete_item( $request ) {
		
		global $wpdb; 
		$user = $this->get_user( $request['userid'] );
		if ( is_wp_error( $user ) ) {
			return $user;
		}
		$id       = (int)$request['userid'];
		//$request->set_param( 'context', 'edit' );		
		require_once ABSPATH . 'wp-admin/includes/user.php';
		$result = wp_delete_user( $id,null);
		if ( ! $result ) {
			return new WP_Error(
				'error',
				'注销用户错误',
				array( 'status' => 500 )
			);
		}


		$wpdb->delete(
			$wpdb->comments,
			['user_id' => $id ],
			['%d']
		);
		
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$wpdb->minapper_order= $wpdb->prefix.'minapper_order';
		$wpdb->minapper_integral= $wpdb->prefix.'minapper_integral';
		$wpdb->minapper_message= $wpdb->prefix.'minapper_message';
		$wpdb->minapper_user_session= $wpdb->prefix.'minapper_user_session';
		$wpdb->minapper_logs= $wpdb->prefix.'minapper_logs';
		$wpdb->minapper_cardcode= $wpdb->prefix.'minapper_cardcode';

		$wpdb->delete(
			$wpdb->minapper_weixin_users,
			['userid' => $id ],
			['%d']
		);

		$wpdb->delete(
			$wpdb->minapper_ext,
			['userid' => $id ],
			['%d']
		);

		$wpdb->delete(
			$wpdb->minapper_order,
			['userid' => $id ],
			['%d']
		);


		$wpdb->delete(
			$wpdb->minapper_integral,
			['userid' => $id ],
			['%d']
		);

		$wpdb->delete(
			$wpdb->minapper_message,
			['userid' => $id ],
			['%d']
		);


		$wpdb->delete(
			$wpdb->minapper_user_session,
			['userid' => $id ],
			['%d']
		);

		$wpdb->delete(
			$wpdb->minapper_logs,
			['userid' => $id ],
			['%d']
		);

		$wpdb->delete(
			$wpdb->minapper_cardcode,
			['userid' => $id ],
			['%d']
		);
		$response = array('success' => true, 'message' => '注销用户成功');	
		return $response;
	}

	protected function get_user( $id ) {
		$error = new WP_Error(
			'error',
			'错误的用户id',
			array( 'status' => 404 )
		);

		if ( (int) $id <= 0 ) {
			return $error;
		}

		$user = get_userdata( (int) $id );
		if ( empty( $user ) || ! $user->exists() ) {
			return $error;
		}

		return $user;
	}

	public function updateAvatar($request){

		global $wpdb;
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$avatarUrl=$request['avatarUrl'];
		$userId=(int)$request['userid'];		
		$data_user = array(					
			'updatedate' => date('Y-m-d H:i:s', time()),
			'avatarurl' => $avatarUrl,

		);
		$where  = array('userid' => $userId);
        $updateUserResult = $wpdb->update($wpdb->minapper_weixin_users, $data_user, $where);
		if ($updateUserResult == false) {
			return new WP_Error( 'error', '设置失败', array( 'status' => 200 ) );
        }

		$userInfo=RAW_Util::getMemberUserbyUserId($userId);	
		$enableUpdateAvatarCount =RAW_Util::getEnableUpdateAvatarCount($userId); 
		$userInfo->enableUpdateAvatarCount=$enableUpdateAvatarCount;
		$response = array('success' => true, 'message' => '设置成功','userInfo' => $userInfo);	
		$response = rest_ensure_response($response);
		return $response;


	}
	public function updateNickname($request){

		global $wpdb;
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$nickname=$request['nickname'];
		$userId=(int)$request['userid'];
		$count = RAW_Util::strLength($nickname);
        if($count>30)
        {
            return new WP_Error( 'error', '用户昵称不能超过30个字符', array( 'status' => 200 ) );
        }
		$wpuser = get_user_by("id",$userId);
		$openId="";
		if(empty($wpuser))
		{
			return new WP_Error( 'error', '参数不正确', array( 'status' => 200 ) );
		}
		else
		{
			$openId=$wpuser->user_login;
			if(empty($openId))
			{
				return new WP_Error( 'error', '未找到此用户', array( 'status' => 200 ) );
			}
		}
		$check_nickname=mb_convert_encoding($nickname, 'UTF-8', mb_detect_encoding($nickname));
		$data = array(
			'content' =>$check_nickname,
			'openid'   =>$openId,
			'nickname'=>$check_nickname,
			'scene'   =>2,
			'version'=>2
			
		);
		$msgSecCheckResult=RAW_Util::security_msgSecCheck($data); //检测昵称是否合法
		$errcode=$msgSecCheckResult['errcode'];		
		$errmsg=$msgSecCheckResult['errmsg'];
		if($errcode !=0)
		{
			return new WP_Error( 'error', $errmsg, array( 'status' => 200 ) );
		}

		$data_user = array(					
			'updatedate' => date('Y-m-d H:i:s', time()),
			'nickname' => $nickname,

		);
		$where  = array('userid' => $userId);
        $updateUserResult = $wpdb->update($wpdb->minapper_weixin_users, $data_user, $where);
		if ($updateUserResult == false) {
			return new WP_Error( 'error', '设置失败', array( 'status' => 200 ) );
        }
		else
		{
			$nickname = RAW_Util::filterEmoji($nickname);
			$_nickname = base64_encode($nickname);
			$_nickname = strlen($_nickname) > 49 ? substr($_nickname, 49) : $_nickname;
			$userId = RAW_Util::updateWPUser($userId, $nickname, $nickname, $_nickname, $nickname);
			if ($userId == 0) {
				return new WP_Error('error', '更新wordpress用户错误：', array('status' => 500));
			}
		}

		$userInfo=RAW_Util::getMemberUserbyUserId($userId);	
		$response = array('success' => true, 'message' => '设置成功','userInfo' => $userInfo);	
		$response = rest_ensure_response($response);
		return $response;


	}

	public function approve_user_des($request)
	{
		$id = (int)$request['id'];
		$despending = $request['despending'];
		if (get_user_meta($id, "description", true) != '') {
			$result = update_user_meta($id, 'despending', $despending);
			$response = array('success' => true, 'message' => '审核完成');
			$response = rest_ensure_response($response);
			return $response;
		} else {
			return new WP_Error('error', 'despending参数错误', array('status' => 200));
		}
	}

	public  function user_despending($request)
	{

		$per_page = empty($request['per_page']) ? 50 : (int)$request['per_page'];
		$page = empty($request['page']) ? 1 : (int)$request['page'];

		$page = ($page - 1) * $per_page;

		$userpending = RAW_Util::getUserDespending($page, $per_page);
		$response = rest_ensure_response($userpending);
		return $response;
	}

	public function  update_user_description($request)
	{
		$userId = (int)$request['userid'];
		$description = $request['description'];
		$data = array(
			'content' => $description
		);
		$msgSecCheckResult = RAW()->wxapi->msgSecCheck($data);
		$errcode = (int)$msgSecCheckResult['errcode'];
		$errmsg = $msgSecCheckResult['errmsg'];
		if ($errcode == 87014) {
			return new WP_Error($errcode, $errmsg, array('status' => 200));
		}

		$result = update_user_meta($userId, 'description', $description);

		if ($result) {
			update_user_meta($userId, 'despending', '2');
			$response = array('success' => true, 'message' => '个人简介已提交审核');
			$response = rest_ensure_response($response);
			return $response;
		} else {
			$_description = get_user_meta($userId, "description", true);
			return new WP_Error('error', '保存数据失败', array('status' => 200));
		}
	}

	public function  update_user_wechatshop_info($request)
	{
		$userId = (int)$request['userid'];
		$storeAppId = $request['storeappid'];

		$storeLocation = $request['storelocation'];
		$storeAddress = $request['storeaddress'];
		$storeLatitude = $request['storelatitude'];
		$storeLongitude = $request['storelongitude'];

		$storeName="";
		$flag=false;
		$data=new class
        {
        };
        $result =  RAW()->wxapi->get_cooperation_shop_list($data);
		if($result['errcode']==0)
		{
			$shop_list=$result['shop_list'];
			foreach($shop_list as $item)
			{
				if($item['status']==1 &&  $item['appid']==$storeAppId)
				{
					$storeName=$item['nickname'];
					$flag=true;
					try{

						if(!empty($storeLocation))
						{
							global $wpdb; 
							$wpdb->minapper_cooperation_shop = $wpdb->prefix .'minapper_cooperation_shop';
							if ($wpdb->get_var("show tables like '" . $wpdb->minapper_cooperation_shop. "'") == $wpdb->minapper_cooperation_shop) {
								$sql = "select count(1) from ". $wpdb->minapper_cooperation_shop. " where appid='". $storeAppId. "'";
								$count = $wpdb->get_var($sql);
								if($count==0)
								{
									$data=array(
										'appid' => $storeAppId,
										'nickname' => $storeName,
										'status'=>$item['status'],
										'bind_time'=>$item['bind_time'],
										'unbind_time'=>$item['unbind_time'],
										'cancel_time'=>$item['cancel_time'],
										'address' => $storeAddress,
										'location' =>$storeLocation,
										'latitude' =>$storeLatitude,
										'longitude' =>$storeLongitude,
									);
									$wpdb->insert($wpdb->minapper_cooperation_shop, $data);	
								}
								else
								{
									$data=array(
										'address' => $storeAddress,
										'location' =>$storeLocation,
										'latitude' =>$storeLatitude,
										'longitude' =>$storeLongitude,
									);
									$where = array('appid' => $storeAppId);
									$updateResult = $wpdb->update($wpdb->minapper_cooperation_shop, $data, $where);
									
								}
							}
						}

					}
					catch(Exception $e)
					{

					}	
					break;
				}
			}
		}
		else
		{
			return new WP_Error('error', '你的微信小店尚未绑定本小程序', array('status' => 200));
		}

		if($flag)
		{
			$storeInfo=array(
				'storeappid'=>$storeAppId,
				'storename'=>$storeName,
				'storelocation'=>$storeLocation,
				'storeaddress'=>$storeAddress,
				'storelatitude'=>$storeLatitude,
				'storelongitude'=>$storeLongitude
			);
			if(!empty($storeInfo) && !empty($storeLocation))
			{
				update_user_meta($userId, 'storeinfo', $storeInfo);
			}
			else
			{
				update_user_meta($userId, 'storeinfo', '');
			}

			
			update_user_meta($userId, 'storeappid', $storeAppId);
			update_user_meta($userId, 'storename', $storeName);			
			$response = array('success' => true, 'message' => '设置成功','storeappid'=>$storeAppId,'storename'=>$storeName,'storeinfo'=>$storeInfo);	
			$response = rest_ensure_response($response);
			return $response;
		}
		else
		{
			return new WP_Error('error', '你的微信小店尚未绑定本小程序', array('status' => 200));
		}

		
	}
	public function  get_my_cardcode($request)
	{
		global $wpdb;
		$per_page = empty($request['per_page']) ? 100 : (int)$request['per_page'];
		$page = empty($request['page']) ? 1 : (int)$request['page'];
		$page = ($page - 1) * $per_page;
		$userId = (int)$request['userid'];
		$wpdb->minapper_cardcode = $wpdb->prefix . 'minapper_cardcode';
		$sql = $wpdb->prepare("select t.code,t.codetype,t.ID,t.salemoney,t.saleintegral,t.salediscount,t.creatdate,t.enddate,t.usedate,
        case t.codetype WHEN 'money' then '现金直减' WHEN 'integral' then '积分充值' WHEN 'discount' then '折扣' end as codetypename,
        case t.status WHEN '0' then '未使用' WHEN '1' then '已使用'  end as statusname from " . $wpdb->minapper_cardcode . " t  where  t.userid=%d order by t.creatdate desc  limit %d,%d", $userId, $page, $per_page);
		$cardcodes = $wpdb->get_results($sql);
		$count = count($cardcodes);
		$result['cardcodes'] = $cardcodes;
		$result['count'] = $count;
		$response = rest_ensure_response($result);
		return $response;
	}
	public function get_my_shareapp($request)
	{

		global $wpdb;
		$per_page = empty($request['per_page']) ? 10 : (int)$request['per_page'];
		$page = empty($request['page']) ? 1 : (int)$request['page'];
		$page = ($page - 1) * $per_page;

		$userId = (int)$request['userid'];
		$wpdb->minapper_logs = $wpdb->prefix . 'minapper_logs';
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$sql = $wpdb->prepare("SELECT updatedate ,(SELECT u.avatarurl from " . $wpdb->minapper_weixin_users . " u where u.openid=t.openid) as avatarurl ,(SELECT u.nickname from " . $wpdb->minapper_weixin_users . " u where u.openid=t.openid) as nickname FROM " . $wpdb->minapper_logs . " t WHERE t.logtype='shareApp' and t.objectid=(SELECT u2.invitecode from " . $wpdb->minapper_weixin_users . " u2 where u2.userid=%d) order by t.updatedate desc limit %d,%d", $userId, $page, $per_page);
		$_shareApplog = $wpdb->get_results($sql);
		$shareApplog = array();
		if (!empty($_shareApplog)) {
			foreach ($_shareApplog as $item) {

				if (empty($item->avatarurl)) {

					$minapper_avatar = get_user_meta((int)$userId, 'minapper_avatar', true);
					if (!empty($minapper_avatar)) {
						$author_avatar = $minapper_avatar;
					} else {
						$author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
					}
					$item->avatarurl = $author_avatar;
				}

				if (empty($item->nickname)) {
					$item->nickname = "游客";
				}

				$shareApplog[] = $item;
			}
		}



		// 		$avatar=$shareApplog->avatarurl;
		// 		if(empty($avatar))
		// 		{
		// 			$avatar =plugins_url().'/'.REST_API_TO_WECHAT_PLUGIN_NAME.'/images/gravatar.png';
		// 			$shareApplog->avatarurl=$avatar;
		// 		}
		// 		$nickname=$shareApplog->nickname;
		// 		if(empty($nickname))
		// 		{

		// 			$shareApplog->avatarurl="游客";
		// 		}
		$result['shareApplog'] = $shareApplog;
		$sql = $wpdb->prepare("SELECT count(1) FROM " . $wpdb->minapper_logs . " t WHERE t.logtype='shareApp' and t.objectid=(SELECT u2.invitecode from " . $wpdb->minapper_weixin_users . " u2 where u2.userid=%d)", $userId);
		$count = (int)$wpdb->get_var($sql);
		$result['count'] = $count;
		$response = rest_ensure_response($result);
		return $response;
	}
	public function get_my_invite($request)
	{
		$cachedata = '';
		if (function_exists('MRAC')) {
			$cachedata = MRAC()->cacheManager->get_cache();
			if (!empty($cachedata)) {
				$response = rest_ensure_response($cachedata);
				return $response;
			}
		}
		global $wpdb;
		$userId = (int)$request['userid'];
		$userId = (int)$request['userid'];

		$per_page = empty($request['per_page']) ? 10 : (int)$request['per_page'];
		$page = empty($request['page']) ? 1 : (int)$request['page'];
		$page = ($page - 1) * $per_page;

		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$user = array();
		$sql = $wpdb->prepare("SELECT (select u3.userid from " . $wpdb->minapper_weixin_users . " u3 where u3.openid=t.extvalue) as userid, (select u.nickname from " . $wpdb->minapper_weixin_users . " u where u.openid=t.extvalue) as username, (select u2.avatarurl from " . $wpdb->minapper_weixin_users . " u2 where u2.openid=t.extvalue) as avartaurl ,t.updatedate  from " . $wpdb->minapper_ext . " t where t.extype='invite'  and t.userid=%d and t.extid=1 order by t.updatedate desc limit %d,%d", $userId, $page, $per_page);
		$users = $wpdb->get_results($sql);
		$result['users'] = $users;

		$sql = $wpdb->prepare("SELECT count(1) from " . $wpdb->minapper_ext . " t where t.extype='invite'  and t.userid=%d and t.extid=1", $userId);
		$count = (int)$wpdb->get_var($sql);
		$result['count'] = $count;
		$response = rest_ensure_response($result);
		if ($cachedata == '' && function_exists('MRAC')) {

			$cachedata = MRAC()->cacheManager->set_cache($result, 'myinvite', 0);
		}
		return $response;
	}

	public function get_invite_me($request)
	{
		global $wpdb;
		$userId = $request['userid'];
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$sql = $wpdb->prepare("select openid from " . $wpdb->minapper_weixin_users . " where userid=%d", $userId);
		$openId = $wpdb->get_var($sql);
		$user = array();
		if (!empty($openId)) {
			$sql = $wpdb->prepare("SELECT t.userid ,(select u.nickname from " . $wpdb->minapper_weixin_users . " u where u.userid=t.userid) as username, (select u2.avatarurl from " . $wpdb->minapper_weixin_users . " u2 where u2.userid=t.userid) as avartaurl  from " . $wpdb->minapper_ext . " t where t.extype='invite'  and t.extvalue=%s and t.extid=1", $openId);
			$user = $wpdb->get_row($sql);
		}
		$response = rest_ensure_response($user);
		return $response;
	}



	public function get_user_qrcode($request)
	{
		global $wpdb;
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$userId = $request['id'];
		$qrcodeName = 'user-qrcode-' . $userId . '.png'; //文章小程序二维码文件名
		$qrcodePath = REST_API_TO_WECHAT_PLUGIN_DIR . 'images/qrcode/';
		if (!is_dir($qrcodePath)) {
			mkdir($qrcodePath, 0777);
		}
		$qrcodePath = $qrcodePath . $qrcodeName; //文章小程序二维码路径
		$qrcodeUrl = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/qrcode/' . $qrcodeName;
		$sql = $wpdb->prepare("select invitecode from " . $wpdb->minapper_weixin_users . " where userid=%d", $userId);
		$userinvitecode = $wpdb->get_var($sql);
		if (empty($userinvitecode)) {
			$invitecode = RAW_Util::generate_invitecode();
			$data_array = array(
				'invitecode' => $invitecode
			);
			$where  = array('userId' => (int)$userId);
			$updateUserResult = $wpdb->update($wpdb->minapper_weixin_users, $data_array, $where);
		} else {
			$invitecode = $userinvitecode;
		}


		$path = "pages/index/index?invitecode=" . $invitecode;
		$color = array(
			"r" => "0",
			"g" => "0",
			"b" => "0",
		);
		$data = array(

			'path' => $path, //前端传过来的页面path
			'width' => 430, //设置二维码尺寸
			'auto_color' => false,
			'line_color' => $color,
		);
		//判断文章小程序二维码是否存在，如不存在，在此生成并保存
		if (!is_file($qrcodePath)) {

			$qrcodeesult = RAW()->wxapi->get_qrcode($data);
			$errcode = (int)$qrcodeesult['errcode'];
			if ($errcode == 0) {
				$qrcode = $qrcodeesult['buffer'];
				file_put_contents($qrcodePath, $qrcode);
				$response = array('success' => true, 'message' => '生成二维码成功', 'qrcodeurl' => $qrcodeUrl, 'path' => $path);
				$response = rest_ensure_response($response);
				return $response;
			} else {

				return new WP_Error('error', '生成二维码错误' . json_encode($qrcodeesult['code']), array('status' => 400));
			}
		} else {
			$response = array('success' => true, 'message' => '生成二维码成功', 'qrcodeurl' => $qrcodeUrl, 'path' => $path);
			$response = rest_ensure_response($response);
			return $response;
		}
	}

	public function getUserPhoneMumber($request)
	{

		global $wpdb;
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$code = $request['code'];
		$userId = $request['userid'];

		$data = array('code' => $code);
		$result = RAW()->wxapi->getUserPhoneMumber($data);
		if ($result['errcode'] != 0) {
			return new WP_Error('error', '获取手机号出错:' . $result['errmsg'], array('status' => 500));
		}

		$phone_info = $result['phone_info'];
		$purePhoneNumber = $phone_info['purePhoneNumber'];
		$sql = "select phone from " . $wpdb->minapper_weixin_users . " where userid='" . $userId . "'";
		$phone = $wpdb->get_var($sql);
		if ($phone == $purePhoneNumber) {
			$response = array('success' => true, 'message' => '手机号未更新', 'phoneinfo' => $phone_info);
			$response = rest_ensure_response($response);
			return $response;
		}

		$data_array = array(
			'phone' => $purePhoneNumber,

		);
		$where  = array('userid' => $userId);
		$message = '';
		$updateUserResult = $wpdb->update($wpdb->minapper_weixin_users, $data_array, $where);
		if (!empty($updateUserResult) && empty($phone)) {
			$operateRewordIntegral = RAW_Util::operateRewordIntegral($userId, 'raw_phone_integral', '', '', 'add');
			if ($operateRewordIntegral > 0) {

				$message = "获取" . $operateRewordIntegral . "积分奖励.";
			}
		}
		$response = array('success' => true, 'message' => $message, 'phoneinfo' => $phone_info, 'updateUserResult' => $updateUserResult, 'operateRewordIntegral' => $operateRewordIntegral);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function getPhoneMumber($request)
	{
		global $wpdb;
		date_default_timezone_set('Asia/Shanghai');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		// js_code 换取 openid
		$api_url = add_query_arg(array(
			'appid' => get_option('raw_appid'),
			'secret' => get_option('raw_secret'),
			'js_code' => $request['js_code'],
			'grant_type' => 'authorization_code'
		), 'https://api.weixin.qq.com/sns/jscode2session');

		$api_response = wp_remote_get($api_url);
		if (!is_array($api_response) || is_wp_error($api_response) || $api_response['response']['code'] != '200') {
			return new WP_Error('error', 'API错误1：' . json_encode($api_response), array('status' => 400));
		}

		$api_result = json_decode($api_response['body'], true);
		if (empty($api_result['openid']) || empty($api_result['session_key']) || !empty($api_result['errcode'])) {
			return new WP_Error('error', 'API错误2：' . json_encode($api_result), array('status' => 400));
		}

		$err_code = RAW_Util::decrypt_data(get_option('raw_appid'), $api_result['session_key'], $request['encryptedData'], $request['iv'], $data);

		if ($err_code != 0) {
			return new WP_Error('error', '解密错误：' . $err_code, array('status' => 400));
		}
		$data = json_decode($data, true);
		$openId = $api_result['openid'];
		$phoneNumber = $data['phoneNumber'];
		$purePhoneNumber = $data['purePhoneNumber'];
		$countryCode = $data['countryCode'];
		$sql = "select phone from " . $wpdb->minapper_weixin_users . " where openid='" . $openId . "'";
		$phone = $wpdb->get_var($sql);
		if ($phone == $purePhoneNumber) {
			$response = array('success' => true, 'message' => '手机号未更新', 'phoneinfo' => $data);
			$response = rest_ensure_response($response);
			return $response;
		}

		$data_array = array(
			'phone' => $purePhoneNumber,

		);
		$where  = array('openid' => $openId);
		$message = '';
		$updateUserResult = $wpdb->update($wpdb->minapper_weixin_users, $data_array, $where);
		if (!empty($updateUserResult) && empty($phone)) {
			$user = get_user_by('login', $openId);
			if (!empty($user)) {
				$userId = $user->ID;
				$operateRewordIntegral = RAW_Util::operateRewordIntegral($userId, 'raw_phone_integral', '', '', 'add');
				if ($operateRewordIntegral > 0) {

					$message = "获取" . $operateRewordIntegral . "积分奖励.";
				}
			}
		}
		$response = array('success' => true, 'message' => $message, 'phoneinfo' => $data, 'updateUserResult' => $updateUserResult, 'operateRewordIntegral' => $operateRewordIntegral);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function subscribe_message($request)
	{
		global $wpdb;
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$userId = (int)$request['userid'];
		$subscribetype = empty($request['subscribetype']) ? "" : $request['subscribetype'];
		$subscribeflag = empty($request['subscribeflag']) ? "" : $request['subscribeflag'];
		$extid = empty($request['extid']) ? 0 : (int)$request['extid'];
		$sql = "";
		$message = "";
		$subscribeCount = '0';

		// 添加调试日志
		error_log("订阅消息请求: userId={$userId}, subscribetype={$subscribetype}, subscribeflag={$subscribeflag}, extid={$extid}");
		//categorySubscribe 专题订阅消息(文章和动态的分类)
		//postCommentSubscribe 文章被评论
		//topicCommentSubscribe 动态被评论消息
		//postReplaySubscribe 文章评论被回复订阅消息
		//topicReplaySubscribe 动态评论被回复订阅消息
		//vipEnddateSubscribe  vip到期通知
		//help_notifications 互助消息通知
		//help_rating_notifications 互助评价消息通知
		if (!empty($subscribetype) &&  !empty($subscribeflag)) {

			$sql = "select t.extvalue from " . $wpdb->minapper_ext . "  t where  t.userid=" . $userId;
			if ($subscribetype == "newcontent" || $subscribetype == "newreplay" || $subscribetype == "vipEnddateSubscribe" || $subscribetype == "help_notifications" || $subscribetype == "help_rating_notifications") {
				$sql .= " and t.extype='subscribeMessage'  and t.extkey='" . $subscribetype . "'";
			} else if ($subscribetype == "categorySubscribe" || $subscribetype == "postCommentSubscribe" || $subscribetype == "topicCommentSubscribe" || $subscribetype == "postReplaySubscribe" || $subscribetype == "topicReplaySubscribe") {

				$sql .= " and t.extype='subscribeMessage'  and t.extkey='" . $subscribetype . "' and t.extid=" . $extid;
			}
			$count = $wpdb->get_var($sql);
			if ($subscribeflag == "accept") {

				if ($count == null) {
					$data_array = array(
						'userid'   => $userId,
						'extid'   => $extid,
						'extype'  => 'subscribeMessage',
						'extkey' => $subscribetype,
						'extvalue' => '1'

					);
					$inserSubscribe = $wpdb->insert($wpdb->minapper_ext, $data_array);
					if (empty($inserSubscribe)) {
						return new WP_Error('error', '订阅失败', array('status' => 500));
					}
					$subscribeCount = "1";
					$message = "订阅成功";
				} else {

					$count = (int)$count;
					$count++;
					$subscribeCount = (string)$count;
					$minapperext = array(
						'extvalue'   => $subscribeCount
					);

					if ($subscribetype == "newcontent" || $subscribetype == "newreplay" || $subscribetype == "vipEnddateSubscribe" || $subscribetype == "help_notifications" || $subscribetype == "help_rating_notifications") {
						$where  = array('userid' => $userId, 'extype' => 'subscribeMessage', 'extkey' => $subscribetype);
					} else if ($subscribetype == "categorySubscribe" || $subscribetype == "postCommentSubscribe" || $subscribetype == "topicCommentSubscribe" || $subscribetype == "postReplaySubscribe" || $subscribetype == "topicReplaySubscribe") {
						$where  = array('userid' => $userId, 'extype' => 'subscribeMessage', 'extkey' => $subscribetype, 'extid' => $extid);
					}

					$updataResult = $wpdb->update($wpdb->minapper_ext, $minapperext, $where);
					if (empty($updataResult)) {
						return new WP_Error('error', '订阅失败', array('status' => 500));
					}
					$message = "已订阅";
				}
			} else if ($subscribeflag == "reject") {
				if ($count != null  && $count != "0") {
					$count = (int)$count;
					if ($count > 0) {
						$count--;
					} else {
						$count = 0;
					}
					$subscribeCount = (string)$count;
					$minapperext = array(
						'extvalue'   => $subscribeCount
					);
					if ($subscribetype == "newcontent" || $subscribetype == "newreplay" || $subscribetype == "vipEnddateSubscribe" || $subscribetype == "help_notifications" || $subscribetype == "help_rating_notifications") {
						$where  = array('userid' => $userId, 'extype' => 'subscribeMessage', 'extkey' => $subscribetype);
					} else if ($subscribetype == "categorySubscribe" || $subscribetype == "postCommentSubscribe" || $subscribetype == "topicCommentSubscribe" || $subscribetype == "postReplaySubscribe" || $subscribetype == "topicReplaySubscribe") {
						$where  = array('userid' => $userId, 'extype' => 'subscribeMessage', 'extkey' => $subscribetype, 'extid' => $extid);
					}
					$updataResult = $wpdb->update($wpdb->minapper_ext, $minapperext, $where);
					if (empty($updataResult)) {
						return new WP_Error('error', '取消订阅失败' . $count, array('status' => 500));
					}
					$message = "取消订阅成功";
				} else {
					$message = "取消订阅成功";
					$subscribeCount = '0';
				}
			} else {
				return new WP_Error('error', "参数错误", array('status' => 500));
			}

			if (function_exists('MRAC')) {

				$cachedata = MRAC()->cacheManager->clear_cache();
			}

			$response = array('success' => true, 'message' => $message, 'subscribeCount' => $subscribeCount);
			$response = rest_ensure_response($response);
			return $response;
		} else {
			return new WP_Error('error', '参数错误', array('status' => 500));
		}
	}

	public function get_users_locations($request)
	{
		global $wpdb;
		$userList = array();
		$markerCount = empty(get_option('raw_marker_count')) ? 5 : (int)get_option('raw_marker_count');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$sql = "SELECT
		        p.id,
				p.post_content,
				p.post_title,
				u.avatarurl,
				u.userid,
				u.gender,
			u.nickname as username,
				t.meta_value AS address,
				(
					select t1.meta_value
					FROM
					" . $wpdb->postmeta . "  t1
					WHERE
						t1.post_id = p.id
					AND t1.meta_key = 'latitude'
				) AS latitude,
				(
					select t2.meta_value
					FROM
					" . $wpdb->postmeta . "  t2
					WHERE
						t2.post_id = p.id
					AND t2.meta_key = 'longitude'
				) AS longitude
			FROM
				" . $wpdb->postmeta . "  t,
				" . $wpdb->posts . "  p,
				" . $wpdb->minapper_weixin_users . " u
			WHERE
				p.post_type = 'topic'
			AND p.post_status = 'publish'
			AND t.meta_key = 'location'
			AND t.meta_value != ''
			AND p.ID = t.post_id
			AND p.post_author = u.userid
			ORDER BY
				p.post_date desc
			LIMIT 0,
			" . $markerCount;
		$_userList = $wpdb->get_results($sql);
		foreach ($_userList as $_user) {

			$user['id'] = $_user->id;
			$user['post_title'] = $_user->post_title;
			$content = $_user->post_content;
			$user['post_content'] = $content;
			$user['avatarurl'] = $_user->avatarurl;
			$user['userid'] = $_user->userid;
			$user['gender'] = $_user->gender;
			$user['username'] = $_user->username;
			$user['address'] = $_user->address;
			$user['latitude'] = $_user->latitude;
			$user['longitude'] = $_user->longitude;

			$user['firstimg'] = RAW_Util::get_post_content_first_image($content);
			$user['allimg'] = RAW_Util::get_post_content_images($content);
			$user['allimgsrc'] = RAW_Util::get_post_content_images_src($content);

			$userList[] = $user;
		}

		$response = rest_ensure_response($userList);
		return $response;
	}

	public function get_hot_users($request)
	{	$users=array();
		$userId = (int)$request['userid'];
		$ids=get_option('raw_hot_users');
		if(empty($ids))
		{
			return $users;
		}
		$ids = explode(',', $ids);
		$ids = implode(",", $ids);	
		$users=RAW_Util::get_users_byids($ids,$userId);
		$response = rest_ensure_response($users);
		return $response;
	}

	public function get_followRanking($request)
	{
		
		$follows=RAW_Util::get_followRanking();
		$response = rest_ensure_response($follows);
		return $response;
	}

	public function get_integralRanking($request)
	{
		$integral=RAW_Util::get_integralRanking();
		$response = rest_ensure_response($integral);
		return $response;
	}
	public function get_my_integral($request)
	{

		global $wpdb;
		$userId = (int)$request['userid'];
		$page = isset($request['page']) ? (int)$request['page'] : 1;
		$per_page = isset($request['per_page']) ? (int)$request['per_page'] : 20;
		$page = ($page - 1) * $per_page;
		$wpdb->minapper_integral = $wpdb->prefix . 'minapper_integral';
		$sql = "select  t.*, DATE_FORMAT(t.updatedate, '%Y-%m-%d %H:%i:%s') as integralDate  from " . $wpdb->minapper_integral . "  t where  t.userid=" . $userId . " ORDER BY t.updatedate desc  LIMIT " . $page . "," . $per_page;
		$integral = $wpdb->get_results($sql);
		$response = rest_ensure_response($integral);
		return $response;
	}

	public function set_invite_log($request)
	{
		global $wpdb;
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$invitecode = $request['invitecode']; // extkey-invitecode
		$openId = $request['openid']; //extvalue-openid
		$sql = $wpdb->prepare("select openid from " . $wpdb->minapper_weixin_users . " where invitecode=%s", $invitecode);
		$_openId = $wpdb->get_var($sql);
		if ($_openId != $openId) {
			//分享记录插入日志
			$wpdb->minapper_logs = $wpdb->prefix . 'minapper_logs';
			$data = array(
				'openid'   => $openId,
				'logtype'  => 'shareApp',
				'objectid' => $invitecode //invitecode
			);
			$insertLogsResult = $wpdb->insert($wpdb->minapper_logs, $data);
		}


		$shareAppResult = RAW_Util::share_app($invitecode, $openId);
		if (username_exists($openId)) {
			return new WP_Error('error', "已是会员" . "积分：" . $shareAppResult, array('status' => 200));
		}

		$sql = $wpdb->prepare("SELECT count(*) FROM " . $wpdb->minapper_ext . " where extype='invite'  and extvalue=%s and extid=1", $openId);
		$inviteuser = (int)$wpdb->get_var($sql);
		if ($inviteuser == 1) {
			return new WP_Error('error', "已确认邀请" . "积分：" . $shareAppResult, array('status' => 200));
		}

		$sql = $wpdb->prepare("SELECT extkey FROM " . $wpdb->minapper_ext . " where extype='invite'  and extvalue=%s and extid=0", $openId);
		$extkey = $wpdb->get_var($sql);
		if (!empty($extkey)) {
			if ($extkey != $invitecode) {

				$data = array(
					'extkey' => $invitecode

				);
				$where  = array('extvalue' => $openId, 'extype'  => 'invite');
				$result = $wpdb->update($wpdb->minapper_ext, $data, $where);
				if (empty($result)) {
					return new WP_Error('error', '更新邀请错误' . "积分：" . $shareAppResult, array('status' => 200,));
				} else {

					$response = array('success' => true, 'message' => '更新邀请成功' . "积分：" . $shareAppResult);
					return $response;
				}
			} else {
				$response = array('success' => true, 'message' => '无需更新邀请' . "积分：" . $shareAppResult);
				return $response;
			}
		} else {
			$sql = $wpdb->prepare("select userid from " . $wpdb->minapper_weixin_users . " where invitecode=%s", $invitecode);
			$userid = $wpdb->get_var($sql);
			if (empty($userid)) {
				return new WP_Error('error', '邀请码错误' . "积分：" . $shareAppResult, array('status' => 200));
			}

			$data = array(
				'extid'   => 0,
				'extype'  => 'invite',
				'extkey' => $invitecode,
				'extvalue' => $openId,
				'userid' => $userid

			);
			$insert = $wpdb->insert($wpdb->minapper_ext, $data);

			if ($insert) {
				$response = array('success' => true, 'message' => '插入邀请成功' . "积分：" . $shareAppResult);
				return $response;
			} else {
				return new WP_Error('error', '插入邀请错误' . "积分：" . $shareAppResult, array('status' => 200));
			}
		}
	}



	public function open_Ad_Video($request)
	{

		date_default_timezone_set('Asia/Shanghai');
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;
		$raw_openAd_count = empty(get_option("raw_openAd_count")) ? 0 : (int)get_option("raw_openAd_count");
		if (!empty($userId)) {
			$taskDate = date('Y-m-d', time());
			$meta_key = 'raw_user_openAdVideo_' . $taskDate;

			$raw_user_openAdVideo = (int)get_user_meta($userId, $meta_key, true);

			if (empty($raw_user_openAdVideo)) {
				$raw_user_openAdVideo = 1;
			} else {

				if (is_int($raw_user_openAdVideo)) {
					$raw_user_openAdVideo = $raw_user_openAdVideo;
				} else {
					$raw_user_openAdVideo = 1;
				}
				$raw_user_openAdVideo = $raw_user_openAdVideo + 1;
			}

			$result = true;
			$canOpenAdVideo = true;
			if ($raw_openAd_count != 0 && $raw_user_openAdVideo > $raw_openAd_count) {
				$canOpenAdVideo = false;
				return new WP_Error('error', '观看次数超过今日上限', array('status' => 500, 'raw_user_shareapp' => $raw_user_shareapp, 'canOpenAdVideo' => $canOpenAdVideo));
			}

			if ($raw_openAd_count != 0 && $raw_user_openAdVideo == $raw_openAd_count) {
				$canOpenAdVideo = false;
			}

			if (!update_user_meta($userId, $meta_key, $raw_user_openAdVideo)) {
				$result = add_user_meta($userId, $meta_key, $raw_user_openAdVideo, true);
			}
			if ($result == false) {
				return new WP_Error('error', '更新激励视频失败', array('status' => 400, 'raw_user_openAdVideo' => $raw_user_openAdVideo, 'canOpenAdVideo' => $canOpenAdVideo));
			}


			$message = '观看激励视频';
			$operateRewordIntegral = RAW_Util::operateRewordIntegral($userId, 'raw_openAd_integral', '', '', 'add');
			if ($operateRewordIntegral > 0) {

				$message = $message . ",获取" . $operateRewordIntegral . "积分奖励.";
			}
			$response = array('success' => true, 'message' => $message, 'operateRewordIntegral' => $operateRewordIntegral, 'raw_user_openAdVideo' => $raw_user_openAdVideo, 'canOpenAdVideo' => $canOpenAdVideo);
			$response = rest_ensure_response($response);
			return $response;
		} else {
			return new WP_Error('error', '用户参数错误', array('status' => 200));
		}
	}

	public function get_my_task($request)
	{
		date_default_timezone_set('Asia/Shanghai');
		$taskDate = date('Y-m-d', time());
		$signin_meta_key = 'raw_user_signin_' . $taskDate;
		$shareapp_meta_key = 'raw_user_shareapp_' . $taskDate;
		$openAdVideo_meta_key = 'raw_user_openAdVideo_' . $taskDate;

		$userId = (int)$request['userid'];

		$signined = false;
		$shareapped = false;
		$openAdVideoed = false;

		$canShareApp = false;
		$canOpenAdVideo = false;



		$shareappedCount = 0;
		$openAdVideoedCount = 0;

		$raw_user_shareapp_count = empty(get_user_meta($userId, $shareapp_meta_key, true)) ? 0 : (int)get_user_meta($userId, $shareapp_meta_key, true);
		if (!is_int($raw_user_shareapp_count)) {
			$raw_user_shareapp_count = 1;
		}
		$raw_user_openAdVideo_count = empty(get_user_meta($userId, $openAdVideo_meta_key, true)) ? 0 : (int)get_user_meta($userId, $openAdVideo_meta_key, true);
		if (!is_int($raw_user_openAdVideo_count)) {
			$raw_user_openAdVideo_count = 1;
		}


		if (get_user_meta($userId, $signin_meta_key, true) == $taskDate) {

			$signined = true;
		}
		$raw_openAd_count = empty(get_option('raw_openAd_count')) ? 0 : (int)get_option('raw_openAd_count');
		$raw_share_count = empty(get_option('raw_share_count')) ? 0 : (int)get_option('raw_share_count');
		if ($raw_openAd_count == 0) {

			$openAdVideoed = true;
			$canOpenAdVideo = true;
		} else if ($raw_openAd_count > $raw_user_openAdVideo_count) {
			$openAdVideoed = true;
			$canOpenAdVideo = true;
		}

		if ($raw_share_count == 0) {
			$shareapped = true;
			$canShareApp = true;
		} else if ($raw_share_count > $raw_user_shareapp_count) {

			$shareapped = true;
			$canShareApp = true;
		}
		$excitationAdId = empty(get_option('raw_excitation_ad_id')) ? '' : get_option('raw_excitation_ad_id');
		$task['signined'] = $signined;
		$task['shareapped'] = $shareapped;
		$task['openAdVideoed'] = $openAdVideoed;
		$task['excitationAdId'] = $excitationAdId;

		$task['canShareApp'] = $canShareApp;
		$task['canOpenAdVideo'] = $canOpenAdVideo;

		$task['shareappedCount'] = $raw_user_shareapp_count;
		$task['openAdVideoedCount'] = $raw_user_openAdVideo_count;
		$task['raw_user_max_integral'] = empty(get_option('raw_user_max_integral')) ? 0 : (int)get_option('raw_user_max_integral');



		$response = array('success' => true, 'message' => '获取任务成功', 'task' => $task);
		$response = rest_ensure_response($response);
		return $response;
	}


	public function  get_author_list($request)
	{
		$cachedata = '';
		if (function_exists('MRAC')) {
			$cachedata = MRAC()->cacheManager->get_cache();
			if (!empty($cachedata)) {
				$response = rest_ensure_response($cachedata);
				return $response;
			}
		}
		global $wpdb;
		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;

		$page = isset($request['page']) ? (int)$request['page'] : 1;
		$per_page = isset($request['per_page']) ? (int)$request['per_page'] : 50;
		$page = ($page - 1) * $per_page;

		$checkUser = RAW_Util::checkUser($sessionId, $userId);

		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';

		$sql = $wpdb->prepare("SELECT  t.extid,  
					(SELECT  count(1) from " . $wpdb->posts . " p where p.post_type='post' and (p.post_status='publish' or p.post_status='pending')  and p.post_author=t.extid) as postcount,
					(SELECT  count(1) from " . $wpdb->posts . " c where c.post_type='topic' and ( c.post_status='publish' or  c.post_status='pending') and c.post_author=t.extid ) as topiccount,
					(
						SELECT
							u.avatarurl
						FROM
							" . $wpdb->minapper_weixin_users . " u
						WHERE
							u.userid = t.extid
					) AS avatarurl,
					(
						SELECT
							u.`member`
						FROM
							" . $wpdb->minapper_weixin_users . " u
						WHERE
							u.userid = t.extid
					) AS `member`,
				    (
						SELECT
							u1.display_name
						FROM
							" . $wpdb->users . " u1
						WHERE
							u1.id = t.extid
					) AS nickname,
				(SELECT count(1) from " . $wpdb->minapper_ext . " e where e.extid=t.extid and e.extype='follow') as followmecount,
				(SELECT count(1) from " . $wpdb->minapper_ext . " e where e.userid=t.extid and e.extype='follow' and e.extid !=t.userid) as myfollowcount,
				(SELECT if(count(1)=1,'true','false' )   from " . $wpdb->minapper_ext . " t2 where t2.userid=t.extid and t2.extid= " . $userId . " and t2.extype='follow')  as followeachoher
				FROM
					" . $wpdb->minapper_ext . " t
				WHERE
					extype = 'follow'  and t.userid=".$userId."
				GROUP BY t.extid limit %d,%d", $page, $per_page);
		$_authorList = $wpdb->get_results($sql);
		$authorList = array();
		foreach ($_authorList as $_author) {
			$author['post_author'] = $_author->extid;
			$author['postcount'] = $_author->postcount;
			$author['topiccount'] = $_author->topiccount;
			$author['nickname'] = $_author->nickname;
			$author['followmecount'] = $_author->followmecount;
			$author['myfollowcount'] = $_author->myfollowcount;
			$author['followeachoher'] = $_author->followeachoher;
			$member = empty($_author->member) ? "10" : $_author->member;
			$author['member'] = empty($_author->member) ? "10" : $_author->member;
			$author["membername"] = RAW_Util::getMemberName($member);
			$author_id = (int)$_author->extid;
			if ($checkUser) {
				$follow = RAW_Util::getUserFollow($sessionId, $userId, $author_id);
				$author['follow'] = $follow ? "true" : "false";
			} else {
				$author['follow'] = 'false';
			}

			if (empty($_author->avatarurl)) {
				$minapper_avatar = get_user_meta((int)$author_id, 'minapper_avatar', true);
				if (!empty($minapper_avatar)) {
					$author_avatar = $minapper_avatar;
				} else {
					$author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
				}
				$author['avatarurl'] = $author_avatar;
			} else {
				$author['avatarurl'] = $_author->avatarurl;
			}

			if ($checkUser) {
				if ($userId != $author_id) {
					$authorList[] = $author;
				}
			} else {
				$authorList[] = $author;
			}
		}
		if ($cachedata == '' && function_exists('MRAC')) {

			$cachedata = MRAC()->cacheManager->set_cache($authorList, 'myAuthorList', 0);
		}
		$response = rest_ensure_response($authorList);
		return $response;
	}

	public function get_my_zan_images($request)
	{

		$userId = (int)$request['userid'];
		$zanImageUrl = get_user_meta($userId, 'zanimage', true);
		$raw_praise_word = empty(get_option('raw_praise_word')) ? "鼓励" : get_option('raw_praise_word');
		if (empty($zanImageUrl)) {
			return new WP_Error('error', "找不到赞赏图片", array('status' => 404, 'raw_praise_word' => $raw_praise_word));
		}
		$response = array('success' => true, 'message' => '获取我的赞赏码成功', 'zanImageUrl' => $zanImageUrl, 'raw_praise_word' => $raw_praise_word);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function get_follow_me_authors($request)
	{
		global $wpdb;
		$userId = (int)$request['userid'];
		$page = isset($request['page']) ? (int)$request['page'] : 1;
		$per_page = isset($request['per_page']) ? (int)$request['per_page'] : 20;
		$page = ($page - 1) * $per_page;
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$sql = $wpdb->prepare("SELECT u.userid as post_author ,u.nickname,u.avatarurl,u.`member`,
		(SELECT  count(1) from " . $wpdb->posts . " p where p.post_type='post' and (p.post_status='publish' or p.post_status='pending')  and p.post_author=u.userid) as postcount,
		(SELECT  count(1) from " . $wpdb->posts . " c where c.post_type='topic' and ( c.post_status='publish' or  c.post_status='pending') and c.post_author=u.userid ) as topiccount,
		(SELECT if(count(1)=1,'true','false' )   from " . $wpdb->minapper_ext . " t2 where t2.userid=%d and t2.extid= u.userid and t2.extype='follow')  as followeachoher,
	    (select if(count(1)=1,'true','false' )  from " . $wpdb->minapper_ext . " f where f.userid=%d and f.extid=u.userid and f.extype='follow') as follow,
		(SELECT count(1) from " . $wpdb->minapper_ext . " e where e.extid=u.userid and e.extype='follow') as followmecount,
				(SELECT count(1) from " . $wpdb->minapper_ext . " e where e.userid=u.userid and e.extype='follow') as myfollowcount
		
		  from " . $wpdb->minapper_ext . "  t , " . $wpdb->minapper_weixin_users . " u where  t.extid=%d and  t.extype='follow'  and t.userid=u.userid LIMIT %d,%d", $userId, $userId, $userId, $page, $per_page);
		$_followmeAuthors = $wpdb->get_results($sql);
		$followmeAuthors = array();
		if (!empty($_followmeAuthors)) {
			foreach ($_followmeAuthors as $user) {
				$membername = RAW_Util::getMemberName($user->member);
				$user->membername = $membername;
				$followmeAuthors[] = $user;
			}
		}
		$response = array('success' => true, 'message' => '获取关注我的粉丝成功', 'followmeAuthors' => $followmeAuthors);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function get_my_follow_authors($request)
	{
		global $wpdb;
		$userId = (int)$request['userid'];
		$page = isset($request['page']) ? (int)$request['page'] : 1;
		$per_page = isset($request['per_page']) ? (int)$request['per_page'] : 20;

		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$page = ($page - 1) * $per_page;
		$sql = $wpdb->prepare("SELECT t.extid,t.userid,
		(
			SELECT
				u.avatarurl
			FROM
				" . $wpdb->minapper_weixin_users . " u
			WHERE
				u.userid = t.extid
		) AS avatarurl,	
		 (
			SELECT
				u.`member`
			FROM
				" . $wpdb->minapper_weixin_users . " u
			WHERE
				u.userid = t.extid
		) AS `member`,
		 (
			SELECT
				u1.display_name
			FROM
				" . $wpdb->users . " u1
			WHERE
				u1.id = t.extid
		) AS nickname,
		(SELECT  count(1) from " . $wpdb->posts . " p where p.post_type='post' and p.post_status='publish' and p.post_author= t.extid) as postcount,
		(SELECT  count(1) from " . $wpdb->posts . " c where c.post_type='topic' and c.post_status='publish' and c.post_author= t.extid) as topiccount,
		(SELECT if(count(1)=1,'true','false' )   from " . $wpdb->minapper_ext . " t2 where t.extid= t2.userid and t2.extid=%d and t2.extype='follow')  as followeachoher,
		(select if(count(1)=1,'true','false' )  from " . $wpdb->minapper_ext . " f where f.extid=t.extid and f.userid=%d and f.extype='follow') as follow,
		(SELECT count(1) from " . $wpdb->minapper_ext . " e where e.extid= t.extid and e.extype='follow') as followmecount,
		(SELECT count(1) from " . $wpdb->minapper_ext . " e where e.userid= t.extid and e.extype='follow') as myfollowcount  
		from " . $wpdb->minapper_ext . "  t  where  t.userid=%d and  t.extype='follow'  LIMIT %d,%d", $userId, $userId, $userId, $page, $per_page);
		$_myFollowAuthors = $wpdb->get_results($sql);

		$myFollowAuthors = array();

		foreach ($_myFollowAuthors as $_author) {

			$author['post_author'] = $_author->extid;


			$author['nickname'] = $_author->nickname;
			if (empty($_author->nickname)) {
				$myuser = get_user_by("id", (int)$_author->extid);
				$author['nickname'] = $myuser->display_name;
			}

			$author['avatarurl'] = $_author->avatarurl;
			if (empty($_author->avatarurl)) {
				$minapper_avatar = get_user_meta((int)$_author->extid, 'minapper_avatar', true);
				if (!empty($minapper_avatar)) {
					$author_avatar = $minapper_avatar;
				} else {
					$author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
				}
				$author['avatarurl'] = $author_avatar;
			}

			$author['postcount'] = $_author->postcount;
			$author['follow'] = $_author->follow;
			$author['topiccount'] = $_author->topiccount;
			$author['followeachoher'] = $_author->followeachoher;
			$author['followmecount'] = $_author->followmecount;
			$author['myfollowcount'] = $_author->myfollowcount;
			$member = empty($_author->member) ? "10" : $_author->member;
			$author['member'] = empty($_author->member) ? "10" : $_author->member;
			$author["membername"] = RAW_Util::getMemberName($member);

			$author_id = (int)$_author->extid;
			if (empty($_author->avatarurl)) {
				$minapper_avatar = get_user_meta((int)$author_id, 'minapper_avatar', true);
				if (!empty($minapper_avatar)) {
					$author_avatar = $minapper_avatar;
				} else {
					$author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
				}
				$author['avatarurl'] = $author_avatar;
			} else {
				$author['avatarurl'] = $_author->avatarurl;
			}
			$myFollowAuthors[] = $author;
		}



		$response = array('success' => true, 'message' => '获取关注的TA成功', 'myFollowAuthors' => $myFollowAuthors);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function user_follow($request)
	{
		$id = isset($request['id']) ? (int)$request['id'] : 0;
		$userId = (int)$request['userid'];
		$user = get_user_by('ID', $id);
		if (empty($user)) {
			return new WP_Error('error', "关注或取消的用户id错误", array('status' => 500));
		}

		$data_array = array(
			'userid'   => $userId,
			'extid'   => $id,
			'extype'  => 'follow',
			'extkey' => '',
			'extvalue' => ''

		);
		global $wpdb;
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$sql = $wpdb->prepare("select count(1) from " . $wpdb->minapper_ext . " t where t.userid=%d and t.extid=%d and extype='follow'", $userId, $id);
		$count = (int)$wpdb->get_var($sql);

		if ($count == 1) {


			$response = array('success' => true, 'message' => '你已经关注TA');
			$response = rest_ensure_response($response);
			return $response;
		}

		$inserUserFollowId = $wpdb->insert($wpdb->minapper_ext, $data_array);
		if (!$inserUserFollowId) {
			return new WP_Error('error', "关注用户失败", array('status' => 500));
		} else {
			if (function_exists('MRAC')) {

				$cachedata = MRAC()->cacheManager->delete_cache('myAuthorList', 0);
			}
			$messageData = array(
				'fromid' => $userId,
				'userid' => $id,
				"messagetype" => "follow",
				'messagevalue' => "关注了你"
			);
			$sendMessageResut = RAW_Util::send_message($messageData);
			$response = array('success' => true, 'message' => '成功关注TA');
			$response = rest_ensure_response($response);
			return $response;
		}
	}

	public function user_unfollow($request)
	{
		$id = isset($request['id']) ? (int)$request['id'] : 0;
		$user = get_user_by('ID', $id);
		$userId = (int)$request['userid'];
		if (empty($user)) {
			return new WP_Error('error', "关注或取消的用户id错误", array('status' => 500));
		}
		global $wpdb;
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$sql = "delete from " . $wpdb->minapper_ext . "  where userid=" . $userId . " and extid=" . $id . " and extype='follow'";
		$result = $wpdb->query($sql);
		if (!$result) {
			return new WP_Error('error', "取消关注失败", array('status' => 500));
		} else {
			if (function_exists('MRAC')) {

				$cachedata = MRAC()->cacheManager->delete_cache('myAuthorList', 0);
			}
			$response = array('success' => true, 'message' => '已取消关注TA');
			$response = rest_ensure_response($response);
			return $response;
		}
	}

	/**
	 * Logs in a user.
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 * @return user
	 */
	public function login($request)
	{
		global $wpdb;
		date_default_timezone_set('Asia/Shanghai');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		// js_code 换取 openid
		$api_url = add_query_arg(array(
			'appid' => get_option('raw_appid'),
			'secret' => get_option('raw_secret'),
			'js_code' => $request['js_code'],
			'grant_type' => 'authorization_code'
		), 'https://api.weixin.qq.com/sns/jscode2session');

		$api_response = wp_remote_get($api_url);
		if (!is_array($api_response) || is_wp_error($api_response) || $api_response['response']['code'] != '200') {
			return new WP_Error('error', 'API错误1：' . json_encode($api_response), array('status' => 400));
		}

		$api_result = json_decode($api_response['body'], true);
		if (empty($api_result['openid']) || empty($api_result['session_key']) || !empty($api_result['errcode'])) {
			return new WP_Error('error', 'API错误2：' . json_encode($api_result), array('status' => 400));
		}

		// 会话
		$raw_session = RAW_Util::generate_session();
		$invitecode = RAW_Util::generate_invitecode();

		if (!$raw_session) {
			return new WP_Error('error', 'Session生成错误', array('status' => 400));
		}

		$err_code = RAW_Util::decrypt_data(get_option('raw_appid'), $api_result['session_key'], $request['encryptedData'], $request['iv'], $data);

		if ($err_code != 0) {
			return new WP_Error('error', '解密错误：' . $err_code, array('status' => 400));
		}

		$data = json_decode($data, true);
		//$response = rest_ensure_response( $response);
		$userId = 0;
		$openId = $api_result['openid'];
		$nickname = RAW_Util::filterEmoji($data['nickName']);

		$_nickname = base64_encode($nickname);
		$_nickname = strlen($_nickname) > 49 ? substr($_nickname, 49) : $_nickname;
		// 创建用户
		if (!username_exists($openId)) {
			$new_user_data = apply_filters('new_user_data', array(
				'user_login'    => $openId,
				'first_name'	=> $nickname,
				'nickname'      => $nickname,
				'user_nicename' => $_nickname,
				'display_name'  => $nickname,
				'user_pass'     => $openId,
				'user_email'    => $openId . '@weixin.com'
			));

			$userId = wp_insert_user($new_user_data);
			if (is_wp_error($userId) || empty($userId) ||  $userId == 0) {
				return new WP_Error('error', '插入wordpress用户错误：', array('status' => 400));
			}
		}
		//更新用户
		else {
			$user = get_user_by('login', $openId);
			$userdata = array(
				'ID'            => $user->ID,
				'first_name'	=> $nickname,
				'nickname'      => $nickname,
				'user_nicename' => $_nickname,
				'display_name'  => $nickname
			);

			$userId = wp_update_user($userdata);
			if (is_wp_error($userId)) {
				return new WP_Error('error', '更新wp用户错误：', array('status' => 400));
			}
		}

		$sql = $wpdb->prepare("select count(1) from " . $wpdb->minapper_weixin_users . " where openid=%s", $openId);
		$count = (int)$wpdb->get_var($sql);
		if ($count == 0) {
			//未绑定开放平台无法获取unionId            
			$unionId = empty($data['unionId']) ? '' : $data['unionId'];
			$avatarUrl = $data['avatarUrl'];

			if (empty($avatarUrl)) {
				$avatarUrl = plugin_dir_url(__FILE__) . "images/gravatar.png";
			} else {
				$avatarUrl = strpos($avatarUrl, "wx.qlogo.cn") ? $avatarUrl : plugin_dir_url(__FILE__) . "images/gravatar.png";
			}
			$integral = 0;
			$raw_user_initial_integral = get_option('raw_user_initial_integral');
			if (!empty($raw_user_initial_integral)) {
				$integral = (int)$raw_user_initial_integral;
			}
			$nickname = RAW_Util::filterEmoji($data['nickName']);
			$data_array = array(
				'openid'   => $openId,
				'userid'   => $userId,
				'unionid'  => $unionId,
				'nickname' => $nickname,
				'language' => $data['language'],
				'gender'   => $data['gender'],
				'city'     => $data['city'],
				'province' => $data['province'],
				'country'  => $data['country'],
				'gender'   => $data['gender'],
				'avatarUrl' => $avatarUrl,
				'sessionid'  => $raw_session['sessionId'],
				'member' => '10',
				'integral' => $integral,
				'memberbegindate' => date('Y-m-d H:i:s', time()),
				'creatdate' => date('Y-m-d H:i:s', time()),
				'sessionexpire' => date('Y-m-d H:i:s', time() + 2592000),
				'invitecode' => $invitecode


			);
			$insertUserResult = $wpdb->insert($wpdb->minapper_weixin_users, $data_array);

			if ($insertUserResult === false) {
				return new WP_Error('error', '插入微信用户错误', array('status' => 400));
			}

			//extkey- 邀请者的invitecode
			//extvalue-被邀请者的openid
			//userid -邀请者的userid
			//extid- 0 未被确认  1 被确认
			//extype -'invite' 扩展类型
			//extkey-invitecode 邀请码 
			$sql = $wpdb->prepare("SELECT extkey,(select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as inviteuserid , (select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as userid FROM " . $wpdb->minapper_ext . " where extype='invite'  and extvalue=%s and extid=0", $openId);
			$inviteuser = $wpdb->get_row($sql);
			$invitecode = '';
			$inviteuserId = 0;
			$updateinvate = false;
			if (!empty($inviteuser)) {
				$invitecode = $inviteuser->extkey;
				$inviteuserId = $inviteuser->inviteuserid;

				if (!empty($invitecode) && !empty($inviteuserId)) {

					$where  = array('extkey' => $invitecode, 'extvalue' => $openId, 'extid'  => 0, 'extype' => 'invite');
					$data = array(
						'extid' => 1,
						'userid' => $inviteuserId
					);
					$updateinvate = $wpdb->update($wpdb->minapper_ext, $data, $where);
				}
			}

			if (!empty($updateinvate) && !empty($inviteuserId)) {
				$operateRewordIntegral = RAW_Util::operateRewordIntegral((int)$inviteuserId, 'raw_invite_integral', $commentId, '', 'add');
			}
		} elseif ($count == 1) {
			//未绑定开放平台无法获取unionId            
			$unionId = empty($data['unionId']) ? '' : $data['unionId'];
			$nickname = RAW_Util::filterEmoji($data['nickName']);
			$sql = $wpdb->prepare("select invitecode from " . $wpdb->minapper_weixin_users . " where openid=%s", $openId);
			$userinvitecode = $wpdb->get_var($sql);
			if (empty($userinvitecode)) {
				$data_array = array(
					'nickname' => $nickname,
					'gender'   => $data['gender'],
					'unionid'  => $unionId,
					'userid'   => $userId,
					'city'     => $data['city'],
					'province' => $data['province'],
					'country'  => $data['country'],
					'avatarUrl' => $data['avatarUrl'],
					'sessionid'  => $raw_session['sessionId'],
					'sessionexpire' => date('Y-m-d H:i:s', time() + 2592000),
					'invitecode' => $invitecode
				);
			} else {
				$data_array = array(
					'nickname' => $nickname,
					'gender'   => $data['gender'],
					'unionid'  => $unionId,
					'userid'   => $userId,
					'city'     => $data['city'],
					'province' => $data['province'],
					'country'  => $data['country'],
					'avatarUrl' => $data['avatarUrl'],
					'sessionid'  => $raw_session['sessionId'],
					'sessionexpire' => date('Y-m-d H:i:s', time() + 2592000)
				);
			}

			$where  = array('openid' => $openId);
			$updateUserResult = $wpdb->update($wpdb->minapper_weixin_users, $data_array, $where);

			if ($updateUserResult == false) {
				return new WP_Error('error', '更新微信用户错误：' . $updateUserResult, array('status' => 400));
			}
		} else {
			return new WP_Error('error', '微信用户表异常：', array('status' => 400));
		}
		$raw_session['userId'] = $userId;
		$response = array('raw_session' => $raw_session, 'operateRewordIntegral' => $operateRewordIntegral);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function getOpenId($request)
	{
		$js_code = $request["js_code"];
		$weixinSession = RAW()->wxapi->get_jscode2session($js_code);
		if (!empty($weixinSession['errcode']) &&  $weixinSession['errcode'] != 0) {
			return new WP_Error($weixinSession['errcode'], '获取session_key错误:' . $weixinSession['errmsg'], array('status' => 500));
		}
		$result = array();
		$result['openid'] = $weixinSession;
		$response = rest_ensure_response($result);
		return $response;
	}



	public function wxlogin($request)
	{

		global $wpdb;
		date_default_timezone_set('Asia/Shanghai');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';

		$js_code = $request['js_code'];
		$appid = get_option('raw_appid');
		$encryptedData = $request['encryptedData'];
		$iv = $request['iv'];
		$weixinSession = RAW()->wxapi->get_jscode2session($js_code);
		if (!empty($weixinSession['errcode']) &&  $weixinSession['errcode'] != 0) {
			return new WP_Error($weixinSession['errcode'], '获取session_key错误:' . $weixinSession['errmsg'], array('status' => 500));
		}
		$session_key = $weixinSession['session_key'];
		$openId = $weixinSession['openid'];

		$err_code = RAW_Util::decrypt_data(get_option('raw_appid'), $session_key, $encryptedData, $iv, $data);

		if ($err_code != 0) {
			return new WP_Error('error', '解密错误：' . $err_code . $weixinSession, array('status' => 400));
		}
		$data = json_decode($data, true);

		$unionId = empty($data['unionId']) ? '' : $data['unionId'];
		$nickname = RAW_Util::filterEmoji($data['nickName']);
		$_nickname = base64_encode($nickname);
		$_nickname = strlen($_nickname) > 49 ? substr($_nickname, 49) : $_nickname;
		$user_email = $openId . '@weixin.com';

		$userId = 0;
		$userDeviceInfo = array();

		$unionId = empty($data['unionId']) ? '' : $data['unionId'];
		$avatarUrl = $data['avatarUrl'];
		$language = $data['language'];
		$gender  = $data['gender'];
		$city     = $data['city'];
		$province = $data['province'];
		$country  = $data['country'];
		$gender   = $data['gender'];

		if (empty($avatarUrl)) {
			//$avatarUrl=plugin_dir_url(__FILE__ )."images/gravatar.png";
			$avatarUrl = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . 'images/gravatar.png';
		} else {
			$avatarUrl = strpos($avatarUrl, "wx.qlogo.cn") ? $avatarUrl : plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . 'images/gravatar.png';
		}

		// 创建用户
		if (!username_exists($openId)) {

			$userId = RAW_Util::insertWPUser($openId, $nickname, $nickname, $_nickname, $nickname, $user_email, null);

			if ($userId == 0) {
				return new WP_Error('error', '插入wordpress用户错误：', array('status' => 500));
			}
			//$userDeviceInfo=RAW_Util::getUserDeviceInfo($userId,'');
		} else {
			$user = get_user_by('login', $openId);
			$userId = RAW_Util::updateWPUser($user->ID, $nickname, $nickname, $_nickname, $nickname);
			if ($userId == 0) {
				return new WP_Error('error', '更新wordpress用户错误：', array('status' => 500));
			}
			//$userDeviceInfo=RAW_Util::getUserDeviceInfo($userId,'');
		}
		// 会话
		$raw_session = RAW_Util::generate_session();
		$sessionExpire = $raw_session['sessionExpire'];
		$sessionId = $raw_session['sessionId'];
		$sql = $wpdb->prepare("select * from " . $wpdb->minapper_weixin_users . " where openid=%s", $openId);
		$weixin_users = $wpdb->get_row($sql);
		if (empty($weixin_users)) {
			$integral = 0;
			$raw_user_initial_integral = get_option('raw_user_initial_integral');
			if (!empty($raw_user_initial_integral)) {
				$integral = (int)$raw_user_initial_integral;
			}


			$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, '');
			$device = empty($userDeviceInfo['device']) ? array() : $userDeviceInfo['device'];
			$model = empty($device['model']) ? "" : $device['model'];
			$platform = empty($device['platform']) ? "" : $device['platform'];
			$brand = empty($device['brand']) ? "" : $device['brand'];
			$deviceId = empty($userDeviceInfo['deviceId']) ? "" : $userDeviceInfo['deviceId'];



			$invitecode = RAW_Util::generate_invitecode();
			$data_user = array(
				'openid'   => $openId,
				'userid'   => $userId,
				'unionid'  => $unionId,
				'nickname' => $nickname,
				'language' => $language,
				'gender'   => $gender,
				'city'     => $city,
				'province' => $province,
				'country'  => $country,
				'gender'   => $gender,
				'avatarUrl' => $avatarUrl,
				'member' => '10',
				'integral' => $integral,
				'memberbegindate' => date('Y-m-d H:i:s', time()),
				'creatdate' => date('Y-m-d H:i:s', time()),
				'invitecode' => $invitecode
			);


			$data_session = array(
				'userid'   => $userId,
				'deviceid'   => $deviceId,
				'sessionid'   => $sessionId,
				'sessionexpire' => $sessionExpire,
				'platform'   => $platform,
				'brand'   => $brand,
				'model'   => $model
			);

			$result = RAW_Util::insertMinapperUser($data_user, $data_session, $openId);
			if ($result['errcode'] != 0) {
				return new WP_Error('error', $result, array('status' => 500));
			}

			//extkey- 邀请者的invitecode
			//extvalue-被邀请者的openid
			//userid -邀请者的userid
			//extid- 0 未被确认  1 被确认
			//extype -'invite' 扩展类型
			//extkey-invitecode 邀请码 
			$sql = $wpdb->prepare("SELECT extkey,(select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as inviteuserid , (select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as userid FROM " . $wpdb->minapper_ext . " where extype='invite'  and extvalue=%s and extid=0", $openId);
			$inviteuser = $wpdb->get_row($sql);
			$invitecode = '';
			$inviteuserId = 0;
			$updateinvate = false;
			if (!empty($inviteuser)) {
				$invitecode = $inviteuser->extkey;
				$inviteuserId = $inviteuser->inviteuserid;

				if (!empty($invitecode) && !empty($inviteuserId)) {

					$where  = array('extkey' => $invitecode, 'extvalue' => $openId, 'extid'  => 0, 'extype' => 'invite');
					$data = array(
						'extid' => 1,
						'userid' => $inviteuserId
					);
					$updateinvate = $wpdb->update($wpdb->minapper_ext, $data, $where);
				}
			}

			if (!empty($updateinvate) && !empty($inviteuserId)) {
				$operateRewordIntegral = RAW_Util::operateRewordIntegral((int)$inviteuserId, 'raw_invite_integral', $commentId, '', 'add');
			}
		} else {
			$data_user = array(
				'nickname' => $nickname,
				'language' => $language,
				'gender'   => $gender,
				'city'     => $city,
				'province' => $province,
				'country'  => $country,
				'gender'   => $gender,
				'avatarUrl' => $avatarUrl,
				'updatedate' => date('Y-m-d H:i:s', time())

			);
			$_sessionId = $weixin_users->sessionid;

			$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, $_sessionId);
			$device = empty($userDeviceInfo['device']) ? array() : $userDeviceInfo['device'];
			$model = empty($device['model']) ? "" : $device['model'];
			$platform = empty($device['platform']) ? "" : $device['platform'];
			$brand = empty($device['brand']) ? "" : $device['brand'];
			$deviceId = empty($userDeviceInfo['deviceId']) ? "" : $userDeviceInfo['deviceId'];
			$haveSessionData = $userDeviceInfo['haveSessionData'];
			//小程序端是新版本的老用户				 
			if ($haveSessionData) {
				$data_session = array(
					'sessionid'   => $sessionId,
					'sessionexpire' => $sessionExpire
				);
			}
			//小程序端是新版本的新用户
			if (!$haveSessionData) {
				$data_session = array(
					'userid'   => $userId,
					'deviceid'   => $deviceId,
					'sessionid'   => $sessionId,
					'sessionexpire' => $sessionExpire,
					'platform'   => $platform,
					'brand'   => $brand,
					'model'   => $model
				);
			}
			$result = RAW_Util::updateMinapperUser($data_user, $data_session, $openId, $userId, $deviceId, $haveSessionData);
			if ($result['errcode'] != 0) {
				return new WP_Error('error', $result, array('status' => 500));
			}
		}
		$raw_session['userId'] = $userId;
		$response = array('raw_session' => $raw_session, 'device' => $device);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function dountwxlogin($request)
	{

		global $wpdb;
		date_default_timezone_set('Asia/Shanghai');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';

		$code = $request['code'];
		$type = $request['type'];
		$wxUserInfo = RAW()->wxapi->get_donut_code2verifyinfo($code);
		if (!empty($wxUserInfo['errcode']) && $wxUserInfo['errcode'] != 0) {
			return new WP_Error($wxUserInfo['errcode'], '获取用户信息错误:' . $wxUserInfo['errmsg'], array('status' => 500));
		}

		$userInfo = $wxUserInfo['user_info'];
		$login_info=$wxUserInfo['login_info'];
		$loginType=$login_info['type'];
		$openId='';
		$unionId='';
		$nickname ='';
		$wxNickname ='';
		$wxAvatarUrl ='';
		if($loginType=='weixinApp')
		{
			$data = $userInfo['openapp_info'];
			$openId = $data['openid'];
			$unionId = $data['unionid'];
			$nickname = '微信用户';
			$wxNickname = $data['nickname'];
			$wxAvatarUrl = $data['headimgurl'];
		}
		

		// 小程序登录
		if ($loginType=='weixinMiniProgram') {
			$miniprogram_info = $userInfo['miniprogram_info'];
			$openId = $miniprogram_info['openid'];
			$unionId = $miniprogram_info['unionid'];
			$nickname = '小程序用户';
		}
		// 苹果apple登录
		if ($loginType=='apple') {
			$apple_info = $userInfo['apple_info'];
			$openId = $apple_info['apple_user_id'];
			$nickname = '苹果用户';
		}
		$_nickname = '';		
		if(!empty($wxNickname) && $wxNickname != '微信用户')
		{
			$nickname = $wxNickname;
			$avatarUrl = $wxAvatarUrl;
			$nickname = RAW_Util::filterEmoji($nickname);
			$_nickname = base64_encode($nickname);
			$_nickname = strlen($_nickname) > 49 ? substr($_nickname, 49) : $_nickname;
		}
		else
		{
			$avatarUrl = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
			$nickname .=RAW_Util::randString();
			$_nickname = $nickname;
		}
		$user_email = $openId . '@weixin.com';
		$userId = 0;
		$userDeviceInfo = array();
		$language = empty($data['language']) ? '' : $data['language'];
		$gender  = empty($data['gender']) ? '' : $data['gender'];
		$city     = empty($data['city']) ? '' : $data['city'];
		$province = empty($data['province']) ? '' : $data['province'];
		$country  = empty($data['country']) ? '' : $data['country'];
		$gender   = empty($data['gender']) ? '' : $data['gender'];

		// 创建用户
		if (!username_exists($openId)) {

			$userId = RAW_Util::insertWPUser($openId, $nickname, $nickname, $_nickname, $nickname, $user_email, null);

			if ($userId == 0) {
				return new WP_Error('error', '插入wordpress用户错误：', array('status' => 500));
			}
			update_user_meta($userId, 'unionId', $unionId);
			//$userDeviceInfo=RAW_Util::getUserDeviceInfo($userId,'');
		} else {
			$user = get_user_by('login', $openId);
			$userId=$user->ID;
			if(!empty($wxNickname) && $wxNickname !='微信用户' )
			{
				$Id = RAW_Util::updateWPUser($user->ID, $nickname, $nickname, $_nickname, $nickname);
				if ($Id == 0) {
					return new WP_Error('error', '更新wordpress用户错误：', array('status' => 500));
				}
			}
			if (delete_user_meta($userId, 'unionId')) {
				update_user_meta($userId, 'unionId', $unionId);
			}
			//$userDeviceInfo=RAW_Util::getUserDeviceInfo($userId,'');
		}
		//会话
		$raw_session = RAW_Util::generate_session();
		$sessionExpire = $raw_session['sessionExpire'];
		$sessionId = $raw_session['sessionId'];
		$sql = $wpdb->prepare("select * from " . $wpdb->minapper_weixin_users . " where openid=%s", $openId);
		$weixin_users = $wpdb->get_row($sql);
		if (empty($weixin_users)) {
			$integral = 0;
			$raw_user_initial_integral = get_option('raw_user_initial_integral');
			if (!empty($raw_user_initial_integral)) {
				$integral = (int)$raw_user_initial_integral;
			}


			$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, '');
			$device = empty($userDeviceInfo['device']) ? array() : $userDeviceInfo['device'];
			$model = empty($device['model']) ? "" : $device['model'];
			$platform = empty($device['platform']) ? "" : $device['platform'];
			$brand = empty($device['brand']) ? "" : $device['brand'];
			$deviceId = empty($userDeviceInfo['deviceId']) ? "" : $userDeviceInfo['deviceId'];



			$invitecode = RAW_Util::generate_invitecode();
			$data_user = array(
				'openid'   => $openId,
				'userid'   => $userId,
				'unionid'  => $unionId,
				'nickname' => $nickname,
				'language' => $language,
				'gender'   => $gender,
				'city'     => $city,
				'province' => $province,
				'country'  => $country,
				'gender'   => $gender,
				'avatarUrl' => $avatarUrl,
				'member' => '10',
				'integral' => $integral,
				'memberbegindate' => date('Y-m-d H:i:s', time()),
				'creatdate' => date('Y-m-d H:i:s', time()),
				'invitecode' => $invitecode
			);


			$data_session = array(
				'userid'   => $userId,
				'deviceid'   => $deviceId,
				'sessionid'   => $sessionId,
				'sessionexpire' => $sessionExpire,
				'platform'   => $platform,
				'brand'   => $brand,
				'model'   => $model,
				'ip'      =>RAW_Util::get_client_ip()
			);

			$result = RAW_Util::insertMinapperUser($data_user, $data_session, $openId);
			if ($result['errcode'] != 0) {
				return new WP_Error('error', $result, array('status' => 500));
			}

			//extkey- 邀请者的invitecode
			//extvalue-被邀请者的openid
			//userid -邀请者的userid
			//extid- 0 未被确认  1 被确认
			//extype -'invite' 扩展类型
			//extkey-invitecode 邀请码 
			$sql = $wpdb->prepare("SELECT extkey,(select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as inviteuserid , (select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as userid FROM " . $wpdb->minapper_ext . " where extype='invite'  and extvalue=%s and extid=0", $openId);
			$inviteuser = $wpdb->get_row($sql);
			$invitecode = '';
			$inviteuserId = 0;
			$updateinvate = false;
			if (!empty($inviteuser)) {
				$invitecode = $inviteuser->extkey;
				$inviteuserId = $inviteuser->inviteuserid;

				if (!empty($invitecode) && !empty($inviteuserId)) {

					$where  = array('extkey' => $invitecode, 'extvalue' => $openId, 'extid'  => 0, 'extype' => 'invite');
					$data = array(
						'extid' => 1,
						'userid' => $inviteuserId
					);
					$updateinvate = $wpdb->update($wpdb->minapper_ext, $data, $where);
				}
			}

			if (!empty($updateinvate) && !empty($inviteuserId)) {
				$operateRewordIntegral = RAW_Util::operateRewordIntegral((int)$inviteuserId, 'raw_invite_integral', $commentId, '', 'add');
			}
		} else {
			$data_user=array();
			if(!empty($wxNickname) && $wxNickname !='微信用户' )
			{
				$data_user = array(
					'unionid'  => $unionId,
					'nickname' => $nickname,
					'language' => $language,
					'gender'   => $gender,
					'city'     => $city,
					'province' => $province,
					'country'  => $country,
					'gender'   => $gender,
					'avatarUrl' => $avatarUrl,
					'updatedate' => date('Y-m-d H:i:s', time())
	
				);
			}
			else
			{
				$data_user = array(					
					'updatedate' => date('Y-m-d H:i:s', time())
	
				);
			}
			
			$_sessionId = $weixin_users->sessionid;
			$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, $_sessionId);
			$device = empty($userDeviceInfo['device']) ? array() : $userDeviceInfo['device'];
			$model = empty($device['model']) ? "" : $device['model'];
			$platform = empty($device['platform']) ? "" : $device['platform'];
			$brand = empty($device['brand']) ? "" : $device['brand'];
			$deviceId = empty($userDeviceInfo['deviceId']) ? "" : $userDeviceInfo['deviceId'];
			$haveSessionData = $userDeviceInfo['haveSessionData'];
			//小程序端是新版本的老用户				 
			if ($haveSessionData) {
				$data_session = array(
					'sessionid'   => $sessionId,
					'sessionexpire' => $sessionExpire
				);
			}
			//小程序端是新版本的新用户
			if (!$haveSessionData) {
				$data_session = array(
					'userid'   => $userId,
					'deviceid'   => $deviceId,
					'sessionid'   => $sessionId,
					'sessionexpire' => $sessionExpire,
					'platform'   => $platform,
					'brand'   => $brand,
					'model'   => $model,
					'ip'      =>RAW_Util::get_client_ip()
				);
			}
			$result = RAW_Util::updateMinapperUser($data_user, $data_session, $openId, $userId, $deviceId, $haveSessionData);
			if ($result['errcode'] != 0) {
				return new WP_Error('error', $result, array('status' => 500));
			}
			else
			{
				
			}
		}
		$raw_session['userId'] = $userId;
		$_userInfo=RAW_Util::getMemberUserbyUserId($userId);
		$enableUpdateAvatarCount= (int)RAW_Util::getEnableUpdateAvatarCount($userId);
		$_userInfo->enableUpdateAvatarCount=$enableUpdateAvatarCount;
		$response = array('raw_session' => $raw_session,'userInfo'=>$_userInfo, 'device' => $device);
		$response = rest_ensure_response($response);
		return $response;
	}


	public function userlogin($request)
	{

		global $wpdb;
		date_default_timezone_set('Asia/Shanghai');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';


		$userInfo = $request['userInfo'];	
		$js_code = $request['js_code'];
		$appid = get_option('raw_appid');
	
		$weixinSession = RAW()->wxapi->get_jscode2session($js_code);
		if (!empty($weixinSession['errcode']) &&  $weixinSession['errcode'] != 0) {
			return new WP_Error($weixinSession['errcode'], '获取session_key错误:' . $weixinSession['errmsg'], array('status' => 500));
		}
		$session_key = $weixinSession['session_key'];
		$openId = $weixinSession['openid'];
		$unionId = $weixinSession['unionid'];
		$nickname='微信用户';
		$_nickname='';
		$wxNickname=$userInfo['nickName'];
		$wxAvatarUrl=$userInfo['avatarUrl'];
		if(!empty($wxNickname) && $wxNickname !='微信用户')
		{
			$nickname =$wxNickname;
			$avatarUrl =$wxAvatarUrl;
			$nickname = RAW_Util::filterEmoji($nickname);
			$_nickname = base64_encode($nickname);
			$_nickname = strlen($_nickname) > 49 ? substr($_nickname, 49) : $_nickname;
		}
		else
		{
			$avatarUrl = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
			$nickname .=RAW_Util::randString();
			$_nickname = $nickname;
		}
		$user_email = $openId . '@weixin.com';
		$userId = 0;
		$userDeviceInfo = array();
		$language = empty($userinfo['language']) ? '' : $userinfo['language'];
		$gender  = empty($userinfo['gender']) ? '' : $userinfo['gender'];
		$city     = empty($userinfo['city']) ? '' : $userinfo['city'];
		$province = empty($userinfo['province']) ? '' : $userinfo['province'];
		$country  = empty($userinfo['country']) ? '' : $userinfo['country'];
		$gender   = empty($userinfo['gender']) ? '' : $userinfo['gender'];

		// 创建用户
		if (!username_exists($openId)) {

			$userId = RAW_Util::insertWPUser($openId, $nickname, $nickname, $_nickname, $nickname, $user_email, null);

			if ($userId == 0) {
				return new WP_Error('error', '插入wordpress用户错误：', array('status' => 500));
			}
			update_user_meta($userId, 'unionId', $unionId);
			//$userDeviceInfo=RAW_Util::getUserDeviceInfo($userId,'');
		} else {
			$user = get_user_by('login', $openId);
			$userId=$user->ID;
			if(!empty($wxNickname) && $wxNickname !='微信用户' )
			{
				$Id = RAW_Util::updateWPUser($user->ID, $nickname, $nickname, $_nickname, $nickname);
				if ($Id == 0) {
					return new WP_Error('error', '更新wordpress用户错误：', array('status' => 500));
				}
			}
			
			if (delete_user_meta($userId, 'unionId')) {
				update_user_meta($userId, 'unionId', $unionId);
			}
			//$userDeviceInfo=RAW_Util::getUserDeviceInfo($userId,'');
		}
		//会话
		$raw_session = RAW_Util::generate_session();
		$sessionExpire = $raw_session['sessionExpire'];
		$sessionId = $raw_session['sessionId'];
		$sql = $wpdb->prepare("select * from " . $wpdb->minapper_weixin_users . " where openid=%s", $openId);
		$weixin_users = $wpdb->get_row($sql);
		if (empty($weixin_users)) {
			$integral = 0;
			$raw_user_initial_integral = get_option('raw_user_initial_integral');
			if (!empty($raw_user_initial_integral)) {
				$integral = (int)$raw_user_initial_integral;
			}


			$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, '');
			$device = empty($userDeviceInfo['device']) ? array() : $userDeviceInfo['device'];
			$model = empty($device['model']) ? "" : $device['model'];
			$platform = empty($device['platform']) ? "" : $device['platform'];
			$brand = empty($device['brand']) ? "" : $device['brand'];
			$deviceId = empty($userDeviceInfo['deviceId']) ? "" : $userDeviceInfo['deviceId'];



			$invitecode = RAW_Util::generate_invitecode();
			$data_user = array(
				'openid'   => $openId,
				'userid'   => $userId,
				'unionid'  => $unionId,
				'nickname' => $nickname,
				'language' => $language,
				'gender'   => $gender,
				'city'     => $city,
				'province' => $province,
				'country'  => $country,
				'gender'   => $gender,
				'avatarUrl' => $avatarUrl,
				'member' => '10',
				'integral' => $integral,
				'memberbegindate' => date('Y-m-d H:i:s', time()),
				'creatdate' => date('Y-m-d H:i:s', time()),
				'invitecode' => $invitecode
			);


			$data_session = array(
				'userid'   => $userId,
				'deviceid'   => $deviceId,
				'sessionid'   => $sessionId,
				'sessionexpire' => $sessionExpire,
				'platform'   => $platform,
				'brand'   => $brand,
				'model'   => $model,
				'ip'      =>RAW_Util::get_client_ip()
			);

			$result = RAW_Util::insertMinapperUser($data_user, $data_session, $openId);
			if ($result['errcode'] != 0) {
				return new WP_Error('error', $result, array('status' => 500));
			}

			//extkey- 邀请者的invitecode
			//extvalue-被邀请者的openid
			//userid -邀请者的userid
			//extid- 0 未被确认  1 被确认
			//extype -'invite' 扩展类型
			//extkey-invitecode 邀请码 
			$sql = $wpdb->prepare("SELECT extkey,(select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as inviteuserid , (select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as userid FROM " . $wpdb->minapper_ext . " where extype='invite'  and extvalue=%s and extid=0", $openId);
			$inviteuser = $wpdb->get_row($sql);
			$invitecode = '';
			$inviteuserId = 0;
			$updateinvate = false;
			if (!empty($inviteuser)) {
				$invitecode = $inviteuser->extkey;
				$inviteuserId = $inviteuser->inviteuserid;

				if (!empty($invitecode) && !empty($inviteuserId)) {

					$where  = array('extkey' => $invitecode, 'extvalue' => $openId, 'extid'  => 0, 'extype' => 'invite');
					$data = array(
						'extid' => 1,
						'userid' => $inviteuserId
					);
					$updateinvate = $wpdb->update($wpdb->minapper_ext, $data, $where);
				}
			}

			if (!empty($updateinvate) && !empty($inviteuserId)) {
				$operateRewordIntegral = RAW_Util::operateRewordIntegral((int)$inviteuserId, 'raw_invite_integral', $commentId, '', 'add');
			}
		} else {
			$data_user=array();
			if(!empty($wxNickname) && $wxNickname !='微信用户' )
			{
				$data_user = array(
					'unionid'  => $unionId,
					'nickname' => $nickname,
					'language' => $language,
					'gender'   => $gender,
					'city'     => $city,
					'province' => $province,
					'country'  => $country,
					'gender'   => $gender,
					'avatarUrl' => $avatarUrl,
					'updatedate' => date('Y-m-d H:i:s', time())
	
				);
			}
			else
			{
				$data_user = array(					
					'updatedate' => date('Y-m-d H:i:s', time())
	
				);
			}
			
			$_sessionId = $weixin_users->sessionid;
			$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, $_sessionId);
			$device = empty($userDeviceInfo['device']) ? array() : $userDeviceInfo['device'];
			$model = empty($device['model']) ? "" : $device['model'];
			$platform = empty($device['platform']) ? "" : $device['platform'];
			$brand = empty($device['brand']) ? "" : $device['brand'];
			$deviceId = empty($userDeviceInfo['deviceId']) ? "" : $userDeviceInfo['deviceId'];
			$haveSessionData = $userDeviceInfo['haveSessionData'];
			//小程序端是新版本的老用户				 
			if ($haveSessionData) {
				$data_session = array(
					'sessionid'   => $sessionId,
					'sessionexpire' => $sessionExpire
				);
			}
			//小程序端是新版本的新用户
			if (!$haveSessionData) {
				$data_session = array(
					'userid'   => $userId,
					'deviceid'   => $deviceId,
					'sessionid'   => $sessionId,
					'sessionexpire' => $sessionExpire,
					'platform'   => $platform,
					'brand'   => $brand,
					'model'   => $model,
					'ip'      =>RAW_Util::get_client_ip()
				);
			}
			$result = RAW_Util::updateMinapperUser($data_user, $data_session, $openId, $userId, $deviceId, $haveSessionData);
			if ($result['errcode'] != 0) {
				return new WP_Error('error', $result, array('status' => 500));
			}
			else
			{
				
			}
		}
		$raw_session['userId'] = $userId;
		$_userInfo=RAW_Util::getMemberUserbyUserId($userId);
		$enableUpdateAvatarCount= (int)RAW_Util::getEnableUpdateAvatarCount($userId);
		$_userInfo->enableUpdateAvatarCount=$enableUpdateAvatarCount;
		$response = array('raw_session' => $raw_session,'userInfo'=>$_userInfo, 'device' => $device);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function registerUser($request)
	{

		global $wpdb;
		date_default_timezone_set('Asia/Shanghai');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$user_login = $request['user_login '];
		$user_pass = $request['user_pass '];
		$nickname = $request['nickname'];
		$nickname = RAW_Util::filterEmoji($nickname);
		$_nickname = base64_encode($nickname);
		$_nickname = strlen($_nickname) > 49 ? substr($_nickname, 49) : $_nickname;
		$user_email = $user_login . '@weixin.com';

		$userId = 0;
		$userDeviceInfo = array();
		$avatarUrl = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';

		// 创建用户
		if (!username_exists($user_login)) {

			$userId = RAW_Util::insertWPUser($user_login, $nickname, $nickname, $_nickname, $nickname, $user_email, $user_pass);

			if ($userId == 0) {
				return new WP_Error('error', '插入wordpress用户错误：', array('status' => 500));
			}
		} else {
			return new WP_Error('error', '该用户名已被注册', array('status' => 500));
		}
		// 会话
		$raw_session = RAW_Util::generate_session();
		$sessionExpire = $raw_session['sessionExpire'];
		$sessionId = $raw_session['sessionId'];
		$integral = 0;
		$raw_user_initial_integral = get_option('raw_user_initial_integral');
		if (!empty($raw_user_initial_integral)) {
			$integral = (int)$raw_user_initial_integral;
		}
		$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, '');
		$device = empty($userDeviceInfo['device']) ? array() : $userDeviceInfo['device'];
		$model = empty($device['model']) ? "" : $device['model'];
		$platform = empty($device['platform']) ? "" : $device['platform'];
		$brand = empty($device['brand']) ? "" : $device['brand'];
		$deviceId = empty($userDeviceInfo['deviceId']) ? "" : $userDeviceInfo['deviceId'];
		$invitecode = RAW_Util::generate_invitecode();
		$data_user = array(
			'openid'   => $user_login,
			'userid'   => $userId,
			'avatarUrl' => $avatarUrl,
			'member' => '10',
			'integral' => $integral,
			'memberbegindate' => date('Y-m-d H:i:s', time()),
			'creatdate' => date('Y-m-d H:i:s', time()),
			'invitecode' => $invitecode
		);


		$data_session = array(
			'userid'   => $userId,
			'deviceid'   => $deviceId,
			'sessionid'   => $sessionId,
			'sessionexpire' => $sessionExpire,
			'platform'   => $platform,
			'brand'   => $brand,
			'model'   => $model
		);

		$result = RAW_Util::insertMinapperUser($data_user, $data_session, $user_login);
		if ($result['errcode'] != 0) {
			return new WP_Error('error', $result, array('status' => 500));
		}
		$raw_session['userId'] = $userId;
		$response = array('raw_session' => $raw_session, 'device' => $device);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function bindMpUser($request)
	{

		global $wpdb;
		date_default_timezone_set('Asia/Shanghai');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';


		$user_login = $request['user_login '];
		$user_pass = $request['user_pass '];
		$sql = $wpdb->prepare("SELECT ID,user_pass FROM wp_users WHERE user_login=%s", $user_login);
		$user = $wpdb->get_row($sql);
		$userId = $user->ID;
		$user_pass = $user->user_pass;
		$userpassright = wp_check_password($password, $user_pass, $userId);

		if (!$userpassright) {
			return new WP_Error('error', '用户名或密码错误', array('status' => 500));
		}

		$userinfo = $request['userInfo'];
		$js_code = $request['js_code'];

		$weixinSession = RAW()->wxapi->get_jscode2session($js_code);
		if (!empty($weixinSession['errcode']) &&  $weixinSession['errcode'] != 0) {
			return new WP_Error($weixinSession['errcode'], '获取session_key错误:' . $weixinSession['errmsg'], array('status' => 500));
		}
		$openId = $weixinSession['openid'];
		$where = array('openid ' => $user_login);
		$data = array('userid' => '');
		$resultUpdate = $wpdb->update($wpdb->minapper_weixin_users, $data, $where);
		if ($resultUpdate) {
			$where = array('openid ' => $openId);
			$data = array('userid' => $userId);
			$resultUpdate = $wpdb->update($wpdb->minapper_weixin_users, $data, $where);
			if (!$resultUpdate) {
				return new WP_Error($weixinSession['errcode'], '绑定错误', array('status' => 500));
			}
		} else {
			return new WP_Error($weixinSession['errcode'], '绑定错误', array('status' => 500));
		}

		//todo此处待完善
		$response = array('raw_session' => $raw_session, 'device' => $device);
		$response = rest_ensure_response($response);
		return $response;


		return $response;
	}

	public function registerUserLogin($request)
	{

		global $wpdb;
		date_default_timezone_set('Asia/Shanghai');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$user_login = RAW_Util::sanitize_param($request['user_login ']);
		$password = RAW_Util::sanitize_param($request['user_pass ']);

		$sql = $wpdb->prepare("SELECT ID,user_pass FROM wp_users WHERE user_login=%s", $user_login);
		$user = $wpdb->get_row($sql);
		$userId = $user->ID;
		$user_pass = $user->user_pass;
		$userpassright = wp_check_password($password, $user_pass, $userId);

		if (!$userpassright) {
			return new WP_Error('error', '用户名或密码错误', array('status' => 500));
		}



		$raw_session = RAW_Util::generate_session();
		$sessionExpire = $raw_session['sessionExpire'];
		$sessionId = $raw_session['sessionId'];

		$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, '');
		$device = empty($userDeviceInfo['device']) ? array() : $userDeviceInfo['device'];
		$model = empty($device['model']) ? "" : $device['model'];
		$platform = empty($device['platform']) ? "" : $device['platform'];
		$brand = empty($device['brand']) ? "" : $device['brand'];
		$deviceId = empty($userDeviceInfo['deviceId']) ? "" : $userDeviceInfo['deviceId'];
		$haveSessionData = $userDeviceInfo['haveSessionData'];
		$data_session = array(
			'userid'   => $userId,
			'deviceid'   => $deviceId,
			'sessionid'   => $sessionId,
			'sessionexpire' => $sessionExpire,
			'platform'   => $platform,
			'brand'   => $brand,
			'model'   => $model
		);

		$result = RAW_Util::updateUserSession($data_session, $userId, $deviceId);
		if ($result['errcode'] != 0) {
			return new WP_Error('error', $result, array('status' => 500));
		}
		$raw_session['userId'] = $userId;
		$response = array('raw_session' => $raw_session, 'device' => $device);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function update_session($request)
	{

		global $wpdb;
		date_default_timezone_set('Asia/Shanghai');
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';
		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;
		$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, '');
		$deviceId = empty($userDeviceInfo['deviceId']) ? "" : $userDeviceInfo['deviceId'];
		// 会话
		$raw_session = RAW_Util::generate_session();
		if (!$raw_session) {
			return new WP_Error('session_generate_error', 'Session生成错误', array('status' => 400));
		}
		date_default_timezone_set('Asia/Shanghai');
		$data_array = array(
			'sessionid'  => $raw_session['sessionId'],
			'sessionexpire' => $raw_session['sessionExpire']

		);
		$where  = array('userid' => $userId, 'deviceId' => $deviceId);
		$updateUserResult = $wpdb->update($wpdb->minapper_user_session, $data_array, $where);

		if ($updateUserResult == false) {
			return new WP_Error('update_user_error', '更新微信用户会话错误：' . $updateUserResult, array('status' => 200));
		}


		$raw_session['userId'] = $userId;
		$response = array('raw_session' => $raw_session);
		$response = rest_ensure_response($response);

		return $response;
	}


	public function getMemberUserInfo($request)
	{
		global $wpdb;
		$sessionId  = isset($request['sessionId']) ? $request['sessionId'] : "";
		$userId = isset($request['userId']) ? (int)$request['userId'] : 0;

		$updateUserMember = RAW_Util::updateUserMember($userId);
		$memberUserInfo = RAW_Util::getMemberUserInfo($userId);
		if (empty($memberUserInfo)) {
			return new WP_Error('error', '无相关信息', array('status' => 404));
		}
		if (empty($memberUserInfo->newcontentSubscribeCount)) {
			$memberUserInfo->newcontentSubscribeCount = 0;
		}

		if (empty($memberUserInfo->newreplaySubscribeCount)) {
			$memberUserInfo->newreplaySubscribeCount = 0;
		}

		$raw_new_post_message_id = empty(get_option('raw_new_post_message_id')) ? '' : get_option('raw_new_post_message_id');
		$memberUserInfo->raw_new_post_message_id = $raw_new_post_message_id;

		$raw_new_replay_message_id = empty(get_option('raw_new_replay_message_id')) ? '' : get_option('raw_new_replay_message_id');
		$memberUserInfo->raw_new_replay_message_id = $raw_new_replay_message_id;

		$userDeviceInfo = RAW_Util::getUserDeviceInfo($userId, $sessionId);
		$memberUserInfo->userDeviceInfo = $userDeviceInfo;

		$is_mpauth = empty(get_user_meta($userId, 'raw_mp_openid', true)) ? false : true;
		$memberUserInfo->is_mpauth = $is_mpauth;
		$despending = get_user_meta($userId, "despending", true);
		$memberUserInfo->despending = $despending;
		if ($despending != "1") {
			$memberUserInfo->description = '';
		}

		$memberUserInfo->storeappid=get_user_meta($userId, 'storeappid', true);
		$memberUserInfo->storename=get_user_meta($userId, 'storename', true);
		$memberUserInfo->storeinfo=get_user_meta($userId, 'storeinfo', true);

		$enableUpdateAvatarCount= (int)RAW_Util::getEnableUpdateAvatarCount($userId);
		$memberUserInfo->enableUpdateAvatarCount=$enableUpdateAvatarCount;

		$publishPostUserRight=RAW_Util::checkUserRight($userId,'publishPost');
		$memberUserInfo->publishPostUserRight=$publishPostUserRight;

		$response = array('memberUserInfo' => $memberUserInfo, 'updateUserMember' => $updateUserMember);
		$response = rest_ensure_response($response);
		return $response;
	}

	public function getUserInfo($request)
	{
		global $wpdb;
		$authorId = isset($request['id']) ? (int)$request['id'] : 0;
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';

		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;

		$checkUser = RAW_Util::checkUser($sessionId, $userId);



		$sql = $wpdb->prepare("SELECT u.id,u.display_name,wu.avatarurl,wu.integral,wu.`member`
            , (
                SELECT
                    um.meta_value
                FROM
                    " . $wpdb->usermeta . " um
                WHERE
                    um.meta_key = '" . $wpdb->prefix . "user_level' 
                AND um.user_id = u.id
			) AS level,
			(
                SELECT
				ud.meta_value
                FROM
                    " . $wpdb->usermeta . " ud
                WHERE
				ud.meta_key = 'description' 
                AND ud.user_id = u.id
			) AS description,
			wu.userid as minapperweixinuserid,
			(SELECT if(count(1)=1,'true','false' )  from " . $wpdb->minapper_ext . " t2 where t2.userid= %d and t2.extid=%d and t2.extype='follow')  as followeachoher,
			(SELECT  count(1) from " . $wpdb->posts . " p where p.post_type='post' and (p.post_status='publish' or p.post_status='pending')  and p.post_author=u.id ) as postcount,
			(SELECT  count(1) from " . $wpdb->posts . " c where c.post_type='topic' and ( c.post_status='publish' or  c.post_status='pending') and c.post_author=u.id ) as topiccount,
		   (select count(1) from " . $wpdb->minapper_ext . "  e where e.extid=u.id and e.extype='follow' )as followmecount,
		   (select count(1) from " . $wpdb->minapper_ext . "   f where f.userid=u.id and f.extype='follow' ) as myfollowcount            
              FROM $wpdb->users  u left join  " . $wpdb->minapper_weixin_users . " wu on wu.userid=u.id WHERE id = %d ", $authorId, $userId, $authorId);
		$_memberUserInfo = $wpdb->get_row($sql);
		if (empty($_memberUserInfo)) {
			return new WP_Error('error', '无相关信息', array('status' => 404));
		} else {
			$memberUserInfo['postcount'] = $_memberUserInfo->postcount;
			$memberUserInfo['topiccount'] = $_memberUserInfo->topiccount;
			$memberUserInfo['followmecount'] = $_memberUserInfo->followmecount;
			$memberUserInfo['myfollowcount'] = $_memberUserInfo->myfollowcount;
			$memberUserInfo['user_nicename'] = $_memberUserInfo->display_name;
			$memberUserInfo['followeachoher'] = $_memberUserInfo->followeachoher;
			$member = empty($_memberUserInfo->member) ? "10" : $_memberUserInfo->member;
			$memberUserInfo['member'] = $member;
			$memberUserInfo['membername'] = RAW_Util::getMemberName($member);
			$despending = get_user_meta($authorId, "despending", true);
			$memberUserInfo['despending'] = $despending;
			//$memberUserInfo['$sql']=$sql;

			if ($despending != "1") {
				if (empty($memberUserInfo['minapperweixinuserid'])) {
					$memberUserInfo['description'] = $_memberUserInfo->description;
				} else {
					$memberUserInfo['description'] = '';
				}
			} else {
				$memberUserInfo['description'] = $_memberUserInfo->description;
			}



			$author_id = $_memberUserInfo->id;

			if ($checkUser) {
				$memberUserInfo['follow'] = RAW_Util::getUserFollow($sessionId, $userId, (int)$author_id);
			} else {
				$memberUserInfo['follow'] = false;
			}
			$memberUserInfo['id'] = (int)$author_id;
			if (empty($_memberUserInfo->avatarurl)) {
				$minapper_avatar = get_user_meta((int)$author_id, 'minapper_avatar', true);
				if (!empty($minapper_avatar)) {
					$author_avatar = $minapper_avatar;
				} else {
					$author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
				}
				$memberUserInfo['avatarurl'] = $author_avatar;
			} else {
				$memberUserInfo['avatarurl'] = $_memberUserInfo->avatarurl;
			}



			// if($_memberUserInfo->level==10 && empty($_memberUserInfo->membername))
			// {
			//     $memberUserInfo['membername'] ="管理员";

			// }
			// else
			// {
			//     $memberUserInfo['membername'] =$_memberUserInfo->membername;
			// }

			if (empty($_memberUserInfo->integral)) {
				$memberUserInfo['integral'] = "0";
			} else {
				$memberUserInfo['integral'] = $_memberUserInfo->integral;
			}


		}

		$memberUserInfo['storeappid']=get_user_meta($authorId, 'storeappid', true);
		$memberUserInfo['storename']=get_user_meta($authorId, 'storename', true);
		$memberUserInfo['storeinfo']=get_user_meta($authorId, 'storeinfo', true);

		$publishPostUserRight=RAW_Util::checkUserRight($userId,'publishPost');
		$memberUserInfo['publishPostUserRight']=$publishPostUserRight;
		$response = array('memberUserInfo' => $memberUserInfo);
		$response = rest_ensure_response($response);
		return $response;
	}
	public function user_sign_in($request)
	{
		date_default_timezone_set('Asia/Shanghai');
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;
		if (!empty($userId)) {
			$signinDate = date('Y-m-d', time());
			$meta_key = 'raw_user_signin_' . $signinDate;

			if (get_user_meta($userId, $meta_key, true) != $signinDate) {
				$result = add_user_meta($userId, $meta_key, $signinDate);

				if ($result === false) {
					return new WP_Error('error', '签到失败', array('status' => 400));
				} else {
					$message = '签到成功';
					$operateRewordIntegral = RAW_Util::operateRewordIntegral($userId, 'raw_sign_integral', '', '', 'add');
					if ($operateRewordIntegral > 0) {

						$message = $message . ",获取" . $operateRewordIntegral . "积分奖励.";
					}
					$response = array('success' => true, 'message' => $message, 'operateRewordIntegral' => $operateRewordIntegral,);
					$response = rest_ensure_response($response);
					return $response;
				}
			} else {
				$response = array('success' => true, 'message' => '今日已签到');
				$response = rest_ensure_response($response);
				return $response;
			}
		} else {
			return new WP_Error('error', '用户参数错误', array('status' => 200));
		}
	}

	public function get_users_rand($request)
	{
		$users=RAW_Util::get_users_rand();
		$response = rest_ensure_response($users);
		return $response;
	}

	public function get_users_recent_login($request)
	{
		$users=RAW_Util::get_users_recent_login();
		$response = rest_ensure_response($users);
		return $response;
	}

	public function get_users_recent_comment($request)
	{
		$users=RAW_Util::get_users_recent_comment();
		$response = rest_ensure_response($users);
		return $response;
	}



	/**
	 * Check whether a given request has permission to read customers.
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 * @return WP_Error|boolean
	 */
	public function get_users_login_permissions_check($request)
	{
		return true;
	}

	public function userlogin_permissions_check($request)
	{

		//$userinfo= json_decode($request['userinfo'], true );
		$js_code = $request['js_code'];
		if (empty($js_code)) {
			return new WP_Error('error', "js_code参数错误", array('status' => 200));
		}
		return true;
	}

	/**
	 * Check if a given request has access create customers.
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 * @return boolean
	 */
	public function get_author_list_permissions_check($request)
	{

		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;

		$checkUser = RAW_Util::checkUser($sessionId, $userId);
		if (!$checkUser) {
			return  RAW_Util::errorUserMessage();
		}

		return true;
	}

	/**
	 * Check if a given request has access create customers.
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 * @return boolean
	 */
	public function get_weixinUserInfo_permissions_check($request)
	{

		$sessionId  = isset($request['sessionId']) ? $request['sessionId'] : "";
		$userId = isset($request['userId']) ? (int)$request['userId'] : 0;

		$checkUser = RAW_Util::checkUser($sessionId, $userId);
		if (!$checkUser) {
			return  RAW_Util::errorUserMessage();
		}

		return true;
	}

	public function update_user_description_permissions_check($request)
	{

		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;
		$description = isset($request['description']) ? $request['description'] : '';

		$checkUser = RAW_Util::checkUser($sessionId, $userId);
		if (!$checkUser) {
			return  RAW_Util::errorUserMessage();
		}

		if (mb_strlen($description, 'utf-8') > 150) {
			return new WP_Error('error', '个人简介文字过长了', array('status' => 200));
		}
		$_description = get_user_meta($userId, "description", true);
		if (strcmp($description, $_description) == 0) {
			return new WP_Error('error', '个人简介没有修改', array('status' => 200));
		}

		return true;
	}

	public function get_userInfo_permissions_check($request)
	{

		$userId = isset($request['id']) ? (int)$request['id'] : 0;
		$user = get_user_by('ID', $userId);
		if (empty($user)) {
			return new WP_Error('error', "用户参数错误", array('status' => 200));
		}
		return true;
	}


	/**
	 * Check if a given request has access create customers.
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 * @return boolean
	 */
	public function update_session_permissions_check($request)
	{

		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;

		$checkUser = RAW_Util::checkUser($sessionId, $userId);
		if (!$checkUser) {
			return  RAW_Util::errorUserMessage();
		}

		return true;
	}

	/**
	 * Check if a given request has access create customers.
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 * @return boolean
	 */
	public function check_user_permissions_check($request)
	{

		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;

		$checkUser = RAW_Util::checkUser($sessionId, $userId);
		if (!$checkUser) {
			return  RAW_Util::errorUserMessage();
		}

		return true;
	}




	/**
	 * Check if a given request has access create customers.
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 * @return boolean
	 */
	public function user_sign_in_permissions_check($request)
	{

		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;

		$checkUserEnable = RAW_Util::checkUserEnable($sessionId, $userId);
		$error = $checkUserEnable['error'];
		$message = $checkUserEnable['message'];
		if ($error != "0") {
			if ($error == "01") {
				return new WP_Error('user_parameter_error', $message, array('status' => 400));
			} else if ($error == "02") {

				return new WP_Error('error', $message, array('status' => 403));
			}
		}

		return true;
	}

	/**
	 * Check if a given request has access create customers.
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 * @return boolean
	 */
	public function share_app_permissions_check($request)
	{

		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;

		$checkUserEnable = RAW_Util::checkUserEnable($sessionId, $userId);
		$error = $checkUserEnable['error'];
		$message = $checkUserEnable['message'];
		if ($error != "0") {
			if ($error == "01") {
				return new WP_Error('user_parameter_error', $message, array('status' => 400));
			} else if ($error == "02") {

				return new WP_Error('error', $message, array('status' => 403));
			}
		}

		return true;
	}


	/**
	 * Check if a given request has access create customers.
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 * @return boolean
	 */
	public function open_Ad_Video_permissions_check($request)
	{

		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;

		$checkUserEnable = RAW_Util::checkUserEnable($sessionId, $userId);
		$error = $checkUserEnable['error'];
		$message = $checkUserEnable['message'];
		if ($error != "0") {
			if ($error == "01") {
				return new WP_Error('user_parameter_error', $message, array('status' => 400));
			} else if ($error == "02") {

				return new WP_Error('error', $message, array('status' => 403));
			}
		}

		return true;
	}

	public function set_invite_log_permissions_check($request)
	{
		global $wpdb;
		$wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
		$wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
		$invitecode = $request['invitecode'];
		$openId = $request['openid'];


		$sql = $wpdb->prepare("select count(1) from " . $wpdb->minapper_weixin_users . " where invitecode=%s", $invitecode);
		$count = (int)$wpdb->get_var($sql);
		if ($count == 0) {
			return new WP_Error('error', "邀请码错误", array('status' => 200));
		}



		return true;
	}

	public function user_despending_permissions_check($request)
	{
		$sessionId = isset($request['sessionid']) ? $request['sessionid'] : '';
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;
		$checkUser = RAW_Util::checkUser($sessionId, $userId);
		if (!$checkUser) {
			return  RAW_Util::errorUserMessage();
		}
		$memberUserInfo = RAW_Util::getMemberUserInfo($userId);
		if ($memberUserInfo->member != "00") {
			return new WP_Error('error', "未授权使用此功能", array('status' => 500));
		}

		return true;
	}

	public function approve_user_des_permissions_check($request)
	{
		$sessionId  = isset($request['sessionid']) ? $request['sessionid'] : "";
		$userId = isset($request['userid']) ? (int)$request['userid'] : 0;

		$checkUser = RAW_Util::checkUser($sessionId, $userId);
		if (!$checkUser) {
			return  RAW_Util::errorUserMessage();
		}

		$memberUserInfo = RAW_Util::getMemberUserInfo($userId);
		if ($memberUserInfo->member != "00") {
			return new WP_Error('error', "未授权使用此功能", array('status' => 500));
		}

		$despending = empty($request['despending']) ? '' : $request['despending'];

		if ($despending != "0" && $despending != "1") {
			return new WP_Error('error', "参数错误", array('status' => 500));
		}

		return true;
	}



	public function get_users_locations_permissions_check($request)
	{
		return true;
	}
}
