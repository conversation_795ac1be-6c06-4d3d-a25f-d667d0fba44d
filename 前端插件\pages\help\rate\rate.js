// pages/help/rate/rate.js
const API = require('../../../utils/api.js')

Page({
  data: {
    // 页面参数
    helpId: '',
    ratingType: '', // 'requester_to_helper' 或 'helper_to_requester'

    // 需求信息
    helpDetail: {},
    targetUser: {}, // 被评价的用户信息

    // 评价表单
    rating: 0,
    comment: '',

    // 界面状态
    loading: true,
    submitting: false,

    // 用户信息
    userSession: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('=== 评价页面加载 ===', options);

    if (options.helpId && options.type) {
      this.setData({
        helpId: options.helpId,
        ratingType: options.type
      });
      this.initPage();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 初始化页面
  initPage() {
    this.getUserInfo();
    this.loadHelpDetail();
  },

  // 获取用户信息
  getUserInfo() {
    const userSession = wx.getStorageSync('userSession') || {};
    this.setData({ userSession });
  },

  // 加载互助详情
  loadHelpDetail() {
    wx.showLoading({ title: '加载中...' });

    API.getHelpDetail(this.data.helpId).then(res => {
      wx.hideLoading();
      console.log('API返回数据:', res);

      if (res && res.request) {
        const helpDetail = res.request;
        const responses = res.responses || [];

        // 找到选中的响应者
        const selectedResponse = responses.find(r => r.status === 'selected');

        let targetUser = {};
        let pageTitle = '';

        if (this.data.ratingType === 'requester_to_helper') {
          // 发布者评价帮助者
          targetUser = {
            id: selectedResponse?.user_id,
            name: selectedResponse?.user_name,
            avatar: selectedResponse?.user_avatar
          };
          pageTitle = '评价帮助者';
        } else {
          // 帮助者评价发布者
          targetUser = {
            id: helpDetail.user_id,
            name: helpDetail.author_name,
            avatar: helpDetail.author_avatar
          };
          pageTitle = '评价发布者';
        }

        this.setData({
          helpDetail,
          targetUser,
          loading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({ title: pageTitle });

      } else {
        this.handleLoadError('数据格式错误');
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('加载互助详情失败:', error);
      this.handleLoadError('网络错误，请重试');
    });
  },

  // 处理加载错误
  handleLoadError(message) {
    this.setData({ loading: false });
    wx.showToast({
      title: message,
      icon: 'none'
    });
    setTimeout(() => {
      wx.navigateBack();
    }, 2000);
  },

  // 星级评分点击
  onStarTap(e) {
    const rating = parseInt(e.currentTarget.dataset.rating);
    this.setData({ rating });
  },

  // 评价内容输入
  onCommentInput(e) {
    this.setData({ comment: e.detail.value });
  },

  // 提交评价
  submitRating() {
    if (this.data.submitting) return;

    // 验证评分
    if (this.data.rating === 0) {
      wx.showToast({
        title: '请选择评分',
        icon: 'none'
      });
      return;
    }

    // 验证评价内容
    if (!this.data.comment.trim()) {
      wx.showToast({
        title: '请填写评价内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });
    wx.showLoading({ title: '提交中...' });

    const args = {
      rating: this.data.rating,
      comment: this.data.comment.trim(),
      userid: this.data.userSession.userId,
      sessionid: this.data.userSession.sessionId
    };

    API.submitHelpRating(this.data.helpId, args).then(res => {
      wx.hideLoading();
      this.setData({ submitting: false });

      if (res && res.success) {
        wx.showToast({
          title: '评价提交成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);

      } else {
        wx.showToast({
          title: res.message || '提交失败，请重试',
          icon: 'none'
        });
      }
    }).catch(error => {
      wx.hideLoading();
      this.setData({ submitting: false });
      console.error('提交评价失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 取消评价
  cancelRating() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消评价吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  }
})