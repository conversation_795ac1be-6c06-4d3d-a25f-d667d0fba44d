# 邻里互助模块开发工作清单

## 项目概述
基于现有bbPress论坛系统扩展，利用forums/topics结构创建专门的"邻里互助"论坛分类，通过自定义字段扩展互助特有属性，复用现有的用户系统和评论回复机制。邻里互助模块作为首页服务入口，不占用底部导航栏位置。

## 色彩方案（已实施）：**
- 主色：#FF6B35（温暖橙色）- 体现"暖友"温度感
- 强调色：#FFB74D（柔和金橙）- 用于VIP和特殊功能
- 辅助色：#E65100（深橙红）- 用于渐变和强调

## 邻里互助模块完整流程设计

### 1. 需求发布流程
**发布者操作：**
- 选择紧急程度（紧急/一般/不急）
- 填写标题和详细描述
- 选择互助类型（家居维修/照顾宠物/搬运物品/技能咨询/其他）
- 设置位置信息
- 设定积分奖励
- 发布需求

### 2. 响应和选择流程
**帮助者操作：**
- 浏览互助列表
- 查看详情并响应需求
- 填写响应说明和预计时间

**发布者操作：**
- 查看所有响应
- 选择合适的帮助者
- 确认服务安排

### 3. 服务进行流程
**系统状态更新：**
- 自动更新为"服务进行中"
- 显示帮助者联系方式
- 提供进度跟踪

### 4. 完成确认流程
**发布者操作：**
- 确认服务完成
- 对帮助者进行评价（1-5星）
- 积分自动转账

**帮助者操作：**
- 标记服务完成
- 对发布者进行评价

### 5. 异常处理流程
- 取消需求功能
- 更换帮助者功能
- 申诉和客服处理

## 开发工作清单（按功能模块顺序）

### 模块一：基础数据结构和论坛分类（第1周）

#### 1.1 数据库设计
- **任务1.1.1**: 创建邻里互助专用数据表
  ```sql
  -- 互助需求表（基于bbPress topics扩展）
  CREATE TABLE wo_minapper_help_requests (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    topic_id bigint(20) NOT NULL COMMENT '关联bbPress topic ID',
    user_id bigint(20) NOT NULL COMMENT '发布用户ID',
    urgency enum('urgent','normal','low') DEFAULT 'normal' COMMENT '紧急程度',
    help_type varchar(50) NOT NULL COMMENT '互助类型',
    points_reward int DEFAULT 0 COMMENT '积分奖励',
    location_info text COMMENT '位置信息JSON',
    contact_info varchar(255) COMMENT '联系方式',
    status enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY topic_id (topic_id),
    KEY user_id (user_id),
    KEY status (status),
    KEY urgency (urgency)
  );
  ```

- **任务1.1.2**: 创建响应记录表
  ```sql
  -- 互助响应表
  CREATE TABLE wo_minapper_help_responses (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    request_id bigint(20) NOT NULL COMMENT '互助需求ID',
    user_id bigint(20) NOT NULL COMMENT '响应用户ID',
    content text COMMENT '响应内容',
    estimated_time varchar(100) COMMENT '预计完成时间',
    status enum('pending','selected','completed','rejected') DEFAULT 'pending',
    selected_at timestamp NULL COMMENT '被选中时间',
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY request_id (request_id),
    KEY user_id (user_id),
    KEY status (status)
  );
  ```

- **任务1.1.3**: 创建评价系统表
  ```sql
  -- 互助评价表
  CREATE TABLE wo_minapper_help_ratings (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    request_id bigint(20) NOT NULL COMMENT '互助需求ID',
    rater_id bigint(20) NOT NULL COMMENT '评价者ID',
    rated_id bigint(20) NOT NULL COMMENT '被评价者ID',
    rating tinyint(1) NOT NULL COMMENT '评分1-5',
    comment text COMMENT '评价内容',
    type enum('requester_to_helper','helper_to_requester') NOT NULL,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY request_id (request_id),
    KEY rater_id (rater_id),
    KEY rated_id (rated_id)
  );
  ```

#### 1.2 后端API基础
- **任务1.2.1**: 创建互助控制器
  - 新建`raw-rest-help-controller.php`
  - 实现基础的CRUD操作
  - 确保与WordPress 6.8.2兼容

- **任务1.2.2**: 设置bbPress论坛分类
  - 在WordPress后台创建"邻里互助"论坛
  - 配置论坛权限和显示规则
  - 设置论坛基本信息

#### 1.3 前端基础配置
- **任务1.3.1**: 更新app.json配置
  - 添加互助相关页面路径
  - 配置页面权限和参数

### 模块二：互助需求发布功能（第2周）

#### 2.1 数据库扩展
- **任务2.1.1**: 扩展自定义字段
  - 在`wo_minapper_custom_fields`表中添加互助字段定义
  - 配置紧急程度、类型等选项

#### 2.2 后端API开发
- **任务2.2.1**: 发布需求API
  - `POST /minapper/v1/help/publish` - 发布互助需求
  - 集成bbPress topic创建
  - 实现数据验证和安全检查

- **任务2.2.2**: 图片上传API
  - 集成WordPress媒体库
  - 实现图片压缩和格式转换

#### 2.3 前端页面开发
- **任务2.3.1**: 创建发布页面
  - 新建`pages/help/publish/publish`页面
  - 实现表单UI和交互
  - 集成地理位置选择

- **任务2.3.2**: 表单验证和提交
  - 实现客户端验证
  - 添加草稿保存功能
  - 优化用户体验

### 模块三：互助列表和搜索功能（第3周）

#### 3.1 后端API开发
- **任务3.1.1**: 列表查询API
  - `GET /minapper/v1/help/list` - 获取互助列表
  - 实现分页、筛选、排序
  - 优化查询性能

- **任务3.1.2**: 搜索功能API
  - 实现关键词搜索
  - 地理位置筛选
  - 类型和紧急程度筛选

#### 3.2 前端页面开发
- **任务3.2.1**: 创建列表页面
  - 新建`pages/help/list/list`页面
  - 实现列表展示和分页
  - 添加筛选和搜索功能

- **任务3.2.2**: 优化列表性能
  - 实现虚拟滚动
  - 添加加载状态
  - 优化图片懒加载

### 模块四：互助详情和响应功能（第4周）

#### 4.1 后端API开发
- **任务4.1.1**: 详情查询API
  - `GET /minapper/v1/help/{id}` - 获取互助详情 
  - 包含需求信息和响应列表

- **任务4.1.2**: 响应管理API
  - `POST /minapper/v1/help/{id}/respond` - 提交响应
  - `POST /minapper/v1/help/{id}/select` - 选择帮助者
  - `POST /minapper/v1/help/{id}/cancel` - 取消需求

#### 4.2 前端页面开发
- **任务4.2.1**: 创建详情页面
  - 新建`pages/help/detail/detail`页面
  - 显示需求详细信息
  - 实现响应提交功能

- **任务4.2.2**: 响应管理界面
  - 显示响应列表
  - 实现选择帮助者功能
  - 添加进度跟踪显示

### 模块五：服务完成和评价功能（第5周）

#### 5.1 后端API开发
- **任务5.1.1**: 完成确认API
  - `POST /minapper/v1/help/{id}/complete` - 完成确认
  - 实现积分转账逻辑

- **任务5.1.2**: 评价系统API
  - `POST /minapper/v1/help/{id}/rate` - 提交评价
  - `GET /minapper/v1/help/ratings/{user_id}` - 获取用户评价

#### 5.2 前端页面开发
- **任务5.2.1**: 创建评价页面
  - 新建`pages/help/rate/rate`页面
  - 实现星级评价功能
  - 添加评价内容输入

- **任务5.2.2**: 积分系统集成
  - 显示积分变动
  - 实现积分冻结和转账
  - 添加积分记录查询

### 模块六：首页集成和个人中心（第6周）

#### 6.1 首页集成
- **任务6.1.1**: 首页服务入口
  - 在首页服务网格中添加"邻里互助"图标
  - 使用已实施的色彩方案（#FF6B35）
  - 配置跳转逻辑

- **任务6.1.2**: 首页动态展示
  - 在首页显示紧急互助需求
  - 添加快速响应入口

#### 6.2 个人中心集成
- **任务6.2.1**: 我的互助功能
  - `GET /minapper/v1/help/my-requests` - 我的发布
  - `GET /minapper/v1/help/my-responses` - 我的响应
  - 在个人中心添加互助统计

- **任务6.2.2**: 信誉度系统
  - 计算用户互助信誉度
  - 显示互助历史和评价
  - 实现信誉等级展示

### 模块七：消息通知和系统优化（第7周）

#### 7.1 消息通知系统
- **任务7.1.1**: 集成现有消息系统
  - 响应通知（有人响应需求）
  - 选择通知（被选为帮助者）
  - 完成通知（服务完成确认）

- **任务7.1.2**: 微信模板消息
  - 配置相关模板消息
  - 实现关键节点推送

#### 7.2 系统优化和测试
- **任务7.2.1**: 性能优化
  - 优化数据库查询
  - 实现缓存机制
  - 优化图片加载

- **任务7.2.2**: 全流程测试
  - 测试发布-响应-选择-完成全流程
  - 验证积分系统正确性
  - 测试异常情况处理

## 技术实现要点

### WordPress版本兼容性
- **目标版本**: WordPress 6.8.2
- **PHP版本要求**: PHP 8.0+ (推荐PHP 8.1+)
- **数据库**: MySQL 8.0+ 或 MariaDB 10.6+
- **bbPress版本**: 2.6+ (确保与WordPress 6.8.2兼容)

### 数据库设计（避免表名冲突）

基于现有数据库分析，使用`wo_minapper_help_`前缀避免与现有表冲突：

```sql
-- 1. 互助需求主表（与bbPress topics关联）
CREATE TABLE wo_minapper_help_requests (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  topic_id bigint(20) unsigned NOT NULL COMMENT '关联bbPress topic ID',
  user_id bigint(20) unsigned NOT NULL COMMENT '发布用户ID',
  urgency enum('urgent','normal','low') DEFAULT 'normal' COMMENT '紧急程度',
  help_type varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '互助类型',
  points_reward int DEFAULT 0 COMMENT '积分奖励',
  location_info text COLLATE utf8mb4_unicode_ci COMMENT '位置信息JSON',
  contact_info varchar(255) COLLATE utf8mb4_unicode_ci COMMENT '联系方式',
  status enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY topic_id (topic_id),
  KEY user_id (user_id),
  KEY status (status),
  KEY urgency (urgency),
  KEY help_type (help_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 互助响应表
CREATE TABLE wo_minapper_help_responses (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  request_id bigint(20) unsigned NOT NULL COMMENT '互助需求ID',
  user_id bigint(20) unsigned NOT NULL COMMENT '响应用户ID',
  content text COLLATE utf8mb4_unicode_ci COMMENT '响应内容',
  estimated_time varchar(100) COLLATE utf8mb4_unicode_ci COMMENT '预计完成时间',
  status enum('pending','selected','completed','rejected') DEFAULT 'pending',
  selected_at timestamp NULL COMMENT '被选中时间',
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY request_id (request_id),
  KEY user_id (user_id),
  KEY status (status),
  FOREIGN KEY (request_id) REFERENCES wo_minapper_help_requests(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 互助评价表
CREATE TABLE wo_minapper_help_ratings (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  request_id bigint(20) unsigned NOT NULL COMMENT '互助需求ID',
  rater_id bigint(20) unsigned NOT NULL COMMENT '评价者ID',
  rated_id bigint(20) unsigned NOT NULL COMMENT '被评价者ID',
  rating tinyint(1) NOT NULL COMMENT '评分1-5',
  comment text COLLATE utf8mb4_unicode_ci COMMENT '评价内容',
  type enum('requester_to_helper','helper_to_requester') NOT NULL,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY request_id (request_id),
  KEY rater_id (rater_id),
  KEY rated_id (rated_id),
  FOREIGN KEY (request_id) REFERENCES wo_minapper_help_requests(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 扩展自定义字段（复用现有表）
INSERT INTO wo_minapper_custom_fields (posttypes, category, fieldname, fieldkey, datatype, mark, orderby) VALUES
('topic', 'help', '紧急程度', 'help_urgency', 'select', '紧急,一般,不急', 1),
('topic', 'help', '互助类型', 'help_type', 'select', '家居维修,照顾宠物,搬运物品,技能咨询,其他', 2),
('topic', 'help', '积分奖励', 'help_points', 'number', NULL, 3),
('topic', 'help', '位置信息', 'help_location', 'text', NULL, 4),
('topic', 'help', '联系方式', 'help_contact', 'text', NULL, 5);
```

### API接口设计（按模块分组）

#### 基础接口
- `GET /minapper/v1/help/list` - 获取互助列表
  - 参数：page, per_page, urgency, help_type, location, keyword
  - 返回：分页的互助需求列表
- `GET /minapper/v1/help/{id}` - 获取互助详情
  - 返回：需求详情 + 响应列表

#### 发布管理接口
- `POST /minapper/v1/help/publish` - 发布互助需求
  - 参数：title, content, urgency, help_type, points_reward, location_info, contact_info
  - 返回：创建的需求ID
- `PUT /minapper/v1/help/{id}` - 修改互助需求
- `POST /minapper/v1/help/{id}/cancel` - 取消需求

#### 响应管理接口
- `POST /minapper/v1/help/{id}/respond` - 响应互助需求
  - 参数：content, estimated_time
- `POST /minapper/v1/help/{id}/select` - 选择帮助者
  - 参数：response_id
- `POST /minapper/v1/help/{id}/complete` - 完成确认

#### 评价系统接口
- `POST /minapper/v1/help/{id}/rate` - 提交评价
  - 参数：rated_id, rating, comment, type
- `GET /minapper/v1/help/ratings/{user_id}` - 获取用户评价统计

#### 个人中心接口
- `GET /minapper/v1/help/my-requests` - 我的发布
- `GET /minapper/v1/help/my-responses` - 我的响应
- `GET /minapper/v1/help/my-stats` - 我的互助统计

### 首页集成方案
在现有首页的服务网格中添加邻里互助入口，使用已实施的色彩方案：
```javascript
// 在pages/index/index.wxml的service-grid中添加
<navigator url="/pages/help/list/list" class="service-item">
  <view class="service-icon help" style="background: linear-gradient(135deg, #FF6B35, #FFB74D);">
    <i class="ri-heart-line"></i>
  </view>
  <view class="service-name">邻里互助</view>
</navigator>
```

### 状态流转设计
```
需求状态流转：
pending（待响应） -> in_progress（进行中） -> completed（已完成）
                                      \-> cancelled（已取消）

响应状态流转：
pending（待选择） -> selected（已选中） -> completed（已完成）
                                   \-> rejected（被拒绝）

积分流转：
发布需求 -> 积分冻结 -> 选择帮助者 -> 服务完成 -> 积分转账
```

### 数据库表关系
```
wo_posts (bbPress topics)
    ↓ (1:1)
wo_minapper_help_requests (互助需求扩展)
    ↓ (1:N)
wo_minapper_help_responses (响应记录)
    ↓ (1:N)
wo_minapper_help_ratings (评价记录)
```

## 开发时间估算（按模块）
- **模块一（基础数据结构）**: 1周
- **模块二（发布功能）**: 1周
- **模块三（列表搜索）**: 1周
- **模块四（详情响应）**: 1周
- **模块五（完成评价）**: 1周
- **模块六（首页集成）**: 1周
- **模块七（优化测试）**: 1周

**总计**: 7周

## 风险评估
1. **技术风险**: bbPress系统扩展与WordPress 6.8.2的兼容性问题
2. **性能风险**: 大量互助需求时的查询性能
3. **用户体验风险**: 流程复杂度对用户的影响
4. **数据风险**: 积分系统的准确性和安全性
5. **集成风险**: 与现有首页服务网格的视觉一致性
6. **版本风险**: WordPress 6.8.2新特性的学习成本和潜在bug

## 成功标准
1. 用户能够顺利发布和响应互助需求
2. 积分系统运行准确无误
3. 页面加载速度在3秒以内
4. 用户操作流程清晰易懂
5. 系统稳定性达到99%以上
6. 首页集成无缝，用户体验流畅

## 注意事项
1. **数据库表名冲突避免**:
   - 使用`wo_minapper_help_`前缀避免与现有表冲突
   - 已分析现有数据库，确保新表名唯一性
   - 复用`wo_minapper_custom_fields`等现有表结构

2. **WordPress 6.8.2兼容性**:
   - 确保所有代码与最新WordPress版本兼容
   - 利用新版本的性能优化和安全特性
   - 使用最新的REST API标准

3. **模块化开发**:
   - 按功能模块顺序开发，每个模块包含完整的数据库、后端、前端
   - 确保模块间的依赖关系清晰
   - 便于测试和调试

4. **去除私信功能**:
   - 不实现私信功能，简化用户交流流程
   - 通过系统内置的响应和选择机制进行交流

5. **首页入口设计**:
   - 使用已实施的色彩方案（#FF6B35主色）
   - 确保与现有服务图标的视觉一致性
   - 保持简洁的用户体验

6. **数据安全和隐私**:
   - 用户位置信息加密存储
   - 联系方式仅在必要时显示
   - 实现数据访问权限控制

7. **积分系统安全**:
   - 实现积分冻结机制，防止恶意刷分
   - 确保积分转账的原子性操作
   - 添加积分变动日志记录

8. **性能优化**:
   - 数据库查询优化，添加必要索引
   - 实现缓存机制减少数据库压力
   - 前端页面懒加载和虚拟滚动