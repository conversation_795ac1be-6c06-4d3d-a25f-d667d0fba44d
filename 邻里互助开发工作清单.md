# 邻里互助模块开发工作清单

## 项目概述
基于现有bbPress论坛系统扩展，利用forums/topics结构创建专门的"邻里互助"论坛分类，通过自定义字段扩展互助特有属性，复用现有的用户系统和评论回复机制。邻里互助模块作为首页服务入口，不占用底部导航栏位置。

T0

## 邻里互助模块完整流程设计

### 1. 需求发布流程
**发布者操作：**
- 选择紧急程度（紧急/一般/不急）
- 填写标题和详细描述
- 选择互助类型（家居维修/照顾宠物/搬运物品/技能咨询/其他）
- 设置位置信息
- 设定积分奖励
- 发布需求

### 2. 响应和选择流程
**帮助者操作：**
- 浏览互助列表
- 查看详情并响应需求
- 填写响应说明和预计时间

**发布者操作：**
- 查看所有响应
- 选择合适的帮助者
- 确认服务安排

### 3. 服务进行流程
**系统状态更新：**
- 自动更新为"服务进行中"
- 显示帮助者联系方式
- 提供进度跟踪

### 4. 完成确认流程
**发布者操作：**
- 确认服务完成
- 对帮助者进行评价（1-5星）
- 积分自动转账

**帮助者操作：**
- 标记服务完成
- 对发布者进行评价

### 5. 异常处理流程
- 取消需求功能
- 更换帮助者功能
- 申诉和客服处理

## 开发工作清单

### 阶段一：数据库设计和后端基础（第1-2周）

#### 1.1 扩展bbPress数据结构
- **任务1.1.1**: 创建邻里互助专用论坛分类
  - 在WordPress后台创建"邻里互助"论坛
  - 设置论坛权限和显示规则
  - 配置论坛基本信息

- **任务1.1.2**: 扩展自定义字段表结构
  - 在`minapper_custom_fields`表中添加互助相关字段定义
  - 创建互助需求的元数据字段（紧急程度、积分奖励、位置、类型等）
  - 设计响应记录的数据结构

- **任务1.1.3**: 创建互助响应表
  - 设计`minapper_help_responses`表存储用户响应
  - 包含字段：响应ID、话题ID、响应用户ID、响应内容、响应时间、状态等
  - 创建相关索引优化查询性能

#### 1.2 后端API开发
- **任务1.2.1**: 扩展forums控制器
  - 修改`raw-rest-forums-controller.php`
  - 添加互助需求发布接口
  - 添加互助列表查询接口（支持筛选和分页）

- **任务1.2.2**: 创建互助响应API
  - 开发响应提交接口
  - 开发响应列表查询接口
  - 开发帮助者选择接口

- **任务1.2.3**: 集成积分系统
  - 修改积分相关API支持互助奖励
  - 实现积分冻结和转账机制
  - 添加积分变动记录

### 阶段二：前端页面开发（第3-4周）

#### 2.1 互助列表页面
- **任务2.1.1**: 创建互助列表页面
  - 新建`pages/help/list`页面
  - 实现筛选功能（类型、紧急程度、距离）
  - 实现列表展示和分页加载

- **任务2.1.2**: 实现搜索功能
  - 添加关键词搜索
  - 实现地理位置筛选
  - 优化列表性能

#### 2.2 发布互助页面
- **任务2.2.1**: 创建发布页面
  - 新建`pages/help/publish`页面
  - 实现表单验证和数据提交
  - 集成地理位置选择

- **任务2.2.2**: 优化用户体验
  - 添加图片上传功能
  - 实现草稿保存
  - 添加发布成功反馈

#### 2.3 互助详情页面
- **任务2.3.1**: 创建详情页面
  - 新建`pages/help/detail`页面
  - 显示需求详细信息
  - 实现响应提交功能

- **任务2.3.2**: 实现进度跟踪
  - 显示服务进度状态
  - 实现状态更新功能
  - 添加完成确认功能

### 阶段三：用户交互功能（第5周）

#### 3.1 响应管理功能
- **任务3.1.1**: 响应列表展示
  - 在详情页显示所有响应
  - 实现响应者信息展示
  - 添加选择帮助者功能

- **任务3.1.2**: 状态管理
  - 实现需求状态更新
  - 添加取消和修改功能
  - 处理异常状态

#### 3.2 评价系统
- **任务3.2.1**: 创建评价页面
  - 新建`pages/help/rate`页面
  - 实现星级评价功能
  - 添加评价内容输入

- **任务3.2.2**: 评价展示
  - 在用户资料中显示互助评价
  - 计算用户互助信誉度
  - 实现评价统计

### 阶段四：首页集成和系统优化（第6周）

#### 4.1 首页集成
- **任务4.1.1**: 首页添加邻里互助入口
  - 在首页服务网格中添加"邻里互助"图标
  - 设计互助专用图标和样式（使用爱心图标ri-heart-line）
  - 配置跳转到互助列表页面
  - 保持与现有服务图标的视觉一致性

- **任务4.1.2**: 首页互助信息展示（可选功能）
  - 在首页最新动态中显示紧急互助需求
  - 添加互助需求数量提示
  - 实现快速响应入口

#### 4.2 页面路由配置
- **任务4.2.1**: 更新app.json配置
  - 在pages数组中添加互助相关页面路径：
    - `pages/help/list/list`
    - `pages/help/publish/publish`
    - `pages/help/detail/detail`
    - `pages/help/rate/rate`
  - 配置页面权限和参数
  - 更新页面预加载设置

- **任务4.2.2**: 导航优化
  - 优化页面间跳转逻辑
  - 添加面包屑导航
  - 实现返回和分享功能
  - 确保从首页进入的用户体验流畅

#### 4.3 消息通知
- **任务4.3.1**: 集成现有消息系统
  - 响应通知（有人响应需求）
  - 选择通知（被选为帮助者）
  - 完成通知（服务完成确认）

- **任务4.3.2**: 微信模板消息
  - 配置相关模板消息
  - 实现关键节点推送
  - 处理用户订阅状态

### 阶段五：测试和优化（第7周）

#### 5.1 功能测试
- **任务5.1.1**: 完整流程测试
  - 测试发布-响应-选择-完成全流程
  - 测试各种异常情况处理
  - 验证积分系统正确性

- **任务5.1.2**: 性能优化
  - 优化列表查询性能
  - 实现数据缓存机制
  - 优化图片加载

#### 5.2 用户体验优化
- **任务5.2.1**: UI/UX优化
  - 根据测试反馈优化界面
  - 完善交互动画
  - 优化加载状态显示

- **任务5.2.2**: 错误处理
  - 完善错误提示信息
  - 添加网络异常处理
  - 实现离线数据缓存

## 技术实现要点

### 数据库设计
```sql
-- 扩展自定义字段
INSERT INTO wo_minapper_custom_fields (posttypes, category, fieldname, fieldkey, datatype, mark, orderby) VALUES
('topic', 'help', '紧急程度', 'help_urgency', 'select', '紧急,一般,不急', 1),
('topic', 'help', '互助类型', 'help_type', 'select', '家居维修,照顾宠物,搬运物品,技能咨询,其他', 2),
('topic', 'help', '积分奖励', 'help_points', 'number', NULL, 3),
('topic', 'help', '位置信息', 'help_location', 'text', NULL, 4),
('topic', 'help', '联系方式', 'help_contact', 'text', NULL, 5),
('topic', 'help', '服务状态', 'help_status', 'select', 'pending,in_progress,completed,cancelled', 6);

-- 创建响应表
CREATE TABLE wo_minapper_help_responses (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  topic_id bigint(20) NOT NULL,
  user_id bigint(20) NOT NULL,
  content text,
  estimated_time varchar(100),
  status varchar(20) DEFAULT 'pending',
  selected tinyint(1) DEFAULT 0,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY topic_id (topic_id),
  KEY user_id (user_id),
  KEY status (status)
);

-- 创建评价表
CREATE TABLE wo_minapper_help_ratings (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  topic_id bigint(20) NOT NULL,
  rater_id bigint(20) NOT NULL,
  rated_id bigint(20) NOT NULL,
  rating tinyint(1) NOT NULL,
  comment text,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY topic_id (topic_id),
  KEY rater_id (rater_id),
  KEY rated_id (rated_id)
);
```

### API接口设计
- `GET /minapper/v1/help/list` - 获取互助列表
- `POST /minapper/v1/help/publish` - 发布互助需求
- `GET /minapper/v1/help/{id}` - 获取互助详情
- `POST /minapper/v1/help/{id}/respond` - 响应互助需求
- `POST /minapper/v1/help/{id}/select` - 选择帮助者
- `POST /minapper/v1/help/{id}/complete` - 完成确认
- `POST /minapper/v1/help/{id}/rate` - 评价功能
- `POST /minapper/v1/help/{id}/cancel` - 取消需求
- `GET /minapper/v1/help/my-requests` - 我的发布
- `GET /minapper/v1/help/my-responses` - 我的响应

### 首页集成方案
在现有首页的服务网格中添加邻里互助入口：
```javascript
// 在pages/index/index.wxml的service-grid中添加
<navigator url="/pages/help/list/list" class="service-item">
  <view class="service-icon help">
    <i class="ri-heart-line"></i>
  </view>
  <view class="service-name">邻里互助</view>
</navigator>
```

### 状态流转设计
```
需求状态流转：
pending（待响应） -> in_progress（进行中） -> completed（已完成） / cancelled（已取消）

响应状态流转：
pending（待选择） -> selected（已选中） -> completed（已完成）
```

## 开发时间估算
- **阶段一（数据库和后端）**: 2周
- **阶段二（前端页面）**: 2周
- **阶段三（用户交互）**: 1周
- **阶段四（首页集成）**: 1周
- **阶段五（测试优化）**: 1周

**总计**: 7周

## 风险评估
1. **技术风险**: bbPress系统扩展的兼容性问题
2. **性能风险**: 大量互助需求时的查询性能
3. **用户体验风险**: 流程复杂度对用户的影响
4. **数据风险**: 积分系统的准确性和安全性
5. **集成风险**: 与现有首页服务网格的视觉一致性

## 成功标准
1. 用户能够顺利发布和响应互助需求
2. 积分系统运行准确无误
3. 页面加载速度在3秒以内
4. 用户操作流程清晰易懂
5. 系统稳定性达到99%以上
6. 首页集成无缝，用户体验流畅

## 注意事项
1. **去除私信功能**: 按照要求，不实现私信功能，用户通过系统内置的响应和选择机制进行交流
2. **首页入口设计**: 确保邻里互助图标与现有服务图标保持视觉一致性
3. **用户权限**: 复用现有的用户认证和权限系统
4. **数据安全**: 确保用户位置信息和联系方式的隐私保护
5. **积分安全**: 实现积分冻结机制，防止恶意刷分