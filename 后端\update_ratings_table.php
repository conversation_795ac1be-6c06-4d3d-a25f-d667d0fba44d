<?php
/**
 * 更新评价表字段名脚本
 * 将 type 字段重命名为 rating_type
 */

// 引入WordPress环境
require_once('../../../wp-config.php');

global $wpdb;

echo "<h2>更新互助评价表字段名</h2>";

$table_name = $wpdb->prefix . 'minapper_help_ratings';

// 检查表是否存在
if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
    echo "<p style='color: red;'>✗ 表 {$table_name} 不存在，请先运行创建表脚本</p>";
    exit;
}

// 检查是否已经有 rating_type 字段
$columns = $wpdb->get_results("SHOW COLUMNS FROM {$table_name}");
$has_rating_type = false;
$has_type = false;

foreach ($columns as $column) {
    if ($column->Field == 'rating_type') {
        $has_rating_type = true;
    }
    if ($column->Field == 'type') {
        $has_type = true;
    }
}

if ($has_rating_type && !$has_type) {
    echo "<p style='color: green;'>✓ 表结构已经是最新的，rating_type 字段已存在</p>";
    exit;
}

if ($has_type && !$has_rating_type) {
    // 需要重命名字段
    echo "<p>正在将 type 字段重命名为 rating_type...</p>";
    
    $sql = "ALTER TABLE {$table_name} CHANGE COLUMN `type` `rating_type` enum('requester_to_helper','helper_to_requester') NOT NULL";
    
    $result = $wpdb->query($sql);
    
    if ($result !== false) {
        echo "<p style='color: green;'>✓ 字段重命名成功: type -> rating_type</p>";
    } else {
        echo "<p style='color: red;'>✗ 字段重命名失败: " . $wpdb->last_error . "</p>";
    }
} else if ($has_type && $has_rating_type) {
    // 两个字段都存在，删除旧的 type 字段
    echo "<p>检测到同时存在 type 和 rating_type 字段，正在删除旧的 type 字段...</p>";
    
    $sql = "ALTER TABLE {$table_name} DROP COLUMN `type`";
    
    $result = $wpdb->query($sql);
    
    if ($result !== false) {
        echo "<p style='color: green;'>✓ 旧字段删除成功: type</p>";
    } else {
        echo "<p style='color: red;'>✗ 旧字段删除失败: " . $wpdb->last_error . "</p>";
    }
} else {
    // 都不存在，添加 rating_type 字段
    echo "<p>正在添加 rating_type 字段...</p>";
    
    $sql = "ALTER TABLE {$table_name} ADD COLUMN `rating_type` enum('requester_to_helper','helper_to_requester') NOT NULL AFTER `comment`";
    
    $result = $wpdb->query($sql);
    
    if ($result !== false) {
        echo "<p style='color: green;'>✓ 字段添加成功: rating_type</p>";
    } else {
        echo "<p style='color: red;'>✗ 字段添加失败: " . $wpdb->last_error . "</p>";
    }
}

// 显示最终的表结构
echo "<h3>当前表结构:</h3>";
$columns = $wpdb->get_results("SHOW COLUMNS FROM {$table_name}");
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>{$column->Field}</td>";
    echo "<td>{$column->Type}</td>";
    echo "<td>{$column->Null}</td>";
    echo "<td>{$column->Key}</td>";
    echo "<td>{$column->Default}</td>";
    echo "<td>{$column->Extra}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>更新完成</h3>";
echo "<p>现在可以正常使用评价功能了。</p>";

?>
