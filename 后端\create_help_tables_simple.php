<?php
/**
 * 简单的邻里互助数据库表创建脚本
 * 直接访问此文件即可创建表
 */

// 引入WordPress环境
require_once('wp-config.php');

global $wpdb;

echo "<h2>邻里互助数据库表创建</h2>";

// 获取表前缀
$prefix = $wpdb->prefix;

// 1. 创建互助需求表
$table_name = $prefix . 'minapper_help_requests';
$sql1 = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `topic_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联bbPress topic ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '发布用户ID',
  `urgency` enum('urgent','normal','low') DEFAULT 'normal' COMMENT '紧急程度',
  `help_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '其他' COMMENT '互助类型',
  `points_reward` int DEFAULT 0 COMMENT '积分奖励',
  `location_info` text COLLATE utf8mb4_unicode_ci COMMENT '位置信息JSON',
  `contact_info` varchar(255) COLLATE utf8mb4_unicode_ci COMMENT '联系方式',
  `status` enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `urgency` (`urgency`),
  KEY `help_type` (`help_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

$result1 = $wpdb->query($sql1);
if ($result1 !== false) {
    echo "<p style='color: green;'>✓ 互助需求表创建成功: {$table_name}</p>";
} else {
    echo "<p style='color: red;'>✗ 互助需求表创建失败: " . $wpdb->last_error . "</p>";
}

// 2. 创建互助响应表
$table_name = $prefix . 'minapper_help_responses';
$sql2 = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `request_id` bigint(20) UNSIGNED NOT NULL COMMENT '互助需求ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '响应用户ID',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '响应内容',
  `estimated_time` varchar(100) COLLATE utf8mb4_unicode_ci COMMENT '预计完成时间',
  `status` enum('pending','selected','completed','rejected') DEFAULT 'pending',
  `selected_at` timestamp NULL COMMENT '被选中时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `request_id` (`request_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

$result2 = $wpdb->query($sql2);
if ($result2 !== false) {
    echo "<p style='color: green;'>✓ 互助响应表创建成功: {$table_name}</p>";
} else {
    echo "<p style='color: red;'>✗ 互助响应表创建失败: " . $wpdb->last_error . "</p>";
}

// 3. 创建互助评价表
$table_name = $prefix . 'minapper_help_ratings';
$sql3 = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `request_id` bigint(20) UNSIGNED NOT NULL COMMENT '互助需求ID',
  `rater_id` bigint(20) UNSIGNED NOT NULL COMMENT '评价者ID',
  `rated_id` bigint(20) UNSIGNED NOT NULL COMMENT '被评价者ID',
  `rating` tinyint(1) NOT NULL COMMENT '评分1-5',
  `comment` text COLLATE utf8mb4_unicode_ci COMMENT '评价内容',
  `rating_type` enum('requester_to_helper','helper_to_requester') NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `request_id` (`request_id`),
  KEY `rater_id` (`rater_id`),
  KEY `rated_id` (`rated_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

$result3 = $wpdb->query($sql3);
if ($result3 !== false) {
    echo "<p style='color: green;'>✓ 互助评价表创建成功: {$table_name}</p>";
} else {
    echo "<p style='color: red;'>✗ 互助评价表创建失败: " . $wpdb->last_error . "</p>";
}

// 4. 检查自定义字段表是否存在
$custom_fields_table = $prefix . 'minapper_custom_fields';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$custom_fields_table'") == $custom_fields_table;

if ($table_exists) {
    // 添加自定义字段
    $custom_fields = array(
        array('topic', 'help', '紧急程度', 'help_urgency', 'select', '紧急,一般,不急', 1),
        array('topic', 'help', '互助类型', 'help_type', 'select', '家居维修,照顾宠物,搬运物品,技能咨询,其他', 2),
        array('topic', 'help', '积分奖励', 'help_points', 'number', NULL, 3),
        array('topic', 'help', '位置信息', 'help_location', 'text', NULL, 4),
        array('topic', 'help', '联系方式', 'help_contact', 'text', NULL, 5)
    );
    
    foreach ($custom_fields as $field) {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$custom_fields_table} WHERE category = %s AND fieldkey = %s",
            $field[1], $field[3]
        ));
        
        if (!$existing) {
            $result = $wpdb->insert(
                $custom_fields_table,
                array(
                    'posttypes' => $field[0],
                    'category' => $field[1],
                    'fieldname' => $field[2],
                    'fieldkey' => $field[3],
                    'datatype' => $field[4],
                    'mark' => $field[5],
                    'orderby' => $field[6]
                )
            );
            
            if ($result) {
                echo "<p style='color: green;'>✓ 自定义字段添加成功: {$field[2]}</p>";
            } else {
                echo "<p style='color: red;'>✗ 自定义字段添加失败: {$field[2]} - " . $wpdb->last_error . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>- 自定义字段已存在: {$field[2]}</p>";
        }
    }
} else {
    echo "<p style='color: orange;'>⚠ 自定义字段表不存在，跳过自定义字段创建</p>";
}

// 5. 插入测试数据
echo "<h3>插入测试数据</h3>";
$test_data = array(
    'user_id' => 1,
    'urgency' => 'normal',
    'help_type' => '家居维修',
    'points_reward' => 50,
    'location_info' => '{"address": "测试地址", "latitude": 0, "longitude": 0}',
    'contact_info' => '微信：test123',
    'status' => 'pending'
);

$help_table = $prefix . 'minapper_help_requests';
$existing_test = $wpdb->get_var("SELECT id FROM {$help_table} WHERE user_id = 1 AND help_type = '家居维修'");

if (!$existing_test) {
    $result = $wpdb->insert($help_table, $test_data);
    if ($result) {
        echo "<p style='color: green;'>✓ 测试数据插入成功</p>";
    } else {
        echo "<p style='color: red;'>✗ 测试数据插入失败: " . $wpdb->last_error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>- 测试数据已存在</p>";
}

echo "<hr>";
echo "<h3>测试API</h3>";
$api_url = home_url('/wp-json/minapper/v1/help/list');
echo "<p>API地址: <a href='$api_url' target='_blank'>$api_url</a></p>";
echo "<p>点击上面的链接测试API是否正常工作</p>";

echo "<hr>";
echo "<p><strong>完成！</strong>现在可以测试邻里互助功能了。</p>";
echo "<p><small>测试完成后请删除此文件：create_help_tables_simple.php</small></p>";
?>
