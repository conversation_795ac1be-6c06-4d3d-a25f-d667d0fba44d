//  * 微慕小程序
//  * author: jianbo
//  * organization:  微慕 www.minapper.com 
//  * 技术支持微信号：Jianbo
//  * Copyright (c) 2018 https://www.minapper.com All rights reserved.


// 配置域名
// 如果wordpress没有安装在网站根目录请加上目录路径,例如："www.minapper.com/blog"
const getDomain = "m.wolinwarm.com";
//////////////////////////////////////////////////////

//显示小红点的tabbar的位置，第一个用0，第二个用1 以此类推。
const getTabBarRedDotIndex=3
//////////////////////////////////////////////////////


// 上传图片的最大文件大小,单位是m,必须填整数,
// 同时必须修改php.ini文件 post_max_size 和 upload_max_filesize 具体修改请自行搜索
const uploadImageSize=10
//////////////////////////////////////////////////////

// 上传音频和视频的最大文件大小,单位是m,必须填整数,
// 同时必须修改php.ini文件 post_max_size 和 upload_max_filesize 具体修改请自行搜索
const uploadMediaSize=30
//////////////////////////////////////////////////////

// 上传doc, docx, ppt, pptx, xls, xlsx, pdf文件最大文件大小,单位是m,必须填整数,
// 同时必须修改php.ini文件 post_max_size 和 upload_max_filesize 具体修改请自行搜索
const uploadFileSize=10
//////////////////////////////////////////////////////

// 默认文章列表样式：1 左图 2 右图 3 大图 4 多图 5 瀑布流 6 无图
const articleStyle = 4
//////////////////////////////////////////////////////

// 作者主页文章列表样式：1 左图 2 右图 3 大图 4 多图 5 瀑布流 6 无图
const authorPostArticleStyle = 4
//////////////////////////////////////////////////////

// 作者主页话题列表样式：1 左图 2 右图 3 大图 4 多图 5 瀑布流 6 无图
const authorTopicArticleStyle = 5
//////////////////////////////////////////////////////

//是否启用小程序扫描二维码登录网站,  true 启用  false  不启用
//未安装微慕登录插件不要启用,插件下载地址：https://www.minapper.com/shops/
const enableScanLogin =true 
//////////////////////////////////////////////////////

//是否启用微慕视频号插件,  true 启用  false  不启用
//未安装微慕视频号插件不要启用,插件下载地址：https://www.minapper.com/shops/
const enableChannels =true 
//////////////////////////////////////////////////////


//是否启用积分商城插件,  true 启用  false  不启用
//未安装微慕积分商城插件不要启用,插件下载地址：https://www.minapper.com/shops/
const enableMinapperShop =true  
//////////////////////////////////////////////////////

//是否绑定微信开放平台,  true 绑定  false  未绑定
//无微信开放平台不要开启
const enableWeixinOpen =true 
//////////////////////////////////////////////////////

//是否启用微信小店,  true 启用  false  不启用
//未安装微信小店插件不要启用,插件下载地址：https://www.minapper.com/shops/
const enableWechatshop =true 
//////////////////////////////////////////////////////

//是否启用微信小商店,  true 启用  false  不启用 (微信小商店已暂停，此配置已废弃)
//未开通小程序交易组件不要启用
//const enableMinishop =false 
//////////////////////////////////////////////////////

//浮动导航是否展开 true 展开  false  不展开
const  showSidePop=false 
//////////////////////////////////////////////////////

//视频中心默认打开的标签  datetime：最新   rand：发现 recommend:推荐  follow:关注
const  tabVideo='recommend' 
//////////////////////////////////////////////////////

//地址管理，是否可以导入微信地址（需要申请权限）
const  enableChooseAddress = true
//////////////////////////////////////////////////////


//微慕小程序端版本,请勿修改
const minapperVersion=5.67
const minapperSource="pro"
//////////////////////////////////////////////////////


export default {
  getDomain,
  getTabBarRedDotIndex,
  articleStyle,
  authorPostArticleStyle,
  authorTopicArticleStyle,
  minapperVersion,
  uploadImageSize,
  uploadMediaSize,
  uploadFileSize,
  minapperSource,
  enableScanLogin,
  showSidePop,
  enableWeixinOpen,
  tabVideo,
  enableChannels,
  enableMinapperShop,
  enableChooseAddress,
  enableWechatshop,
  //enableMinishop
}