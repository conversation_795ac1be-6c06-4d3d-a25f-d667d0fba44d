const API = require('../../utils/api.js')
const Auth = require('../../utils/auth.js')
const Adapter = require('../../utils/adapter.js')
const util = require('../../utils/util.js')
const NC = require('../../utils/notificationcenter.js')

import config from '../../utils/config.js'
import { ModalView } from '../../templates/modal-view/modal-view.js'
import Poster from '../../templates/components/wxa-plugin-canvas-poster/poster/poster'
const app = getApp()
const innerAudioContext = wx.createInnerAudioContext()
let ctx = wx.createCanvasContext('mycanvas')
const pageCount = 10
let isFocusing = false
let rewardedVideoAd = null
import Dialog from '../../miniprogram_npm/@vant/weapp/dialog/dialog'
const behavior = require('../../utils/new/behavior.js')


Page({
  behaviors: [behavior],
  // 初始数据
  data: {
    parentId: "0",
    shareTitle: "",
    pageTitle: "",
    postId: "",
    detail: {},
    commentCounts: 0,
    relatedPostList: [],
    commentsList: [],
    display: false,
    displaygoods: false,
    displaytags: false,
    displaymp: false,
    page: 1,
    isLastPage: false,
    isLoading: false,
    isPull: false,
    toolbarShow: true,
    commentInputDialogShow: false,
    iconBarShow: false,
    menuBackgroup: false,

    focus: false,
    placeholder: "说点什么...",
    toUserId: "",
    toFormId: "",
    commentdate: "",
    content: "",

    dialog: {
      title: "",
      content: "",
      hidden: true
    },
    userSession: {},
    wxLoginInfo: {},
    memberUserInfo: {},
    userInfo: {},

    isLike: false,
    downloadFileDomain: '',
    businessDomain: '',
    logo: app.globalData.appLogo,
    domain: config.getDomain,
    platform: '',
    posterConfig: {},

    system: '',

    isPlayAudio: false,
    audioSeek: 0,
    audioDuration: 0,
    showTime1: '00:00',
    showTime2: '00:00',
    audioTime: 0,
    displayAudio: 'none',
    shareImagePath: '',
    detailSummaryHeight: '',
    detailAdsuccess: true,
    detailTopAdsuccess:true,
    fristOpen: false,
    popupShow:false,
    columns: [],
    pageName:"detail",
    isShowSubscribe: true,
    isPaySuccess: false,

    insertWxPopupShow: false, // 嵌入公众号弹出层
    insertChannelsPopupShow:false,//嵌入视频弹出层
    appID: '',
    pagePath: '',
    showPopPhone: false,
    banner: {},
    showPopPoints: false,
    onlyVideo: true, // 默认为false的话视频样式下腾讯视频插件会报错
    showAfficialAccount:false,

    format: '', // 文章格式
    brotherVideo: [], // 相关视频
    showVideoDes: false, // 是否显示视频描述
    isShareTimeline: false, // 是否安卓朋友圈打开的 “单页模式”(很多小程api和功能不能用)
    imgCurrent: 0,

    swiperImgs: [],
    swiperImgHieght: 300,
    isFixedSwiper: false,
    hasWechatInstall: app.globalData.hasWechatInstall
  },

  // 页面加载
  onLoad: function (option) {  
    this.setPageInfo()
    let self = this

    var LaunchOptions = wx.getLaunchOptionsSync();
    var scene = LaunchOptions['scene'];

    if (scene == 1001 || scene == 1047 || scene == 1124 || scene == 1089 || scene == 1038 || scene == 1011 || scene == 1017) {
      self.setData({ showAfficialAccount: true })
    }
    let isShareTimeline = scene === 1154

    let args = {}
    args.id = option.id
    args.postType = "post"
    Auth.checkSession(app, API, this, 'isLoginLater', util)

    let system =/ios/i.test(app.globalData.deviceInfo.system)? 'iOS' : 'Android'
    let platform=app.globalData.deviceInfo.platform
    self.setData({
      system: system,
      platform: platform
    })

    self.setData({
      postId: option.id,
      pagePath: `pages/detail/detail?id=${option.id}`,
      format: option.format,
      isShareTimeline
    })

    args.userId = self.data.userSession.userId
    args.sessionId = self.data.userSession.sessionId

    // if (option.format === 'video'){
    //   this.getVideoDetail()
    // }
    let excitationOpened = self.getExcitationOpened(option.id)
    Adapter.loadArticleDetail(args, self, API, util, innerAudioContext, ctx, excitationOpened, isShareTimeline)
    

    //Auth.checkLogin(self)
    new ModalView

    // 设置系统分享菜单
    wx.showShareMenu({
      withShareTicket: false,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    Adapter.getCustomBanner(self,API,'post_detail_top_nav')
  },

  onShow: function () {
    let self = this;    
    if (this.data.isPaySuccess) {
      //self.onPullDownRefresh();
      self.detailRefresh();
      self.setData({isPaySuccess:false})
    }
    if (this.data.userSession.sessionId) {
      Auth.checkGetMemberUserInfo(this.data.userSession, this, API)
    }

    if (this.data.showPopPoints && this.selectComponent("#count-down")) {
      this.selectComponent("#count-down").start()
    }
  },

  // 分享
  onShareAppMessage() {
    var self = this
    var imageUrl = self.data.detail.postcover || self.data.detail.post_full_image
    //var path = `/pages/detail/detail?id=${self.data.postId}&format=${self.data.format}`
    var path=`/pages/index/index?from=detail&id=${self.data.postId}&format=${self.data.format}`
    let invitecode = this.data.memberUserInfo.invitecode
    if(invitecode) {
      path +='&invitecode='+invitecode
    }

    return {
      title: self.data.detail.title.rendered,
      path:path,   
      imageUrl
    }
  },

  // 自定义分享朋友圈
  onShareTimeline: function() {
    let imageUrl = this.data.detail.postcover || this.data.detail.post_full_image

    return {
      title: this.data.detail.title.rendered,
      query: {
        id: this.data.detail.id
      },
      imageUrl
    }
  },

  // onHide: function() {
  //   if (this.data.showPopPoints) {
  //     this.selectComponent("#count-down").pause()
  //   }
  // },

  // 页面销毁
  onUnload: function () {
    // 清除定时器
    clearInterval(this.data.durationIntval)
    if (rewardedVideoAd && rewardedVideoAd.destroy) {
      rewardedVideoAd.destroy()
    }
    innerAudioContext.stop()
    ctx = null
  },

  // 监听页面滚动
  onPageScroll: function (res) {
    const scrollTop = res.scrollTop
    let params = {}
    const format = this.data.format
    // if (['image', 'gallery', 'postformatgallery'].includes(format)) {
    //   params.isFixedSwiper = scrollTop > 10
    // }

    // params.isShowSubscribe = scrollTop < 100
    // this.setData(params)
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    var self = this
    let args = {}
    Auth.checkLogin(self)
    Auth.setUserMemberInfoData(self) 
    args.id = self.data.postId
    args.postType = 'post'  
    args.userId = self.data.userSession.userId
    args.sessionId = self.data.userSession.sessionId
    self.setData({
      relatedPostList: [],
      isPull: true,
      detailAdsuccess: true,
      detailTopAdsuccess:true,
      display: false
    })

    // if (this.data.format === 'video')
    // {
    //   this.getVideoDetail();
    // }
    var excitationOpened=self.getExcitationOpened(self.data.postId);
      Adapter.loadArticleDetail(args, self,  API, util, innerAudioContext, ctx, excitationOpened)
   
  },

  // 上拉加载
  onReachBottom: function () {
    let args = {}
    args.userId = this.data.userSession.userId
    args.sessionId = this.data.userSession.sessionId
    args.postId = this.data.postId
    args.limit = pageCount
    args.page = this.data.page
    args.flag = 'postcomment'
    if (!this.data.isLastPage) {      
      Adapter.loadComments(args, this, API)
    }
  },

  changeImgSwiper(e) {
    const { current } = e.detail
    this.setData({
      imgCurrent: current
    })
  },

  previewImg(e) {
    const { url, urls } = e.currentTarget.dataset
    wx.previewImage({
      current: url,
      urls
    })
  },

  swiperImgLoad(e) {
    const { index } = e.currentTarget.dataset
    const { width, height } = e.detail
    this.data.swiperImgs[index] = {
      width,
      height,
      h: 750 * height / width
    }
    let minHeight = 0
    this.data.swiperImgs.map(m => {
      if (!minHeight) {
        minHeight = m.h
      } else if (m.h < minHeight) {
        minHeight = m.h
      }
    })
    // 设置最大高度 600，否则高度最小的图片太高可能导致滑动区域太小
    if (minHeight > 600) minHeight = 600

    this.setData({
      swiperImgHieght: minHeight
    })
  },

  // 动态设置页面信息
  setPageInfo() {
    let app = getApp()
    let downloadFileDomain = getApp().globalData.downloadDomain;
    let businessDomain =  getApp().globalData.businessDomain

    this.setData({
      downloadFileDomain,
      businessDomain
    })
  },

  // 获取视频详情
  async getVideoDetail(id) {
    const res = await app.$api.getVideoDetail({
      id: this.data.postId
    })

    // if (res.code === 'success') return
    // wx.showToast({
    //   title: res.message,
    //   icon: 'none',
    //   duration: 2000
    // })
    let brotherVideo = res.related_posts || []
    this.setData({
      brotherVideo
    })
  },

  // 跳转
  toDetail(e) {
    let { type, appid, url, path } = e.currentTarget.dataset

    if (type === 'apppage') { // 小程序页面         
      wx.navigateTo({
        url: path
      })
    }
    if (type === 'webpage') { // web-view页面
      url = '../webview/webview?url=' +  encodeURIComponent(url)
      wx.navigateTo({
        url
      })
    }
    if (type === 'miniapp') { // 其他小程序
      // #if MP
      wx.navigateToMiniProgram({
        appId: appid,
        path
      })
      // #elif NATIVE
      wx.showToast({
        title: '暂不支持，请在微信小程序中使用此功能！',
        icon: "none"
      })
      // #endif
    }
  },

  // 加载广告
  loadInterstitialAd(excitationAdId) {
    var self = this
    if (wx.createRewardedVideoAd) {
      rewardedVideoAd = wx.createRewardedVideoAd
      (
        {
         adUnitId: excitationAdId,
         multiton: true 
        }
      )
      rewardedVideoAd.onLoad(() => {
        // console.log('广告加载成功')
      })
      rewardedVideoAd.onError((err) => {
        Adapter.toast("广告加载错误,请刷新页面", 3000)
        
      })
      rewardedVideoAd.onClose((res) => {
        var id = this.data.detail.id
        if (res && res.isEnded) {
          var nowDate = new Date()
          nowDate = nowDate.getFullYear() + "-" + (nowDate.getMonth() + 1) + '-' + nowDate.getDate()
          var openAdLogs = wx.getStorageSync('openAdLogs') || []

          // 过滤重复值
          if (openAdLogs.length > 0) {
            openAdLogs = openAdLogs.filter(function (log) {
              return log['id'] !== id
            })
          }
 
          // 如果超过指定数量不再记录
          if (openAdLogs.length < 21) {
            var log = {
              "id": id,
              "date": nowDate
            }
            openAdLogs.unshift(log)
            wx.setStorageSync('openAdLogs', openAdLogs)
          }

          this.setData({
            detailSummaryHeight: ''
          })
          this.onPullDownRefresh();

          
        } else {
          Adapter.toast("你中途关闭了视频", 3000)
        }
      })
    }
  },

  // 广告报错
  adbinderror(e) {
    var self = this  
    if (e.detail.errCode) {
      self.setData({
        detailAdsuccess: false
        
      })
    }
  },
  adTopbinderror: function (e) {
    var self = this;
    if (e.detail.errCode) {
      self.setData({ detailTopAdsuccess: false })
    }
  },

  // 阅读更多
  readMore() {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    }
    var self = this
    // if (!self.data.userSession.sessionId) {
    //   self.setData({ isLoginPopup: true });
    //   return;
    // }
    var platform = self.data.platform
    if (platform == 'devtools') {
      Adapter.toast("开发工具无法显示激励视频", 2000)
      // self.setData({
      //   detailSummaryHeight: ''
      // })
    } else {
      rewardedVideoAd.show()
        .catch(() => {
          rewardedVideoAd.load()
            .then(() => rewardedVideoAd.show())
            .catch(err => {
              Adapter.toast("激励视频广告获取失败！", 2000)
              // self.setData({
              //   detailSummaryHeight: ''
              // })
            })
        })
    }
  },

  // 播放暂停
  playAudio() {
    var self = this;
    Adapter.playAudio(innerAudioContext, self)
  },

  //拖动音频进度条
  sliderChange(e) {
    var that = this
    innerAudioContext.src = this.data.detail.audios[0].src
    //获取进度条百分比
    var value = e.detail.value
    this.setData({ audioTime: value })
    var duration = this.data.audioDuration
    //根据进度条百分比及歌曲总时间，计算拖动位置的时间
    value = parseInt(value * duration / 100)
    //更改状态
    this.setData({ audioSeek: value, isPlayAudio: true })
    //调用seek方法跳转歌曲时间
    innerAudioContext.seek(value)
    //播放歌曲
    innerAudioContext.play()
  },

  // 刷新
  detailRefresh() {
    var self = this
    let args = {}
    args.id = this.data.postId
    args.postType = 'post'
    Auth.setUserMemberInfoData(this)
    args.userId = this.data.userSession.userId
    args.sessionId = this.data.userSession.sessionId
    args.time=true;
    this.setData({
      // detail: {},
      // relatedPostList: [],
      isPull: true,
      detailAdsuccess: true,
      detailTopAdsuccess: true
    })
    var excitationOpened=self.getExcitationOpened(this.data.postId);
    Adapter.loadArticleDetail(args, self, API, util, innerAudioContext,excitationOpened)
  },

  getExcitationOpened(id){
    var excitationOpened=0;
    let openAdLogs = wx.getStorageSync('openAdLogs') || []   
    if (openAdLogs.length) {   
      openAdLogs.forEach(item => {
        if (item.id == id) {
          excitationOpened=1;        
        }
      })
    }
    return excitationOpened
  },
  // 加载评论数据
  fristOpenComment() {
    let args = {}
    args.userId = this.data.userSession.userId
    args.sessionId = this.data.userSession.sessionId
    args.postId = this.data.postId
    args.limit = pageCount
    args.page = 1
    args.flag = 'postcomment'
    this.setData({    
      commentsList: []
    })

    Adapter.loadComments(args, this, API)
  },

  // 跳转文章详情
  redictDetail(e) {
    Adapter.redictDetail(e, 'post')
  },

  // 回复评论
  replay(e) {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    }
    var self = this
    if (self.data.detail.enableComment == '0') {
      return
    }
    var parentId = e.currentTarget.dataset.id
    var toUserName = e.currentTarget.dataset.name
    var toUserId = e.currentTarget.dataset.userid
 
    var commentdate = e.currentTarget.dataset.commentdate
    isFocusing = true
    self.showToolBar('replay')

    self.setData({
      parentId: parentId,
      placeholder: "回复" + toUserName + ":",
      focus: true,
      toUserId: toUserId,     
      commentdate: commentdate
    })
  },

  // 发送评论
  sendComment(e) {
    var self = this
    if (!self.data.userSession.sessionId) {
      self.setData({ isLoginPopup: true })
    } else {
      Adapter.sendComment(e.detail, self, app, API, util)
    }
  },
  // 评论成功
  commentSuccess() {
    this.selectComponent('#action-bar').closePopup()
  },
  // 提交评论
  formSubmitComment(e) {
    var self = this
    if (!self.data.userSession.sessionId) {
      self.setData({ isLoginPopup: true })
    } else {
      Adapter.submitComment(e, self, app, API, util)
    }
  },

  // 点赞
  postLike() {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    }
    var self = this
    var id = self.data.detail.id
    if (!self.data.userSession.sessionId) {
      self.setData({ isLoginPopup: true })
    } else {
      Adapter.postLike(id, self, app, API, "postDetail")
    }
  },

  // 用户授权
  agreeGetUser(e) {
    let self = this
    Auth.checkAgreeGetUser(e, app, self, API, '0')
  },

  // 弹出登录框
  openLoginPopup() {
    this.setData({
      isLoginPopup: true
    })
  },

  // 关闭登录框
  closeLoginPopup() {
    Auth.logout(this);
    this.setData({
      isLoginPopup: false
    })
  },

  // 输入框获得焦点
  onBindFocus(e) {
    var self = this
    isFocusing = false
      if (!self.data.focus) {
        self.setData({ focus: true })
      }  
  },

  // 输入框失去焦点
  onBindBlur(e) {
    var self = this
    if (!isFocusing) {
      const text = e.detail.value.trim()
      if (!text) {
        self.setData({
          parentID: '0',
          placeholder: '说点什么...',
          userid: '',
          toFromId: '',
          commentdate: ''
        })
      }
    }
  },

  //显示隐藏评论输入框
  showToolBar(e) {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    } 
    var self=this;
    var member = self.data.memberUserInfo.member;
    var _member=10;
    if(member !="00" && member !="01" ) {
      _member= parseInt(member)
    }
    var min_comment_user_member=self.data.detail.min_comment_user_member;
    var min_comment_user_memberName=self.data.detail.min_comment_user_memberName;
    if(member !="00" && member !="01"  && _member < min_comment_user_member) {
      if(e !="replay") {
        wx.z.showDialog({
          type: "confirm",
          title: "提示",        
          confirmText: "确认",
          // isCoverView: true,
          content: "权限不足,需"+min_comment_user_memberName+"及以上等级方可发表评论。是否去赚积分提高等级?",
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '../earnIntegral/earnIntegral'
              });
            }        
          }
        })
      }  
    } else {
      let userId = self.data.userSession.userId
      let sessionId = self.data.userSession.sessionId
      if (!userId || !sessionId) {
        self.setData({ isLoginPopup: true })
        return
      }

      // this.setData({
      //   toolbarShow: false,
      //   commentInputDialogShow: true,
      //   iconBarShow: false,
      //   menuBackgroup: !this.data.menuBackgroup,
      //   focus: true
      // })
      this.selectComponent('#action-bar').openComment()
    } 
  },

  //显示隐藏工具栏
  showIconBar() {
    this.setData({
      toolbarShow: false,
      iconBarShow: true,
      commentInputDialogShow: false,
      menuBackgroup: !this.data.menuBackgroup,
      focus: false
    })
  },

  //点击非评论区隐藏弹出栏
  hiddenBar() {
    this.setData({
      iconBarShow: false,
      toolbarShow: true,
      menuBackgroup: false,
      commentInputDialogShow: false,
      focus: false
    })
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '../index/index'
    })
  },

  // 文章刷新
  postRefresh() {
    this.onPullDownRefresh()
    this.hiddenBar()
    Adapter.toast("已刷新", 1500)
  },

  // 嵌入公众号
  insertWxPost() {
    this.getAppId()
    this.hiddenBar()
    this.getPostShortlink();
    this.setData({
      insertWxPopupShow: true
    })
  },
  // 复制小程序链接
  async copyAppLink() {    
    var args = {id: this.data.postId};
    const res = await app.$api.getPostShortlink(args) 
    let shortlink = res.shortlink || []    
    if(shortlink)
    {
      Adapter.copyLink(shortlink, "已复制")
    }
  },

  // 嵌入公众号
  insertChannelsPost(e) { 
    let userId = this.data.userSession.userId
      let sessionId = this.data.userSession.sessionId
      if (!userId || !sessionId) {
        this.setData({ isLoginPopup: true })
        return
      }
    var sourceurl=this.data.detail.link; 
    Adapter.insertChannelsPost(this,API,this.data.detail.id,"pages/detail/detail?id="+this.data.detail.id,"detail",sourceurl,userId,sessionId)
    
  },

  // 获取appID
  getAppId() {
    var self = this
    API.getSettings().then(res => {
      self.setData({
        appID: res.settings.appid
      })
    })
  },

  async getPostShortlink() {
    var args = {id: this.data.postId};
    const res = await app.$api.getPostShortlink(args) 
    let shortlink = res.shortlink || []
    this.setData({
      shortlink
    })
  },

  // 复制嵌入信息
  copyInsertInfo(e) {
    let data = e.currentTarget.dataset
    let id = data.id
    let path = data.path
    let shortlink=data.shortlink
    let info = `AppID：${id}，小程序路径：${path}，小程序短链接：${shortlink}`

    this.closeInsertWxPopup()
    Adapter.copyLink(info, "已复制")
  },


   // 复制嵌入视频号
   copyInsertChannels(e) {
    let data = e.currentTarget.dataset
    let url = data.url
    this.closeInsertChannelsPopup()
    Adapter.copyLink(url, "已复制")
  },

  // 关闭嵌入微信弹出
  closeInsertWxPopup() {
    this.setData({
      insertWxPopupShow: false
    })
  },

   // 关闭视频号弹出
   closeInsertChannelsPopup() {
    this.setData({
      insertChannelsPopupShow: false
    })
  },

  // 复制链接
  copyLink() {
    var url = this.data.detail.link
    this.hiddenBar()
    Adapter.copyLink(url, "已复制")
  },

  // 去到网页
  gotoWebpage() {
    var url = this.data.detail.link
    var enterpriseMinapp = this.data.detail.enterpriseMinapp
    this.hiddenBar()
    Adapter.gotoWebpage(enterpriseMinapp, url)
  },

  // 生成海拔
  onCreatePoster() {
    var self = this
    if (!self.data.userSession.sessionId) {
      self.setData({
        isLoginPopup: true
      })
    } else {
      Adapter.creatArticlePoster(self, app, API, util, self.modalView, 'post', Poster)
    }
  },

  // 海报生成成功
  onPosterSuccess(e) {
    const { detail } = e
    var invitecode= this.data.memberUserInfo.invitecode;

    var filedate=this.data.filedate;
    let data = {}
    if(invitecode || filedate)
    {
      data.invitecode=invitecode;
      data.filedate=filedate;  
      app.$api.deleteinviteqrcodeimg(data).then(response => {
        console.log(response);
      })
    }
    

    this.showModal(detail)
  },

  // 海报生成失败
  onPosterFail(err) {
    Adapter.toast(err, 2000)
  },

  // 创建海报
  creatPoster() {
    var self = this
    self.hiddenBar()
    Adapter.creatPoster(self, app, API, util, self.modalView, 'post')
  },

  // 保存海报
  showModal(posterPath) {
    this.modalView.showModal({
      title: '保存至相册可以分享给好友',
      confirmation: false,
      confirmationText: '',
      inputFields: [{
        fieldName: 'posterImage',
        fieldType: 'Image',
        fieldPlaceHolder: '',
        fieldDatasource: posterPath,
        isRequired: false
      }],
      confirm: function (res) {}
    })
  },

  // 支付
  payment() {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    }
    var self = this
    var enterpriseMinapp = this.data.detail.enterpriseMinapp

    if (enterpriseMinapp == "1") {
      if (!self.data.userSession.sessionId) {
        self.setData({ isLoginPopup: true })
      } else {
        var originalprice = self.data.detail.originalprice
        var postprice = self.data.detail.postprice
        var catYearPrice = self.data.detail.catyearprice
        var catYearIntegral = self.data.detail.catYearIntegral

        if (postprice != '' || catYearPrice != '' || catYearIntegral != '') {
          wx.navigateTo({
            url: '../payment/payment?postid=' + self.data.postId + "&categoryid=" + self.data.detail.categories[0] + "&posttitle=" + self.data.detail.title.rendered
          })
        } else if (originalprice != "" && catYearIntegral == '') {
          self.postIntegral()
        }
      }
    } else {
      Adapter.toast("个人主体小程序无法使用此功能", 2000)
    }
  },

  // 支付积分
  postIntegral() {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    }
    var self = this
    var userId = self.data.userSession.userId
    var sessionId = self.data.userSession.sessionId
    var postId = self.data.detail.id

    if (!sessionId) {
      self.setData({ isLoginPopup: true })
      return
    }
    var originalprice = parseInt(self.data.detail.originalprice);
    var userIntegral  = parseInt(self.data.memberUserInfo.integral);
    if(userIntegral<originalprice)
    {


      var _integral =originalprice-userIntegral;
      wx.z.showDialog({
        type: "confirm",
        title: "提示",  
        confirmText: "确认",
        // isCoverView: true,
        content: "积分不足,还需要"+_integral+"积分，是否去赚积分?",
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '../earnIntegral/earnIntegral'
            });
          }

          
        }
      }) 
      
      return;

    }
    var args = {}
    args.sessionid = sessionId
    args.extid = postId
    args.userid = userId
    args.integral = originalprice
    args.extype = "postIntegral"

    wx.z.showDialog({
      type: "confirm",
      title: "提示",    
      confirmText: "确认",
      // isCoverView: true,
      content: "将使用积分" + originalprice + ",确认使用？",
      success: (res) => {
        if (res.confirm) {
          API.postIntegral(args).then(res => {
            if (res.code == 'error') {
              wx.showToast({
                title: res.message,
                mask: false,
                icon: "none",
                duration: 3000
              });
            } else {
              wx.showToast({
                title: res.message,
                mask: false,
                icon: "none",
                duration: 3000
              });
              self.onPullDownRefresh();
            }
          })
        }
      }
    })
  },

  // 开通会员
  onBuyVip() {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    }
    var self = this
    var userId = self.data.userSession.userId
    var sessionId = self.data.userSession.sessionId
    var postId = self.data.detail.id

    if (!sessionId) {
      self.setData({ isLoginPopup: true })
      return
    }
    wx.navigateTo({
      url: '../buyvip/buyvip',
    })
  },

  
  postPraise() {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    }
    var self = this
    var system = self.data.system
    var enterpriseMinapp = this.data.detail.enterpriseMinapp
    var authorZanImage = self.data.detail.author_zan_image
    var praiseimgurl = self.data.detail.praiseimgurl
    if(praiseimgurl=='')
    {
      praiseimgurl = app.globalData.praiseImg;
    }
    if (authorZanImage) {
      wx.previewImage({
        urls: [authorZanImage]
      })
    } else {
      if (system == 'iOS') {
        if (praiseimgurl) {
          wx.previewImage({
            urls: [praiseimgurl]
          })
        } else if (!praiseimgurl && enterpriseMinapp == "1") {
          Adapter.toast("根据相关规定，该功能暂时只支持在安卓手机上使用", 1500)
        } else {
          Adapter.toast("设置错误", 1500)
        }
      } else {
        if (enterpriseMinapp == "1") {
          if (!self.data.userSession.sessionId) {
            self.setData({ isLoginPopup: true })
          } else {
            wx.navigateTo({
              url: '../postpraise/postpraise?postid=' + self.data.postId + "&touserid=" + self.data.userSession.userId + "&posttype=post"
            })
          }
        }
        else
        {
          if (enterpriseMinapp != "1" && praiseimgurl) {
            wx.previewImage({
              urls: [praiseimgurl]
            })
          } else {
            Adapter.toast("设置错误", 1500)
          }
        }
         
      }
    }

    self.hiddenBar()
  },
  //打开百度网盘小程序
  openbaidupan(e){
    let self = this
    let baiduPancode = e.currentTarget.dataset.baidupancode;
    if(baiduPancode)
    {
      let path='pages/netdisk_share/share?scene='+baiduPancode;
      // #if MP
      wx.navigateToMiniProgram({
        appId: 'wxdcd3d073e47d1742',
        path: path
      })
      // #elif NATIVE
      wx.showToast({
        title: '暂不支持，请在微信小程序中使用此功能！',
        icon: "none"
      })
      // #endif
    }

  },
  // a标签跳转和复制链接
  tagATap(e) {
    Adapter.tagATap(e,this,config.getDomain,API,util);    
  },

  // 打开文档
  async openLinkDoc(e) {
    let self = this
    let url
    let fileType

    let src = e.src || e.href
    let isPdf = /\.(pdf)$/.test(src)
    if(isPdf)
    {
        var href = '../webview/webview?url=' + encodeURIComponent(src);
        wx.navigateTo({
          url: href
        })
        return;
    }

    // 如果是a标签href中插入的文档
    let downloadFileDomain=self.data.downloadFileDomain;
    if(downloadFileDomain)
    {
      const res = await app.$api.getBaseConfig();
      let info = res.settings || {}
      downloadFileDomain=info.downloadfile_domain;
    }
    
    var n=0;
    for (var i = 0; i <downloadFileDomain .length; i++) {

      if (src.indexOf(downloadFileDomain[i]) != -1) {
        n++;
        break;
      }
    }

    if(n==0)
    {
      Adapter.copyLink(src, "复制成功")
      return;
    }

    let docType
    let isDoc = /\.(doc|docx|xls|xlsx|ppt|pptx|pdf)$/.test(src)

    if (src && isDoc){
      url = src
      fileType = /doc|docx|xls|xlsx|ppt|pptx|pdf$/.exec(src)[0]
    } else {
      url = e.filelink || e.href
      fileType = e.filetype
    }

    wx.downloadFile({
      url: url,
      success: function (res) {
        const filePath = res.tempFilePath
        wx.openDocument({
          showMenu:true,
          filePath: filePath,
          // fieldType: fileType
        })
      }
    })
  },

  // 删除评论
  deleteComment(e) {
    var self = this
    var id = e.currentTarget.dataset.id
    var data = {}
    var userId = self.data.userSession.userId
    var sessionId = self.data.userSession.sessionId
    var commentsList = self.data.commentsList

    if (!sessionId || !userId) {
      Adapter.toast('请先授权登录', 3000)
      return
    }
    data.id = id
    data.userid = userId
    data.sessionid = sessionId
    data.deletetype = 'publishStatus'
    wx.z.showDialog({
      type: "confirm",
      title: "提示",    
      confirmText: "确认",
      // isCoverView: true,
      content: "确认删除？",
      success: (res) => {
        if (res.confirm) {
          API.deleteCommentById(data).then(res => {
            if (res.code == 'error') {
              wx.showToast({
                title: res.message,
                mask: false,
                icon: "none",
                duration: 3000
              })
            } else {
              var hasChild = false
              commentsList.forEach(element => {
                if (element.id == id && element.child.length > 0) {
                  hasChild = true
                }
              })

              if (hasChild) {
                self.onPullDownRefresh()
              } else {
                commentsList = commentsList.filter(function (item) {
                  return item["id"] !== id
                })
                self.setData({
                  commentsList: commentsList
                })
              }

              var commentCounts = parseInt(self.data.commentCounts) - 1
              self.setData({
                commentCounts: commentCounts
              })

              wx.showToast({
                title: res.message,
                mask: false,
                icon: "none",
                duration: 3000
              })
            }
          })
        }
      }
    })
  },
  onHidePupopTap(e){
    this.setData({popupShow:false})
  },

  showPupop(e){
    var args= {};
    var self=this;
    if (!self.data.userSession.sessionId) {
      Auth.checkSession(app, API, self, 'isLoginNow', util);
      return;
      
    }
    args.cateType = 'subscribe';
    args.userId=self.data.userSession.userId;
    args.sessionId=self.data.userSession.sessionId;

    Adapter.loadCategories(args, self, API, true); 
  },
  
  confirm() {
    this.setData({
      'dialog.hidden': true,
      'dialog.title': '',
      'dialog.content': ''
    })
  },
  postsub(e) {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    }
    var self = this;
    if (!self.data.userSession.sessionId) {
      Auth.checkSession(app, API, self, 'isLoginNow', util);
      return;
      
    }
    else {
    var extid=e.currentTarget.dataset.id;
    var subscribetype = 'categorySubscribe';
    var subscribemessagesid = e.currentTarget.dataset.subid;
    Adapter.subscribeMessage(self, subscribetype, API, subscribemessagesid,extid, util);
    }
      
  },

  // 评论点赞
  postCommentLike(e) {
    if (this.data.isShareTimeline) {
      Adapter.toast("请前往小程序使用完整服务", 3000)
      return
    }
    let self = this

    if (!self.data.userSession.sessionId) {
      Auth.checkSession(app, API, self, 'isLoginNow', util)
      return;
    }

    let id = e.currentTarget.dataset.id
    let extype = 'comment'

    let args = {
      id: id,
      extype: extype,
      userid: self.data.userSession.userId,
      sessionid: self.data.userSession.sessionId
    }

    API.commentLike(args).then(res => {
      if (res.success) {
        let list = self.data.commentsList
        list = list.map(item => {
          let isCur = item.id === id

          if (isCur && item.likeon === '0') {
            item.likeon = '1'
            item.likecount++
          } else if (isCur && item.likeon === '1') {
            item.likeon = '0'
            item.likecount--
          }
          return item
        })

        self.setData({
          commentsList: list
        })
      } else {
        wx.showToast({
          title: res.message,
          mask: false,
          icon: "none",
          duration: 3000
        })
      }
    })
  },

  // 去用户主页
  goUserDetail(e) {
    let id = e.currentTarget.dataset.id
    let url = '../author/author?userid=' + id + '&postype=topic'
    wx.navigateTo({
      url: url
    })
  },
  wxParseToRedict(e){
      var appid=e.currentTarget.dataset.appid;
      var redirectype=e.currentTarget.dataset.redirectype;   
      var path=e.currentTarget.dataset.path;
      var url=e.currentTarget.dataset.url;

      if (redirectype == 'apppage') { //跳转到小程序内部页面         
        wx.navigateTo({
          url: path
        })
      } else if (redirectype == 'webpage') //跳转到web-view内嵌的页面
      {
        url = '../webview/webview?url=' +  encodeURIComponent(url);
        wx.navigateTo({
          url: url
        })
      }
      else if (redirectype == 'miniapp') //跳转其他小程序
      {
        // #if MP
        wx.navigateToMiniProgram({
          appId: appid,
          path: path
        })
        // #elif NATIVE
        wx.showToast({
          title: '暂不支持，请在微信小程序中使用此功能！',
          icon: "none"
        })
        // #endif
      }
  },
  // 预览图片
  previewImage(e) {
    var imgallsrc = e.currentTarget.dataset.imgallsrc
    var imgsrc = e.currentTarget.dataset.imgsrc
    wx.previewImage({
      current: imgsrc,
      urls: imgallsrc
    })
  },

  // 打开地图查看位置
  openmap(e) { 
    var latitude = Number(e.currentTarget.dataset.latitude)
    var longitude = Number(e.currentTarget.dataset.longitude)
    var address = e.currentTarget.dataset.address
    var name=e.currentTarget.dataset.title;
    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      scale: 15,
      name: name,
      address: address
    })
  },
  
  getPhoneNumber(e){
    Adapter.getPhoneNumber(e,this,API,Auth);
  },

  // 获取积分奖励
  async getReadPoints() {
    let userInfo = wx.getStorageSync('userSession')
    let params = {
      postid: this.data.postId,
      sessionid: userInfo.sessionId,
      userid: userInfo.userId
    }
    const res = await app.$api.getReadPoints(params)

    // if (res.code === 'success') return
    if(res.message !='')
    {
      wx.showToast({
      title: res.message,
      icon: 'none',
      duration: 2000
    })

    }
    
    this.setData({
      showPopPoints: false
    })
  },
  logoutTap(e){    
    Auth.logout(this);   
    this.onPullDownRefresh();
    this.closeLoginPopup();     
  },
  officialSucc: function (e) {
    // if(e.detail.status==0)
    // {
    //   this.setData({showAfficialAccount:false})
    // }
  },

  officialFail: function (e) {
    this.setData({ showAfficialAccount: false })
  },
  bindaderror:function(e)
  {
    console.log('bindaderror', e)

  },

  bindadload:function(e)
  {

    console.log('bindadload', e)
  },
  bindadplay:function(e)
  {
    console.log('bindadplay', e)
  },

  // 切换显示隐藏视频描述
  onShowVideoDes() {
    let isShow = this.data.showVideoDes
    this.setData({
      showVideoDes: !isShow
    })
  },
  redirectDetail(e) {   
    let { id } = e.currentTarget
    let type = e.currentTarget.dataset.posttype || 'post'
    let format = e.currentTarget.dataset.format || ''
    let url = ''
    let channelsFeedId = e.currentTarget.dataset.channelsfeedid || ''
    let channelsId = e.currentTarget.dataset.channelsid || ''
    let mpPostLink = e.currentTarget.dataset.mppostlink || ''
    // if (channelsFeedId) {
    //   if ((type == "post" && format == 'video')) {

    //     wx.openChannelsActivity({
    //       finderUserName: channelsId,
    //       feedId: channelsFeedId,
    //       success(res) {
    //         let params = {
    //           id: id
    //         }
    //         app.$api.updatePageviews(params).then(res => {
    //           console.log(res);
    //         })
    //       }
    //     })

    //     return;
    //   }

    // }
    if (mpPostLink) {
      if ((type == "post" && format == 'link') || type == "topic") {
        let url = "/pages/webview/webview?url=" + encodeURIComponent(mpPostLink);
        wx.navigateTo({
          url
        })

        return;
      }
    }
    url = `../detail/detail?format=${format}&id=${id}`;
    wx.navigateTo({
      url
    })
  },

  // dount分享
  dountShare() {
    const data = this.data
    console.log(app)
    console.log(data.detail)
    console.log(data.detail.link)
    wx.miniapp.shareMiniProgramMessage({
      userName: app.globalData.gh_id,
      path: 'pages/detail/detail?id=' + data.postId,
      imagePath: '/images/uploads/logo.png',
      // webpageUrl: data.detail.link,
      webpageUrl: 'https://m.wolinwarm.com',
      title: data.detail.title.rendered,
      description: data.detail.category_name,
      withShareTicket: false,
      miniprogramType: 0,
      scene: 0,
      success(res) {
        // console.log('分享小程序成功:' + res)
      }
    })
  },

  handleComplain() {
    this.hiddenBar()
    wx.showToast({
      title: '感谢您的举报，我们将立即对内容进行核实删除',
      icon: 'none'
    })
  }
})

