# 互助消息通知配置说明

## 功能概述

已为互助流程的关键节点添加了微信小程序订阅消息通知功能：

1. **用户已接单** - 有人响应了您的互助需求
2. **发布者已选择** - 您的响应已被选中
3. **发布者已完结** - 互助已完成，请进行评价
4. **发布者已评分** - 您收到了新的评价

## 配置步骤

### 1. 在微信小程序后台配置订阅消息模板

登录微信小程序后台 → 功能 → 订阅消息 → 公共模板库，搜索并添加以下模板：

#### 模板1：互助响应通知
- **模板用途**：用户响应互助需求时通知发布者
- **推荐搜索关键词**：服务预约、响应通知、接单通知
- **字段配置**：
  - thing1：互助标题
  - thing2：响应者姓名
  - time3：响应时间
  - thing4：备注信息

#### 模板2：响应选中通知
- **模板用途**：发布者选择帮助者时通知被选中用户
- **推荐搜索关键词**：状态更新、选中通知、服务确认
- **字段配置**：
  - thing1：互助标题
  - thing2：状态（您的响应已被选中）
  - time3：选择时间
  - thing4：备注信息

#### 模板3：互助完成通知
- **模板用途**：互助完成时通知帮助者
- **推荐搜索关键词**：服务完成、任务完成、完成通知
- **字段配置**：
  - thing1：互助标题
  - thing2：状态（互助已完成）
  - time3：完成时间
  - thing4：备注信息

#### 模板4：评价通知
- **模板用途**：收到评价时通知被评价者
- **推荐搜索关键词**：评价通知、评分提醒、反馈通知
- **字段配置**：
  - thing1：互助标题
  - thing2：评分（X星评价）
  - time3：评价时间
  - thing4：评价内容

**配置步骤**：
1. 在小程序后台搜索合适的模板
2. 选择模板并配置字段名称
3. 提交审核（通常很快通过）
4. 获取模板ID用于后端配置

### 2. 在WordPress后台配置模板ID

从小程序后台获取模板ID后，在WordPress后台添加以下选项：

**方法1：通过数据库直接添加**
```sql
-- 在wp_options表中添加以下配置
INSERT INTO wp_options (option_name, option_value) VALUES
('raw_help_response_template_id', '您从小程序后台获取的响应通知模板ID'),
('raw_help_selected_template_id', '您从小程序后台获取的选中通知模板ID'),
('raw_help_completed_template_id', '您从小程序后台获取的完成通知模板ID'),
('raw_help_rated_template_id', '您从小程序后台获取的评价通知模板ID');
```

**方法2：通过WordPress后台设置页面**
如果插件有设置页面，可以在设置页面中添加这些配置项。

### 3. 用户订阅设置

用户需要在小程序中订阅互助通知才能收到消息。可以在以下位置添加订阅入口：

1. **互助详情页** - 发布需求后提示订阅
2. **个人设置页** - 提供订阅管理功能
3. **首次使用时** - 引导用户订阅

订阅代码示例：
```javascript
// 在小程序中调用订阅
wx.requestSubscribeMessage({
  tmplIds: [
    '响应通知模板ID',
    '选中通知模板ID', 
    '完成通知模板ID',
    '评价通知模板ID'
  ],
  success: (res) => {
    // 调用后端API保存订阅状态
    api.subscribeHelpNotifications({
      userid: userId,
      sessionid: sessionId,
      subscribetype: 'help_notifications',
      extid: 0
    });
  }
});
```

## 数据库结构

订阅信息存储在 `wp_minapper_ext` 表中：

```sql
-- 订阅记录示例
INSERT INTO wp_minapper_ext (userid, extid, extype, extkey, extvalue) VALUES
(用户ID, 0, 'subscribeMessage', 'help_notifications', '订阅次数');
```

## 测试方法

1. **创建测试数据**：发布一个互助需求
2. **响应测试**：用另一个用户响应该需求
3. **选择测试**：发布者选择帮助者
4. **完成测试**：确认互助完成
5. **评价测试**：提交评价

每个步骤都应该触发相应的订阅消息通知。

## 注意事项

1. **模板ID配置**：必须在WordPress后台正确配置模板ID
2. **用户订阅**：用户必须先订阅才能收到通知
3. **订阅次数**：每次发送消息会消耗一次订阅次数
4. **消息限制**：遵循微信小程序订阅消息的使用规范
5. **错误处理**：即使消息发送失败，业务流程仍会正常进行

## 故障排除

### 消息发送失败
1. 检查模板ID是否正确配置
2. 确认用户是否已订阅
3. 查看错误日志：`error_log('互助通知发送结果: ...')`

### 用户收不到消息
1. 确认用户已授权订阅消息
2. 检查订阅次数是否已用完
3. 验证模板字段配置是否正确

### 调试方法
```php
// 在代码中添加调试日志
error_log('发送通知: ' . $notification_type . ' 给用户: ' . $target_user_id);
error_log('模板ID: ' . $template_id);
error_log('消息数据: ' . json_encode($message_data));
```

## 扩展功能

可以根据需要添加更多通知节点：
- 需求即将过期提醒
- 长时间未响应提醒
- 积分到账通知
- 系统维护通知

只需要在相应的业务逻辑中调用 `send_help_notification()` 方法即可。
