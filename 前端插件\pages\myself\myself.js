/*
 * 
 * 微慕小程序
 * author: jianbo
 * organization:  微慕 www.minapper.com 
 * 技术支持微信号：Jianbo
 * Copyright (c) 2018 https://www.minapper.com All rights reserved.
 */
import config from '../../utils/config.js'
const API = require('../../utils/api.js')
const Auth = require('../../utils/auth.js')
const Adapter = require('../../utils/adapter.js')
const util = require('../../utils/util.js')
const app = getApp()
const appName = app.globalData.appName
const domain = config.getDomain

const options = {
  data: {
    shareTitle: appName + '关于我',
    pageTitle: ' ',
    userInfo: {},
    userSession: {},
    wxLoginInfo: {},
    memberUserInfo: {},
    isLoginPopup: false,
    system: '',
    enterpriseMinapp: '',
    raw_praise_word: '鼓励',
    postMessageId: '',
    scopeSubscribeMessage: '',
    newcontentSubscribeCount: 0,
    pageName: 'myself',
    zan_display: '0',
    pendingCount: {},
    messagesCount: '',
    tabBarRedDotIndex: config.getTabBarRedDotIndex,
    redHotCount: 0,
    showRedeemPop: false, // 积分兑换弹窗
    enable_redpack: '0',
    banner: {},
    minapperSource: config.minapperSource,
    hasWechatInstall: app.globalData.hasWechatInstall,
    enableWechatshop: config.enableWechatshop,
    enableMinishop: config.enableMinishop,
    enableWeixinOpen: config.enableWeixinOpen
  },

  onLoad: function (option) {
    this.setData({
      system: /ios/i.test(app.globalData.deviceInfo.system) ? 'iOS' : 'Android'
    })
    this.getSettings()
    Adapter.getCustomBanner(this, API, 'myself_top_nav')
    this.getEnableExtLinkConfig()
  },

  onShow: function () {
    Auth.checkSession(app, API, this, 'isLoginLater', util)
    wx.setStorageSync('openLinkCount', 0)
    let enable_redpack = getApp().globalData.minapper_enable_redpack
    this.setData({ enable_redpack: enable_redpack })
    if (this.data.userSession.sessionId) {
      this.setData({
        isLoginPopup: false
      })
      this.onPullDownRefresh()
      this.getMessageCount()
    }
  },

  onHide: function () {
    // 兑换积分弹窗未主动关闭时
    this.data.showRedeemPop && this.onCloseRedeemPop()
  },

  onReady: function () {
    wx.setNavigationBarTitle({
      title: this.data.pageTitle
    })
  },

  onPullDownRefresh: function () {
    this.setData({
      isPull: true
    })

    if (this.data.userSession.sessionId) {
      Auth.checkGetMemberUserInfo(this.data.userSession, this, API) 
    }
    wx.stopPullDownRefresh()
  },

  onShareAppMessage: function () {
    return {
      title: this.data.shareTitle,
      path: '/pages/myself/myself',
      //imageUrl: this.data.detail.content_first_image,
    }
  },

  getSettings() {
    API.getSettings().then(res => {
      const enterpriseMinapp = res.settings.enterpriseMinapp ? res.settings.enterpriseMinapp : ''
      const postMessageId = res.settings.postMessageId ? res.settings.postMessageId : ''
      const raw_praise_word = res.settings.raw_praise_word ? res.settings.raw_praise_word : ''
      const zan_display = res.settings.zan_display
      this.setData({
        enterpriseMinapp,
        raw_praise_word,
        postMessageId,
        zan_display
      })
    })
  },

  agreeGetUser(e) {
    Auth.checkAgreeGetUser(e, app, this, API, '0')
  },

  redictSetting() {
    wx.navigateTo({
      url: '../settings/settings'
    })
  },

  // 去积分等级说明页
  toIntegralDes() {
    if (!this.data.userSession.sessionId) {
      this.setData({
        isLoginPopup: true
      })
    } else {
      wx.navigateTo({
        url: `../myinfo/myinfo?integral=${this.data.memberUserInfo.integral}`
      })
    }
  },

  redictPage(e) {
    const mytype = e.currentTarget.dataset.mytype
    if (mytype === 'logout') {
      Auth.logout(this)
      wx.reLaunch({
        url: '../myself/myself'
      })
      return
    } else if (mytype === 'about') {
      wx.navigateTo({
        url: '../about/about'
      })
      return
    } else if (mytype === 'privacy') {
      const url = getApp().globalData.privacyPolicyLink
      wx.navigateTo({
        url: '/pages/webview/webview?url=' + encodeURIComponent(url)
      })
      return
    }

    // 校验登录
    if (!this.data.userSession.sessionId) {
      this.setData({
        isLoginPopup: true
      })
      return
    }
    
    // 需要登录的页面
    if (mytype === 'myorders' || mytype === 'myredpack' || mytype === 'myminapperorders') {
      wx.navigateTo({
        url: '../myorder/myorder?type=' + mytype
      })
    } else if (mytype === 'myaddress') {
      wx.navigateTo({
        url: '/subpages/address/list?pick=false&userid=' + this.data.userSession.userId
      })
    } else if (mytype === 'mypublish') {
      wx.navigateTo({
        url: '../mypublish/mypublish?userid=' + this.data.userSession.userId + '&postype=topic'
      })
    } else if (mytype === 'myposts') {
      wx.navigateTo({
        url: '../mypublish/mypublish?userid=' + this.data.userSession.userId + '&postype=post'
      })
    } else if (mytype === 'myMessages') {
      wx.navigateTo({
        url: '../mymessage/mymessage'
      })
    } else if (mytype === 'mynotice') {
      wx.navigateTo({
        url: '../notice/notice'
      })
    } else if (mytype === 'myID') {
      wx.navigateTo({
        url: './profile/profile'
      })
    } else if (mytype === 'myshoporders') {
      wx.navigateTo({
        url: 'plugin-private://wx34345ae5855f892d/pages/orderList/orderList?tabId=pendingPay'
      })
    } else if (mytype === 'myshoppingcart') {
      wx.navigateTo({
        url: 'plugin-private://wx34345ae5855f892d/pages/shoppingCart/shoppingCart'
      })
    } else if (mytype === 'orderManage') {
      wx.navigateTo({
        url: '../minishop/order/order'
      })
    } else if (mytype === 'wechatOrder') {
      wx.navigateTo({
        url: '../shop/order/order?ordertype=all'
      })
    } else if (mytype === 'mywechatOrder') {
      wx.navigateTo({
        url: '../shop/order/order?ordertype=my&openid=' + this.data.memberUserInfo.openid
      })
    } else if (mytype === 'updateUserInfo') {
      Auth.checkGetMemberUserInfo(this.data.userSession, this, API)
      wx.showToast({
        title: '更新完成',
        icon: 'none',
        duration: 3000
      })
    } else if (mytype === 'mytopics') {
      wx.navigateTo({
        url: '../mypublish/mypublish?userid=' + this.data.userSession.userId + '&postype=topic'
      })
    } else if (mytype === 'myhelp') {
      wx.navigateTo({
        url: '../my-help/my-help?userid=' + this.data.userSession.userId
      })
    } else if (mytype === 'myzanimage') {
      wx.navigateTo({
        url: '../authorcode/authorcode'
      })
    } else if (mytype === 'topicspending') {
      wx.navigateTo({
        url: '/subpages/postpending/postpending?posttype=topic'
      })
    } else if (mytype === 'postspending') {
      wx.navigateTo({
        url: '/subpages/postpending/postpending?posttype=post'
      })
    } else if (mytype === 'decpending') {
      wx.navigateTo({
        url: '../userDesPending/userDesPending'
      })
    } else if (mytype === 'commentspending') {
      wx.navigateTo({
        url: '/subpages/commentsPending/commentsPending?posttype=post'
      })
    } else if (mytype === 'replyspending') {
      wx.navigateTo({
        url: '/subpages/commentsPending/commentsPending?posttype=topic'
      })
    } else if (mytype === 'myIntegral') {
      wx.navigateTo({
        url: '../myIntegral/myIntegral'
      })
    } else if (mytype === 'followmeAuthor') {
      wx.navigateTo({
        url: '../userlist/userlist?authorType=followme'
      })
    } else if (mytype === 'myFollowAuthor') {
      wx.navigateTo({
        url: '../userlist/userlist?authorType=myFollow'
      })
    } else if (mytype === 'mysignin') {
      wx.navigateTo({
        url: '../earnIntegral/earnIntegral'
      })
    } else if (mytype === 'adminCenter') {
      wx.navigateTo({
        url: '/subpages/admincenter/admincenter'
      })
    } else if (mytype === 'publishMinApp') {
      wx.navigateToMiniProgram({
        appId: 'wxcff7381e631cf54e'
      })
    } else if (mytype === 'kefuMinApp') {
      wx.navigateToMiniProgram({
        appId: 'wx277c9f1d194fce2f'
      })
    } else if (mytype === 'shophelper') {
      wx.navigateToMiniProgram({
        appId: 'wx4aedf8c9edf9fd72'
      })
    } else if (mytype === 'shujuMinApp') {
      wx.navigateToMiniProgram({
        appId: 'wx95e493619d0ff929'
      })
    } else if (mytype === 'mycoupon') {
      wx.navigateTo({
        url: '../shop/my-coupon/my-coupon'
      })
    } else if (mytype === 'myshare') {
      wx.navigateTo({
        url: '/pages/userlist/userlist?from=myshare'
      })
    } else if (mytype === 'mycardcode') {
      wx.navigateTo({
        url: '/pages/mycardcode/mycardcode?from=myshare'
      })
    } else if (mytype === 'myinvite') {
      wx.navigateTo({
        url: '/pages/userlist/userlist?from=myinvite'
      })
    } else if (mytype === 'scanqrcode') {
      this.scanQRCode(e)
    } else if (mytype === 'sendredpack') {
      const is_mpauth = this.data.memberUserInfo.is_mpauth
      app.$api.getredpackcashstep({
        userid: this.data.userSession.userId,
        sessionid: this.data.userSession.sessionId
      }).then(res => {
        if (!res.redcashs) {
          return
        }

        if (res.exchange_redpack === 'zanshangcode' && !res.zanImageUrl) {
          wx.z.showDialog({
            content: '没有上传赞赏码，去上传?',
            success: (res) => {
              if (res.confirm) { // 确定
                wx.navigateTo({
                  url: '../authorcode/authorcode'
                })
              }
            }
          })
        } else if (res.exchange_redpack === 'redpack' && !is_mpauth) {
          const authUrl = 'https://' + domain + '/mp-api/' + this.data.memberUserInfo.userid
          wx.showModal({
            title: '提示',
            content: '当前操作需要进行公众号授权登录',
            confirmText: '立即授权',
            success: res => {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/webview/webview?url=' + encodeURIComponent(authUrl)
                })
              }
            }
          })
        } else {
          this.setData({
            showRedeemPop: true,
            redpackCashStep: res.redcashs
          })
        }
      })
    } else if (mytype === 'buyvip') {
      let endDate = e.currentTarget.dataset.date // 有到期时间为：续费
      let url = endDate ? `../buyvip/buyvip?date=${endDate}` : '../buyvip/buyvip'
      wx.navigateTo({
        url
      })
    } else {
      wx.navigateTo({
        url: '../readlog/readlog?mytype=' + mytype
      })
    }
  },

  // 去个人资料页
  goProfile() {
    wx.navigateTo({
      url: '/pages/myself/profile/profile'
    })
  },

  closeLoginPopup() {
    Auth.logout(this)
    this.setData({
      isLoginPopup: false
    })
  },
  openLoginPopup() {
    this.setData({
      isLoginPopup: true
    })
  },

  openSettting() {
    wx.openSetting({
      success(res) { }
    })
  },

  subscribeMessage(e) {
    const subscribetype = e.currentTarget.dataset.subscribetype
    const subscribemessagesid = e.currentTarget.dataset.subscribemessagesid
    Adapter.subscribeMessage(this, subscribetype, API, subscribemessagesid, 0)
  },

  getMessageCount() {
    const self = this
    API.getMessageCount({
      userId: self.data.userSession.userId,
      sessionId: self.data.userSession.sessionId,
      messagetype: 'all'
    }).then(res => {
      if (res.success) {
        const messagesCount = res.messagesCount[0].count
        let count = parseInt(messagesCount)
        let redHotCount = 0
        redHotCount = redHotCount + count
        self.setData({
          messagesCount,
          redHotCount
        })
      }
    }).then(res => {
      if (self.data.memberUserInfo.member == '00') {
        self.getPendingCount()
      } else {
        const redHotCount = self.data.redHotCount
        //设置未读消息提示
        if (redHotCount > 0) {
          wx.showTabBarRedDot({
            index: self.data.tabBarRedDotIndex,
            fail(e) {
              console.log(e)
            },
            success() {}
          })
        } else {
          wx.hideTabBarRedDot({
            index: self.data.tabBarRedDotIndex,
            fail(e) {
              console.log(e)
            },
            success() {}
          })
        }
      }
    })
  },

  getPendingCount(e) {
    const self = this
    API.getPendingCount({
      sessionId: self.data.userSession.sessionId,
      userId: self.data.userSession.userId
    }).then(res => {
      if (res.success) {
        const pendingCount = res.pendingcount
        const topic_pending_count = parseInt(pendingCount.topic_pending_count)
        const reply_pending_count = parseInt(pendingCount.reply_pending_count)
        const post_pending_count = parseInt(pendingCount.post_pending_count)
        const comment_pending_count = parseInt(pendingCount.comment_pending_count)
        const count = topic_pending_count + reply_pending_count + post_pending_count + comment_pending_count
        const redHotCount = self.data.redHotCount + count
        self.setData({
          pendingCount,
          redHotCount
        })
      }
    }).then(res => {
      const redHotCount = self.data.redHotCount
      //设置未读消息提示
      if (redHotCount > 0) {
        wx.showTabBarRedDot({
          index: self.data.tabBarRedDotIndex,
          fail(e) {
            // console.log(e)
          },
          success() {}
        })
      } else {
        wx.hideTabBarRedDot({
          index: self.data.tabBarRedDotIndex,
          fail(e) {
            // console.log(e)
          },
          success() {}
        })
      }
    })
  },

  logoutTap(e) {
    const pageName = this.data.pageName
    this.closeLoginPopup()
    Auth.logout(this)
    wx.reLaunch({
      url: '../' + pageName + '/' + pageName
    })
  },

  // 关闭兑换积分弹窗
  onCloseRedeemPop() {
    this.setData({
      showRedeemPop: false
    })
  },

  handleContact(e) {
    // console.log(e.detail.path)
    // console.log(e.detail.query)
  },

  async getEnableExtLinkConfig() {
    const contact = wx.getStorageSync('contactcard')
    if (!contact) {
      const res = await app.$api.getEnableExtLinkConfig()
      contact = res.contact
    }              
    this.setData({      
      send_message_title: contact.send_message_title,
      send_message_path: contact.send_message_path,
      send_message_img: contact.send_message_img,
    })
  },

  // 注销账号：小米应用商店需提供账号注销功能
  deleteAccount() {
    const content = '注销账号是不可恢复的操作，申请注销后账号所有相关的数据将被删除，请确认是否要注销？'
    wx.showModal({
      title: '注销账号',
      content,
      confirmText: '确认',
      success (res) {
        if (res.confirm) {
          wx.navigateTo({
            url: '/subpages/account-deletion/account-deletion'
          })
        }
      }
    })
  },
}

Page(options)