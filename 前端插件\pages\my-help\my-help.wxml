<!--pages/my-help/my-help.wxml-->
<view class="container">
  <!-- 标签页切换 -->
  <van-tabs active="{{ activeTab }}" bind:change="onTabChange" color="#FF6B35" title-active-color="#FF6B35">
    <van-tab title="我发布的" name="published">
      <!-- 筛选器 -->
      <view class="filter-wrapper" wx:if="{{ activeTab === 'published' }}">
        <van-dropdown-menu active-color="#FF6B35">
          <van-dropdown-item
            value="{{ statusFilter }}"
            options="{{ statusOptions }}"
            bind:change="onStatusChange"
          />
          <van-dropdown-item
            value="{{ typeFilter }}"
            options="{{ typeOptions }}"
            bind:change="onTypeChange"
          />
        </van-dropdown-menu>
      </view>

      <!-- 我发布的互助列表 -->
      <view class="help-list">
        <view wx:for="{{ publishedList }}" wx:key="id" class="help-item" bind:tap="goToDetail" data-id="{{ item.id }}">
          <view class="help-header">
            <view class="urgency-tag urgency-{{ item.urgency }}">
              {{ item.urgency === 'urgent' ? '紧急' : item.urgency === 'normal' ? '一般' : '不急' }}
            </view>
            <view class="help-type">{{ item.help_type }}</view>
            <view class="points-reward" wx:if="{{ item.points_reward > 0 }}">
              <van-icon name="gold-coin-o" />
              {{ item.points_reward }}积分
            </view>
          </view>
          
          <view class="help-title">{{ item.post_title }}</view>
          <view class="help-content">{{ item.post_content }}</view>
          
          <view class="help-footer">
            <view class="time-info">
              {{ item.created_at }}
            </view>
            <view class="response-count" wx:if="{{ item.response_count > 0 }}">
              <van-icon name="chat-o" />
              {{ item.response_count }}个响应
            </view>
          </view>
          
          <view class="help-status status-{{ item.status }}">
            {{ item.status === 'pending' ? '待响应' : item.status === 'in_progress' ? '进行中' : item.status === 'completed' ? '已完成' : '已取消' }}
          </view>
        </view>
      </view>

      <!-- 空状态 - 我发布的 -->
      <van-empty wx:if="{{ !publishedList.length && !loading && activeTab === 'published' }}" 
                 description="您还没有发布过互助需求" 
                 image="search" />
    </van-tab>

    <van-tab title="我接单的" name="accepted">
      <!-- 筛选器 -->
      <view class="filter-wrapper" wx:if="{{ activeTab === 'accepted' }}">
        <van-dropdown-menu active-color="#FF6B35">
          <van-dropdown-item
            value="{{ responseStatusFilter }}"
            options="{{ responseStatusOptions }}"
            bind:change="onResponseStatusChange"
          />
          <van-dropdown-item
            value="{{ typeFilter }}"
            options="{{ typeOptions }}"
            bind:change="onTypeChange"
          />
        </van-dropdown-menu>
      </view>

      <!-- 我接单的互助列表 -->
      <view class="help-list">
        <view wx:for="{{ acceptedList }}" wx:key="id" class="help-item" bind:tap="goToDetail" data-id="{{ item.request_id }}">
          <view class="help-header">
            <view class="urgency-tag urgency-{{ item.urgency }}">
              {{ item.urgency === 'urgent' ? '紧急' : item.urgency === 'normal' ? '一般' : '不急' }}
            </view>
            <view class="help-type">{{ item.help_type }}</view>
            <view class="points-reward" wx:if="{{ item.points_reward > 0 }}">
              <van-icon name="gold-coin-o" />
              {{ item.points_reward }}积分
            </view>
          </view>
          
          <view class="help-title">{{ item.post_title }}</view>
          <view class="help-content">{{ item.post_content }}</view>
          
          <view class="help-footer">
            <view class="user-info">
              <van-icon name="user-o" />
              {{ item.requester_name }}
            </view>
            <view class="time-info">
              {{ item.created_at }}
            </view>
          </view>
          
          <view class="response-status status-{{ item.response_status }}">
            {{ item.response_status === 'pending' ? '待选择' : item.response_status === 'selected' ? '已选中' : item.response_status === 'rejected' ? '未选中' : '已完成' }}
          </view>
        </view>
      </view>

      <!-- 空状态 - 我接单的 -->
      <van-empty wx:if="{{ !acceptedList.length && !loading && activeTab === 'accepted' }}" 
                 description="您还没有响应过互助需求" 
                 image="search" />
    </van-tab>
  </van-tabs>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{ hasMore }}">
    <van-button 
      loading="{{ loading }}" 
      bind:click="loadMore" 
      type="default" 
      size="small"
      block
    >
      {{ loading ? '加载中...' : '加载更多' }}
    </van-button>
  </view>

  <!-- 发布按钮 -->
  <view class="publish-btn">
    <van-button 
      type="primary" 
      round 
      icon="plus" 
      bind:click="goToPublish"
      color="#FF6B35"
    >
      发布互助
    </van-button>
  </view>
</view>
