const app = getApp()

Page({
  data: {
    code: '',
    countdown: 0,
    isExpired: false,
    timer: null,
    codes: [],
  },
  onLoad() {
    this.fetchCode();
  },
  updateCodes() {
    this.setData({
      codes: this.data.code.split('')
    });
  },
  fetchCode() {
    // 用微信小程序的网络请求替换原有的api.getVerificationCode()
    wx.request({
      url: 'https://m.wolinwarm.com/wp-json/uniapp-builder/v1/getcode', // 你的API地址
      method: 'GET',
      success: (res) => {
        this.setData({
          code: res.data.code,

          countdown: res.data.expires_in
        });
        this.updateCodes();
        this.startCountdown();
      },
      fail: (error) => {
        console.error('Failed to fetch code:', error);
      }
    });
  },
  startCountdown() {
    this.setData({
      isExpired: false
    });
    this.data.timer = setInterval(() => {
      let newCountdown = this.data.countdown - 1;
      this.setData({
        countdown: newCountdown
      });
      if (newCountdown <= 0) {
        clearInterval(this.data.timer);
        this.setData({
          code: '000000',
          isExpired: true
        });
        this.updateCodes();  // 在这里添加更新代码
      }
    }, 1000);
  },
  refreshCode() {
    clearInterval(this.data.timer);
    this.fetchCode();
  }
})