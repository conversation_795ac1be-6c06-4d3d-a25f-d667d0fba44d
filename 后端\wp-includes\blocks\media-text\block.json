{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/media-text", "title": "Media & Text", "category": "media", "description": "Set media and words side-by-side for a richer layout.", "keywords": ["image", "video"], "textdomain": "default", "attributes": {"align": {"type": "string", "default": "none"}, "mediaAlt": {"type": "string", "source": "attribute", "selector": "figure img", "attribute": "alt", "default": "", "role": "content"}, "mediaPosition": {"type": "string", "default": "left"}, "mediaId": {"type": "number", "role": "content"}, "mediaUrl": {"type": "string", "source": "attribute", "selector": "figure video,figure img", "attribute": "src", "role": "content"}, "mediaLink": {"type": "string"}, "linkDestination": {"type": "string"}, "linkTarget": {"type": "string", "source": "attribute", "selector": "figure a", "attribute": "target"}, "href": {"type": "string", "source": "attribute", "selector": "figure a", "attribute": "href", "role": "content"}, "rel": {"type": "string", "source": "attribute", "selector": "figure a", "attribute": "rel"}, "linkClass": {"type": "string", "source": "attribute", "selector": "figure a", "attribute": "class"}, "mediaType": {"type": "string", "role": "content"}, "mediaWidth": {"type": "number", "default": 50}, "mediaSizeSlug": {"type": "string"}, "isStackedOnMobile": {"type": "boolean", "default": true}, "verticalAlignment": {"type": "string"}, "imageFill": {"type": "boolean"}, "focalPoint": {"type": "object"}, "allowedBlocks": {"type": "array"}, "useFeaturedImage": {"type": "boolean", "default": false}}, "usesContext": ["postId", "postType"], "supports": {"anchor": true, "align": ["wide", "full"], "html": false, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": true, "radius": true, "style": true, "width": true}}, "color": {"gradients": true, "heading": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}}, "editorStyle": "wp-block-media-text-editor", "style": "wp-block-media-text"}