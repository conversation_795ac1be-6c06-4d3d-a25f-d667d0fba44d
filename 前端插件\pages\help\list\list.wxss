/* pages/help/list/list.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索容器 */
.search-container {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(255, 107, 53, 0.1);
  border: 1rpx solid rgba(255, 107, 53, 0.1);
}

/* 搜索框包装器 */
.search-wrapper {
  margin-bottom: 24rpx;
}

/* 搜索输入框容器 */
.search-input-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 28rpx;
  padding: 8rpx 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.search-input-box:focus-within {
  border-color: #FF6B35;
  background: #fff;
  box-shadow: 0 0 0 4rpx rgba(255, 107, 53, 0.1);
}

/* 搜索输入框 */
.search-input {
  flex: 1;
  height: 64rpx;
  line-height: 64rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  padding: 0 16rpx;
}

.search-input::placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 搜索按钮 */
.search-btn {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #FF6B35 0%, #FFB74D 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
}

.search-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.4);
}

.search-btn .iconfont {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

/* 筛选器包装器 */
.filter-wrapper {
  /* Vant dropdown menu 会自动处理样式 */
}

.help-list {
  /* 列表容器 */
}

.help-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  position: relative;
}

.help-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
  gap: 12rpx;
}

.urgency-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.urgency-urgent {
  background: linear-gradient(135deg, #ff4757, #ff3838);
}

.urgency-normal {
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
}

.urgency-low {
  background: linear-gradient(135deg, #70a1ff, #5352ed);
}

.help-type {
  padding: 8rpx 16rpx;
  background: #f1f2f6;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #2f3542;
}

.points-reward {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #ffa502, #ff6348);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.help-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2f3542;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.help-content {
  font-size: 28rpx;
  color: #57606f;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.help-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16rpx;
  font-size: 24rpx;
  color: #747d8c;
}

.user-info, .location-info, .time-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.help-status {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.status-pending {
  background: #dfe4ea;
  color: #57606f;
}

.status-in_progress {
  background: #ffeaa7;
  color: #fdcb6e;
}

.status-completed {
  background: #55efc4;
  color: #00b894;
}

.load-more {
  margin: 40rpx 0;
}

.publish-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 100;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .help-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .help-footer {
    flex-direction: column;
    align-items: flex-start;
  }
}
