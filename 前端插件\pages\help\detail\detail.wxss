/* pages/help/detail/detail.wxss */

/* ==================== 页面容器 ==================== */
.help-detail-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx; /* 为底部操作栏留空间 */
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* ==================== 详情内容 ==================== */
.detail-content {
  padding: 20rpx;
}

/* ==================== 卡片通用样式 ==================== */
.help-info-card,
.help-description-card,
.responses-card,
.contact-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.response-count {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

/* ==================== 需求信息卡片 ==================== */
.status-bar {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.status-tag,
.urgency-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.status-pending {
  background: #FF6B35;
}

.status-in_progress {
  background: #4CAF50;
}

.status-completed {
  background: #2196F3;
}

.status-cancelled {
  background: #9E9E9E;
}

.urgency-urgent {
  background: #F44336;
}

.urgency-normal {
  background: #FF9800;
}

.urgency-low {
  background: #4CAF50;
}

.help-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.help-meta {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.meta-item .iconfont {
  font-size: 28rpx;
  color: #FF6B35;
  margin-right: 8rpx;
  width: 32rpx;
}

.meta-text {
  flex: 1;
}

/* ==================== 发布者信息 ==================== */
.publisher-info {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.publisher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.publisher-details {
  flex: 1;
}

.publisher-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.publisher-stats {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
}

/* ==================== 详细描述 ==================== */
.description-content {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 20rpx;
  white-space: pre-wrap;
}

.images-container {
  margin-top: 20rpx;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}

.help-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
}

/* ==================== 响应列表 ==================== */
.no-responses {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.no-responses .iconfont {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.no-responses-text {
  font-size: 28rpx;
}

.response-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.response-item {
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.response-item.selected {
  border-color: #FF6B35;
  background: rgba(255, 107, 53, 0.05);
}

.responder-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.responder-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.responder-details {
  flex: 1;
}

.responder-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.responder-stats {
  display: flex;
  gap: 12rpx;
}

.response-status {
  display: flex;
  align-items: center;
}

.status-selected {
  background: #FF6B35;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.response-content {
  margin-bottom: 16rpx;
}

.response-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  margin-bottom: 12rpx;
}

.response-time {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.response-time .iconfont {
  font-size: 24rpx;
  margin-right: 6rpx;
  color: #FF6B35;
}

.response-date {
  font-size: 24rpx;
  color: #999;
}

.response-actions {
  display: flex;
  justify-content: flex-end;
}

.select-btn {
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  font-weight: bold;
}

.select-btn:active {
  transform: scale(0.95);
}

/* ==================== 联系信息 ==================== */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.contact-item .iconfont {
  font-size: 28rpx;
  color: #FF6B35;
  margin-right: 12rpx;
  width: 32rpx;
}

/* ==================== 底部操作栏 ==================== */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.response-btn {
  flex: 1;
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  color: white;
  border: none;
  border-radius: 28rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

.response-btn .iconfont {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.response-btn:active {
  transform: scale(0.98);
}

.cancel-btn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
  border: 2rpx solid #ddd;
  border-radius: 28rpx;
  height: 88rpx;
  font-size: 30rpx;
}

.complete-btn {
  flex: 1;
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  color: white;
  border: none;
  border-radius: 28rpx;
  height: 88rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.helper-status {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background: #f8f9fa;
  border-radius: 28rpx;
  font-size: 28rpx;
  color: #666;
}

/* ==================== 响应弹窗 ==================== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.response-modal {
  background: white;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  padding: 8rpx;
}

.modal-content {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: bold;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 16rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #fafafa;
}

.form-textarea:focus {
  border-color: #FF6B35;
  background: white;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.form-input:focus {
  border-color: #FF6B35;
  background: white;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  border: none;
}

.modal-btn.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.modal-btn.submit-btn {
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  color: white;
  font-weight: bold;
}