/* pages/help/detail/detail.wxss */

/* ==================== 页面容器 ==================== */
.help-detail-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx; /* 为底部操作栏留空间 */
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* ==================== 详情内容 ==================== */
.detail-content {
  padding: 24rpx;
  background: #f8f9fa;
}

/* ==================== 卡片通用样式 ==================== */
.help-info-card,
.responses-card,
.progress-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.response-count {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

/* ==================== 需求信息卡片 ==================== */
.title-section {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 28rpx;
}

.urgency-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.urgency-urgent {
  background: linear-gradient(135deg, #FF4444, #FF6B6B);
}

.urgency-normal {
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
}

.urgency-low {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
}

.help-title {
  font-size: 44rpx;
  font-weight: bold;
  color: #1a1a1a;
  line-height: 1.3;
  letter-spacing: 0.5rpx;
  flex: 1;
}

.help-description {
  font-size: 28rpx;
  line-height: 1.7;
  color: #555;
  margin-bottom: 36rpx;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #FF6B35;
  position: relative;
}

/* 需求信息网格 */
.help-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 40rpx;
  padding: 8rpx;
}

.info-item {
  background: linear-gradient(135deg, #f8f9fa, #fff);
  padding: 24rpx 20rpx;
  border-radius: 16rpx;
  text-align: center;
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.info-label {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.info-value {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  line-height: 1.2;
}

.info-value.points {
  color: #FF6B35;
  font-size: 36rpx;
  font-weight: 900;
}

/* ==================== 发布者信息 ==================== */
.publisher-section {
  padding: 28rpx;
  background: linear-gradient(135deg, #f8f9fa, #fff);
  border-radius: 20rpx;
  border: 1rpx solid #e8e8e8;
  margin-top: 16rpx;
}

.publisher-info {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.2);
}

.publisher-avatar image {
  width: 100%;
  height: 100%;
}

.avatar-text {
  font-size: 36rpx;
  color: white;
  font-weight: bold;
}

.publisher-details {
  flex: 1;
}

.publisher-name {
  font-size: 34rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 12rpx;
  letter-spacing: 0.5rpx;
}

.publisher-stats {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.stat-rating {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: bold;
}

.stat-count {
  font-size: 26rpx;
  color: #666;
}



/* ==================== 通用区块样式 ==================== */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-indicator {
  width: 6rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  border-radius: 3rpx;
  margin-right: 20rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

/* ==================== 响应列表 ==================== */
.no-responses {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.no-responses .iconfont {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.no-responses-text {
  font-size: 28rpx;
}

.response-list {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

.response-item {
  background: white;
  border-radius: 20rpx;
  padding: 28rpx;
  transition: all 0.3s ease;
  border: 2rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.response-item.selected {
  border-color: #FF6B35;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.03), rgba(255, 183, 77, 0.02));
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.15);
  transform: translateY(-2rpx);
}

.responder-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.responder-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.responder-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 3rpx 10rpx rgba(76, 175, 80, 0.2);
}

.responder-avatar image {
  width: 100%;
  height: 100%;
}

.responder-details {
  flex: 1;
}

.responder-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  letter-spacing: 0.3rpx;
}

.responder-stats {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.stat-rating {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: bold;
}

.stat-experience {
  font-size: 26rpx;
  color: #666;
}

.response-status {
  display: flex;
  align-items: center;
}

.status-selected {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: bold;
  box-shadow: 0 3rpx 10rpx rgba(76, 175, 80, 0.3);
}

.select-btn {
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  color: white;
  border: none;
  border-radius: 28rpx;
  padding: 14rpx 28rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
  min-width: 120rpx;
}

.select-btn:active {
  transform: scale(0.95);
}

.response-content {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #FF6B35;
  margin-top: 16rpx;
}

.response-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 16rpx;
  font-style: italic;
}

.response-time {
  font-size: 24rpx;
  color: #888;
  font-weight: 500;
}

/* ==================== 进度跟踪 ==================== */
.progress-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.progress-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.progress-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.progress-item.completed .progress-number {
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  color: white;
}

.progress-item.current .progress-number {
  background: white;
  border: 3rpx solid #FF6B35;
  color: #FF6B35;
}

.progress-item.pending .progress-number {
  background: #f0f0f0;
  color: #999;
}

.progress-content {
  flex: 1;
  padding-top: 4rpx;
}

.progress-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.progress-item.pending .progress-title {
  color: #999;
}

.progress-time {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.progress-item.pending .progress-time {
  color: #999;
}

/* ==================== 联系信息 ==================== */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.contact-item .iconfont {
  font-size: 28rpx;
  color: #FF6B35;
  margin-right: 12rpx;
  width: 32rpx;
}

/* ==================== 底部操作栏 ==================== */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  border-top: 1rpx solid #f0f0f0;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.response-btn {
  flex: 1;
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  color: white;
  border: none;
  border-radius: 32rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 16rpx rgba(255, 107, 53, 0.3);
}

.response-btn .iconfont {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.response-btn:active {
  transform: scale(0.98);
}

.cancel-btn {
  flex: 1;
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e0e0e0;
  border-radius: 32rpx;
  height: 96rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.complete-btn {
  flex: 1;
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  color: white;
  border: none;
  border-radius: 32rpx;
  height: 96rpx;
  font-size: 30rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 107, 53, 0.3);
}

.helper-status-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  background: linear-gradient(135deg, #f8f9fa, #fff);
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #666;
  border: 2rpx solid #f0f0f0;
  text-align: center;
  padding: 0 20rpx;
}

/* ==================== 响应弹窗 ==================== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.response-modal {
  background: white;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  padding: 8rpx;
}

.modal-content {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: bold;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 16rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #fafafa;
}

.form-textarea:focus {
  border-color: #FF6B35;
  background: white;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.form-input:focus {
  border-color: #FF6B35;
  background: white;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  border: none;
}

.modal-btn.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.modal-btn.submit-btn {
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  color: white;
  font-weight: bold;
}