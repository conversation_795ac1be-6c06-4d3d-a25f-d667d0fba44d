import config from '../../utils/config.js'
const API = require('../../utils/api.js')
const Auth = require('../../utils/auth.js')
const Adapter = require('../../utils/adapter.js')
const util = require('../../utils/util.js')
const NC = require('../../utils/notificationcenter.js')
const pageCount = 10
const app = getApp()
const appName = app.globalData.appName
let rewardedVideoAd = null
import Poster from '../../miniprogram_npm/wxa-plugin-canvas/poster/poster';

Page({
  // 初始数据
  data: {
    shareTitle: appName,
    userInfo: {},
    userSession: {},
    wxLoginInfo: {},
    memberUserInfo: {},
    settings: {},
    isLoginPopup: false,
    system: '',
    enterpriseMinapp: '',
    task: {
      signined: false,
      shareapped: false,
      openAdVideoed: false
    },
    taskError: false,
    inviteUser: [],
    inviteTotal: 0,
    inviteInfo: {},
    posterConfig: {},
    showPoster: false,
    posterUrl: '',
    cardCode: '',
    showDialog: false,
    pageName:'earnIntegral',
    isLogin:false,
    hasWechatInstall: app.globalData.hasWechatInstall
  },

  // 页面加载
  onLoad: function() {
    let self = this
    let system =/ios/i.test(app.globalData.deviceInfo.system)? 'iOS' : 'Android'
    let platform=app.globalData.deviceInfo.platform
    self.setData({
      system: system,
      platform: platform
    })
        
  },
  onShow:function()
  {
    let self = this
    Auth.setUserMemberInfoData(self)
    Auth.checkLogin(self)
    let userSession = wx.getStorageSync('userSession');    
    if(userSession.sessionId)
    {
      self.initData()
      self.setData({isLogin:true})
    }
  },
  agreeGetUser: function (e) {
    let self = this;
    Auth.checkAgreeGetUser(e, app, self, API, '0');
  },

closeLoginPopup() {
    Auth.logout(this);
    this.setData({
      isLoginPopup: false
    });
  },
  openLoginPopup() {
    this.setData({
      isLoginPopup: true
    });
  },


  // 分享
  onShareAppMessage: function(e) {
    var self = this
    var data = {}
    // if (e.from === 'button' && !e.target.id) { // id为1的是立即邀请的分享按钮
    //   data.userId = this.data.userSession.userId
    //   data.sessionId = this.data.userSession.sessionId
    //   var task = self.data.task
    //   API.shareApp(data).then(res => {
    //     if (res.code == 'error') {
    //       task.canShareApp = res.data.canShareApp
    //       wx.showToast({
    //         title: res.message,
    //         mask: false,
    //         icon: "none",
    //         duration: 2000
    //       })
    //     } else {
    //       var raw_user_shareapp = res.raw_user_shareapp
    //       task.canShareApp = res.canShareApp
    //       task.shareappedCount = raw_user_shareapp
    //       self.setData({
    //         task: task
    //       })

    //       if (self.data.userSession.sessionId) {
    //         Auth.checkGetMemberUserInfo(self.data.userSession, self, API)
    //       }
    //       wx.showToast({
    //         title: res.message,
    //         mask: false,
    //         icon: "none",
    //         duration: 4000
    //       })
    //     }
    //   })
    // }

    var imageUrl= this.data.settings.raw_default_share_image;
    let txt = getApp().globalData.appDes
    let name = getApp().globalData.appName
    let invitecode=this.data.memberUserInfo.invitecode;
    let  path=this.data.inviteInfo.path || '/pages/index/index?invitecode='+invitecode;
    return {
      title: name + '-' + txt,
      path: path,
      imageUrl: imageUrl
    }
  },

  async initData() {
    wx.showLoading({
      title: '加载中'
    })
    await this.getInviteUser()
    await this.getSetting()
    this.getMytaskStatus()
    wx.hideLoading()
  },

  // 查看更多邀请好友
  viewMore() {
    wx.navigateTo({
      url: '/pages/userlist/userlist?from=myinvite',
    })
  },

  viewUser(e) {
    let authorId = e.currentTarget.id
    let url = '../author/author?userid=' + authorId + '&postype=topic'
    wx.navigateTo({
      url
    })
  },

  // 输入卡码
  cardcodeIntegral(e) {
    let self=this;
    let cardcode = this.data.cardCode
    if (!cardcode) {
      wx.showToast({
        icon: 'none',
        title: '请输入积分兑换码'
      })
      return
    }

    let userSession = wx.getStorageSync('userSession')
    wx.z.showDialog({
      type: "confirm",
      title: "提示",
      confirmText: "提交",
      content: "确认兑换积分吗？",  
      success: (res) => {
        if (res.confirm) {      
          let params = {
            sessionid: userSession.sessionId,
            userid: userSession.userId,
            cardcode
          }
          app.$api.postCardcodeIntegral(params).then(res=>{
            if (res.code) {              
              wx.showToast({
                icon: 'none',
                title: res.message || res.errmsg || '出错了，请稍后再试！'
              })
              self.setData({cardCode:''})
            }
            else {
              if (userSession.sessionId) {
                Auth.checkGetMemberUserInfo(userSession, self, API);
                Auth.setUserMemberInfoData(self)


              }
              self.setData({cardCode:''})
              wx.showToast({
                icon: 'none',
                title: res.message || ''
              })
            }     
          })        
        }
      }
    })
  },

  // 获取邀请好友
  async getInviteUser() {
    let userInfo = wx.getStorageSync('userSession')
    let params = {
      sessionid: userInfo.sessionId,
      userid: userInfo.userId,
      per_page:4,
      page:1
    }
    const res = await app.$api.getInviteUser(params)
    let list = res.users || []
    let inviteTotal = res.count
    list.length = (list.length <= 5) ? 5 : list.length

    this.setData({
      inviteUser: list,
      inviteTotal
    })
  },

  // 获取邀请信息
  async getInviteQrcode() {
    let userInfo = wx.getStorageSync('userSession')
    let params = {
      sessionid: userInfo.sessionId,
      userid: userInfo.userId
    }
    const res = await app.$api.getInviteQrcode(params)
    if (res.qrcodeurl) {
      this.setData({
        posterConfig: {
          width: 600,
          height: 800,
          backgroundColor: '#FFFFFF',
          images: [{
            width: 100,
            height: 100,
            x: 250,
            y: 100,
            borderRadius: 100,
            url: this.data.userInfo.avatarUrl
          }, {
            x: 200,
            y: 500,
            width: 200,
            height: 200,
            url: res.qrcodeurl
          }],
          blocks: [{
            x: 200,
            y: 340,
            width: 200,
            height: 1,
            backgroundColor: '#CCCCCC'
          }],
          texts: [{
              x: 300,
              y: 250,
              fontSize: 32,
              fontWeight: 700,
              color: '#333',
              text: this.data.userInfo.nickName,
              textAlign: 'center'
            }, {
            x: 300,
            y: 320,
            fontSize: 26,
            color: '#666666',
            text: '精彩内容，与君共享',
            textAlign: 'center'
          }, {
            x: 300,
            y: 740,
            fontSize: 20,
            color: '#999999',
            text: '长按识别小程序码',
            textAlign: 'center'
          }]
        }
      }, () => {
        Poster.create()
      })
    } else {
      wx.showToast({
        icon: 'none',
        title: '生成海报失败，请稍后重试'
      })
    }
  },

  onPosterSuccess(e) {
    const url = e.detail
    if (url) {
      this.setData({
        posterUrl: url,
        showPoster: true
      })
    }
  },

  onPosterClose() {
    this.setData({
      showPoster: false
    })
  },

  savePoster() {
    let self = this
    wx.saveImageToPhotosAlbum({
      filePath: self.data.posterUrl,
      success(result) {
        wx.showModal({
          title: '提示',
          content: '海报已存入手机相册，赶快分享到朋友圈吧',
          showCancel: false,
          success: function (res) {
            res.confirm && self.onPosterClose()
          }
        })
      },
      fail: function (err) {
        if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
          wx.showModal({
            title: '用户未授权',
            content: '如需保存海报图片到相册，需获取授权.是否在授权管理中选中“保存到相册”?',
            showCancel: true,
            success: function (res) {
              if (res.confirm) {
                wx.openSetting({
                  success: function success(res) {
                    wx.openSetting({
                      success(settingdata) { }
                    })
                  }
                })
              }
            }
          })
        }
      }
    })
    // 坑：canvasToTempFilePath是生成到本地的临时文件，不需要再downLoadFile

    // wx.downloadFile({
    //   url: self.data.posterUrl,
    //   success: function (res) {
    //     wx.saveImageToPhotosAlbum({
    //       filePath: res.tempFilePath,
    //       success(result) {
    //         wx.showModal({
    //           title: '提示',
    //           content: '海报已存入手机相册，赶快分享到朋友圈吧',
    //           showCancel: false,
    //           success: function (res) {
    //             if (res.confirm) {
    //             }
    //           }
    //         })
    //       },
    //       fail: function (err) {
    //         if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
    //           wx.showModal({
    //             title: '用户未授权',
    //             content: '如需保存海报图片到相册，需获取授权.是否在授权管理中选中“保存到相册”?',
    //             showCancel: true,
    //             success: function (res) {
    //               if (res.confirm) {
    //                 wx.openSetting({
    //                   success: function success(res) {
    //                     wx.openSetting({
    //                       success(settingdata) { }
    //                     })
    //                   }
    //                 })
    //               }
    //             }
    //           })
    //         }
    //       }
    //     });
    //   },
    //   fail: function(err) {
    //     console.log(err)
    //   }
    // });
  },

  viewPoster(e) {
    let src = e.currentTarget.dataset.src
    wx.previewImage({
      urls: [src],
    })
  },

  // 积分设置
  getSetting() {
    var self = this
    API.getSettings().then(res => {
      self.setData({
        settings: res.settings
      })
    })
  },

  // 任务状态
  getMytaskStatus() {
    var data = {}
    var self = this
    data.userId = this.data.userSession.userId
    data.sessionId = this.data.userSession.sessionId

    API.myTask(data).then(res => {
      if (res.code) {
        self.setData({
          taskError: true
        })
        wx.showToast({
          title: res.message,
          mask: false,
          icon: "none",
          duration: 2000
        })
      } else {
        self.setData({
          task: res.task
        })
        self.loadInterstitialAd(self.data.task.excitationAdId)
        if (self.data.userSession.sessionId) {
          Auth.checkGetMemberUserInfo(self.data.userSession, self, API)
        }
      }
    })
  },

  // 签到
  signin() {
    var self = this
    var data = {}
    data.userId = this.data.userSession.userId
    data.sessionId = this.data.userSession.sessionId

    API.signin(data).then(res => {
      if (res.code == 'error') {
        wx.showToast({
          title: res.message,
          mask: false,
          icon: "none",
          duration: 2000
        })
      } else {
        var task = self.data.task
        task.signined = true
        self.setData({
          task: task
        })

        if (self.data.userSession.sessionId) {
          Auth.checkGetMemberUserInfo(self.data.userSession, self, API)
        }

        wx.showToast({
          title: res.message,
          mask: false,
          icon: "none",
          duration: 4000
        })
      }
    })
  },

  // 激励视频
  openAdVideo() {
    var platform = this.data.platform
    if (platform == 'devtools') {
      Adapter.toast("开发工具无法显示激励视频", 2000)
    } else {
      rewardedVideoAd.show().catch(() => {
        rewardedVideoAd.load().then(() => rewardedVideoAd.show()).catch(err => {
          Adapter.toast("激励视频广告获取失败！", 2000)
        })
      })
    }
  },

  // 加载广告
  loadInterstitialAd(excitationAdId) {
    var self = this
    var data = {}
    data.userId = this.data.userSession.userId
    data.sessionId = this.data.userSession.sessionId
    if (wx.createRewardedVideoAd) {
      rewardedVideoAd = wx.createRewardedVideoAd({
        adUnitId: excitationAdId
      })

      rewardedVideoAd.onLoad(() => {
        // console.log('广告加载成功')
      })
      rewardedVideoAd.onError((err) => {
        // console.log('广告加载：' + err)
      })
      rewardedVideoAd.onClose((res) => {
        var task = self.data.task;
        if (res && res.isEnded) {
          API.openAdVideo(data).then(res => {
            if (res.code == 'error') {
              task.canOpenAdVideo = res.data.canOpenAdVideo;
              wx.showToast({
                title: res.message,
                mask: false,
                icon: "none",
                duration: 2000
              })
            } else {
              task.openAdVideoedCount = res.raw_user_openAdVideo;
              task.canOpenAdVideo = res.canOpenAdVideo;
              self.setData({
                task: task
              })

              if (self.data.userSession.sessionId) {
                Auth.checkGetMemberUserInfo(self.data.userSession, self, API)
              }

              wx.showToast({
                title: res.message,
                mask: false,
                icon: "none",
                duration: 4000
              })
            }
          })
        } else {
          Adapter.toast("你中途关闭了视频", 3000)
        }
      })
    }
  },

  // 去积分等级说明页
  toIntegralDes() {
    wx.navigateTo({
      url: `../myinfo/myinfo?integral=${this.data.memberUserInfo.integral}`
    })
  },

  getPhoneNumber(e){
    Adapter.getUserPhoneNumber(e,this,API,Auth);
  },

  // 查看卡码说明
  seeRule() {
    this.setData({
      showDialog: true
    })
  },

  // dount分享
  dountShare() {
    let invitecode = this.data.memberUserInfo.invitecode
    let path = this.data.inviteInfo.path || '/pages/index/index?invitecode=' + invitecode
    wx.miniapp.shareMiniProgramMessage({
      userName: app.globalData.gh_id,
      path,
      imagePath: '/images/uploads/logo.png',
      webpageUrl: 'https://m.wolinwarm.com',
      title: app.globalData.appName,
      description: app.globalData.appDes,
      withShareTicket: false,
      miniprogramType: 0,
      scene: 0,
      success(res) {
        // console.log('分享小程序成功:' + res)
      }
    })
  }
})