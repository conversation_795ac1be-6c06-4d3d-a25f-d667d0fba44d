<import src="../../templates/login-popup.wxml" />
<view class="me-view">
  <view class="header">
    <!-- 用户信息 -->
    <view class="user" bindtap="{{userInfo.isLogin ? 'goProfile' : 'openLoginPopup'}}">
      <image class="avatar" src='{{userInfo.avatarUrl}}' />
      <view class="info">
        <view>
          <view class="name">{{userInfo.nickName}}</view>
          <block wx:if="{{userInfo.isLogin && memberUserInfo.member =='00'}}">
            <text class="member">官方</text>
            <text class="member">{{memberUserInfo.membername}}</text>
          </block>
          <block wx:if="{{userInfo.isLogin && memberUserInfo.member =='01'}}">
            <text class="member">VIP</text>
            <text class="member">{{memberUserInfo.membername}}</text>
          </block>
          <block wx:if="{{userInfo.isLogin && memberUserInfo.member !='01' && memberUserInfo.member !='00'}}">
            <text class="member">Lv{{memberUserInfo.member-10}}</text>
            <text class="member">{{memberUserInfo.membername}}</text>
          </block>
          <view class="description">
            <text wx:if="{{memberUserInfo.description}}">{{memberUserInfo.description}}</text>
            <text wx:else>这家伙很懒, 什么也没留下...</text>
          </view>
        </view>
        <text class="iconfont icon-arrow-right" />
      </view>
    </view>
    <!-- 数据展示 -->
    <view class='data'>
      <view class="item" data-mytype="mytopics" bindtap="redictPage">
        <view class='num'>{{memberUserInfo.topiccount || 0}}</view>
        <view class="label">动态</view>
      </view>
      <!-- <view class="item" data-mytype="myposts" bindtap="redictPage">
        <view class='num'>{{memberUserInfo.postcount || 0}}</view>
        <view class="label">文章</view>
      </view> -->
      <view class="divider" />
      <view class="item" data-mytype="followmeAuthor" bindtap="redictPage">
        <view class='num'>{{memberUserInfo.followmecount || 0}}</view>
        <view class="label">粉丝</view>
      </view>
      <view class="divider" />
      <view class="item" data-mytype="myFollowAuthor" bindtap="redictPage">
        <view class='num'>{{memberUserInfo.myfollowcount || 0}}</view>
        <view class="label">关注</view>
      </view>
      <view class="divider" />
      <view class="item" data-mytype="myIntegral" bindtap="redictPage">
        <view class='num'>{{memberUserInfo.integral || 0}}</view>
        <view class="label">积分</view>
      </view>
    </view>
    <!-- <text class="iconfont icon-bg" /> -->
  </view>
  <!-- vip会员 -->
  <!-- #if MP -->
  <view class="card card-vip" wx:if="{{memberUserInfo.member !='00'}}">
    <view class="vip-des">
      <text class="iconfont icon-vip" />
      <view>
        <text wx:if="{{memberUserInfo.member != '01' &&  memberUserInfo.member != '00'}}">尊享VIP会员专属权益</text>
        <view wx:if="{{ (memberUserInfo.member === '01') && memberUserInfo.memberenddate }}">
          <block wx:if="{{ memberUserInfo.memberenddate !== '0000-00-00' }}">会员到期时间<text style="color:#f1d8b2">{{ memberUserInfo.memberenddate }}</text></block>
          <block wx:else>会员已过期</block>
        </view>
      </view>
    </view>
    <view class="point-btn" data-mytype="buyvip" data-date="{{ memberUserInfo.memberenddate !== '0000-00-00' ? memberUserInfo.memberenddate : '' }}" catchtap="redictPage">
      {{ memberUserInfo.member == '01' ? '立即续费' : '立即开通' }}
    </view>
  </view>
  <view class="card card-point">
    <view class="point-des" data-mytype="mysignin" catchtap="redictPage">
      <text class="iconfont icon-point" />
      <text>完成任务赚取积分</text>
      <text class="iconfont icon-arrow-right" />
    </view>
    <view wx:if="{{enable_redpack=='1'}}" class="point-btn" data-mytype="sendredpack" catchtap="redictPage">
      兑换红包
    </view>
  </view>
  <!-- #endif -->

  <!-- 自定义广告 -->
  <view wx:if="{{banner.enable == 'yes'}}" style="margin: 30rpx;border-radius: 20rpx;overflow: hidden;">
    <custom-ad info="{{banner}}" type="{{banner.adstyle}}" is-border="{{false}}" />
  </view>
  <!-- 我的记录 -->
  <view class='my-log card'>
    <view class="title">我的记录</view>
    <van-row class="content">
      <van-col span="6">
        <view class="item" data-mytype="myreads" bindtap="redictPage">
          <text class="iconfont icon-eye" />
          <view class="label">浏览</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="mycomments" bindtap="redictPage">
          <text class="iconfont icon-comment" />
          <view class="label">评论</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="mylikes" bindtap="redictPage">
          <text class="iconfont icon-like" />
          <view class="label">点赞</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="mypraises" bindtap="redictPage">
          <text class="iconfont icon-wallet" />
          <view class="label">鼓励</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="mynotice" bindtap="redictPage">
          <view class='val-red'>
            <text class="iconfont icon-news" />
            <text class="tag" wx:if="{{messagesCount !='0' && messagesCount !='' }}">{{messagesCount}}</text>
          </view>
          <view class="label">消息</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="myorders" bindtap="redictPage">
          <view class='pictrue'>
            <text class="iconfont icon-bill" />
          </view>
          <view class="label">付费账单</view>
        </view>
      </van-col>
      <!-- <van-col span="6">
        <view class="item" data-mytype="myminapperorders" bindtap="redictPage">
          <view class='pictrue'>
            <text class="iconfont icon-bill" />
          </view>
          <view class="label">商城订单</view>
        </view>
      </van-col> -->
      <van-col span="6">
        <view class="item" data-mytype="myhelp" bindtap="redictPage">
          <text class="iconfont icon-help" />
          <view class="label">我的求助</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="myinvite" bindtap="redictPage">
          <text class="iconfont icon-invite" />
          <view class="label">邀请</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="myshare" bindtap="redictPage">
          <text class="iconfont icon-recommend" />
          <view class="label">推荐</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="mycardcode" bindtap="redictPage">
          <text class="iconfont icon-coupon" />
          <view class="label">优惠码</view>
        </view>
      </van-col>
      <block wx:if="{{enable_redpack=='1'}}">
        <van-col span="6">
          <view class="item" data-mytype="myredpack" bindtap="redictPage">
            <text class="iconfont icon-lucky-money" />
            <view class="label">红包</view>
          </view>
        </van-col>
      </block>
      <van-col span="6">
        <view class="item" data-mytype="updateUserInfo" bindtap="redictPage">
          <text class="iconfont icon-refresh" />
          <view class="label">更新信息</view>
        </view>
      </van-col>
      <van-col span="6" wx:if="{{zan_display==='1'}}">
        <view class="item" data-mytype="myzanimage" bindtap="redictPage">
          <text class="iconfont icon-qrcode" />
          <view class="label">{{raw_praise_word}}码</view>
        </view>
      </van-col>
    </van-row>
  </view>
  <!-- 我的小店 -->
  <view class='card' wx:if="{{enableWechatshop || enableMinishop }}">
    <view class='title'>我的小店</view>
    <van-row class="content">
      <!-- #if MP -->
       <van-col span="6" wx:if="{{enableWechatshop}}">
        <view class="item" data-mytype="mywechatOrder" bindtap="redictPage">
          <text class="iconfont icon-note" />
          <view class="label">订单</view>
        </view>
      </van-col>
      <!-- <van-col span="6" wx:if="{{enableMinishop}}">
        <view class="item" data-mytype="myshoporders" bindtap="redictPage">
          <text class="iconfont icon-order" />
          <view class="label">小商店订单</view>
        </view>
      </van-col> -->
      <van-col span="6" wx:if="{{enableMinishop}}">
        <view class="item" data-mytype="myshoppingcart" bindtap="redictPage">
          <text class="iconfont icon-shipping-bag" />
          <view class="label">购物袋</view>
        </view>
      </van-col>     
      <van-col span="6" wx:if="{{enableMinishop}}">
        <view wx:if="{{memberUserInfo.member =='00'}}" class="item" data-mytype="shophelper" bindtap="redictPage">
          <text class="iconfont icon-shop" />
          <view class="label">小店助手</view>
        </view>
      </van-col>
      <van-col span="6" wx:if="{{enableMinishop}}">
        <view wx:if="{{memberUserInfo.member =='00'}}" class="item" data-mytype="kefuMinApp" bindtap="redictPage">
          <text class="iconfont icon-service" />
          <view class="label">客服助手</view>
        </view>
      </van-col>      
      <!-- #endif -->
      <van-col span="6">
        <view class="item" data-mytype="myaddress" bindtap="redictPage">
          <text class="iconfont icon-location" />
          <view class="label">收货地址</view>
        </view>
      </van-col>
      
    </van-row>
  </view>
  <!-- 管理功能 -->
  <view class='card' wx:if="{{memberUserInfo.member =='00'}}">
    <view class='title'>管理功能</view>
    <van-row class="content">
      <van-col span="6">
        <view class="item" data-mytype="topicspending" bindtap="redictPage">
          <view class='val-red'>
            <text class="iconfont icon-tag" />
            <text wx:if="{{pendingCount.topic_pending_count}}" class="tag">{{pendingCount.topic_pending_count}}</text>
          </view>
          <view class="label">审核话题</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="replyspending" bindtap="redictPage">
          <view class='val-red'>
            <text class="iconfont icon-topic-reply" />
            <text wx:if="{{pendingCount.reply_pending_count}}" class="tag">{{pendingCount.reply_pending_count}}</text>
          </view>
          <view class="label">话题回复</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="postspending" bindtap="redictPage">
          <view class='val-red'>
            <text class="iconfont icon-article" />
            <text wx:if="{{pendingCount.post_pending_count}}" class="tag">{{pendingCount.post_pending_count}}</text>
          </view>
          <view class="label">审核文章</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="commentspending" bindtap="redictPage">
          <view class='val-red'>
            <text class="iconfont icon-reply" />
            <text wx:if="{{pendingCount.comment_pending_count}}" class="tag">{{pendingCount.comment_pending_count}}</text>
          </view>
          <view class="label">文章评论</view>
        </view>
      </van-col>
     <van-col span="6">
        <view class="item" data-mytype="wechatOrder" bindtap="redictPage">
          <text class="iconfont icon-note" />
          <view class="label">小店订单</view>
        </view>
      </van-col>
       <van-col span="6">
        <view wx:if="{{enableMinishop}}" class="item" data-mytype="orderManage" bindtap="redictPage">
          <text class="iconfont icon-order-admin" />
          <view class="label">小商店订单</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="adminCenter" bindtap="redictPage">
          <view class='pictrue'>
            <text class="iconfont icon-admin-center" />
          </view>
          <view class="label">管理中心</view>
        </view>
      </van-col>
      <!-- #if MP -->
      <van-col span="6">
        <view class="item" data-mytype="publishMinApp" bindtap="redictPage">
          <view class='pictrue'>
            <text class="iconfont icon-release" />
          </view>
          <view class="label">发布小程序</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="shujuMinApp" bindtap="redictPage">
          <view class='pictrue'>
            <text class="iconfont icon-statistic" />
          </view>
          <view class="label">数据统计</view>
        </view>
      </van-col>
      <!-- #endif -->
    </van-row>
  </view>
  <!-- 系统服务 -->
  <view class='card'>
    <view class='title'>系统服务</view>
    <van-row class="content">
      <van-col span="6">
        <view class="item" data-mytype="about" bindtap="redictPage" hover-class='none'>
          <text class="iconfont icon-about" />
          <view class="label">关于我们</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="privacy" bindtap="redictPage" hover-class='none'>
          <text class="iconfont icon-history" />
          <view class="label">隐私政策</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" data-mytype="logout" bindtap="redictPage" hover-class='none'>
          <text class="iconfont icon-clear" />
          <view class="label">{{ userInfo.isLogin ? '重新登录' : '清除缓存' }}</view>
        </view>
      </van-col>
      <!-- #if MP -->
      <van-col span="6">
        <view class="item" catchtap="openSettting" hover-class='none'>
          <text class="iconfont icon-set" />
          <view class="label">设置</view>
        </view>
      </van-col>
      <!-- #endif -->
      <van-col span="6">
        <view class="item" data-mytype="myMessages" bindtap="redictPage" hover-class='none'>
          <text class="iconfont icon-appointment" />
          <view class="label">预约留言</view>
        </view>
      </van-col>
      <van-col span="6">
        <view class="item" bindtap="toIntegralDes" hover-class='none'>
          <text class="iconfont icon-rule" />
          <view class="label">等级规则</view>
        </view>
      </van-col>
      <!-- <van-col span="6">
        <button class='item item-btn' open-type='feedback' hover-class='none'>
          <text class="iconfont icon-feedback" />
          <view class="label">意见反馈</view>
        </button>
      </van-col> -->

      <!-- #if MP -->
      <van-col span="6">
        <button class='item item-btn' open-type='contact' hover-class='none' show-message-card="{{true}}"	send-message-title="{{send_message_title}}"  send-message-path="{{send_message_path}}" 	send-message-img="{{send_message_img}}" bindcontact="handleContact">
          <text class="iconfont icon-shop-service" />
          <view class="label">联系客服</view>
        </button>
      </van-col>
      <scan-login />
      <!-- #elif NATIVE -->
      <van-col span="6" wx:if="{{userInfo.isLogin}}">
        <view class="item" bindtap="deleteAccount" >
          <text class="iconfont icon-check" />
          <view class="label">注销账号</view>
        </view>
      </van-col>
      <!-- #endif -->
    </van-row>
  </view>
  <!-- 订阅消息 -->
  <!-- <view class="card subscribe" wx:if="{{memberUserInfo.raw_new_post_message_id}}">
    <view class='title'>订阅消息</view>
    <view class="content">
      <view>
        <text>内容更新消息</text>
        <view class="tip">订阅1次可收到1条消息推送</view>
      </view>
      <view class="subscribe-info">
        <text>已订阅 {{memberUserInfo.newcontentSubscribeCount || 0}} 次</text>
        <view class="subscribe-btn" data-subscribetype="newcontent" data-subscribemessagesid="{{memberUserInfo.raw_new_post_message_id}}" bindtap="subscribeMessage" wx:if="{{memberUserInfo.raw_new_post_message_id !=''}}">
          订阅
        </view>
      </view>
    </view>
  </view> -->
</view>
<copyright />

<!-- 兑换红包 -->
<redeem-points show="{{showRedeemPop}}" list="{{redpackCashStep}}" bind:onClose="onCloseRedeemPop" />

<!-- 登录弹窗 -->
<template is="login-popup" data="{{show:isLoginPopup,userInfo:userInfo,hasWechatInstall:hasWechatInstall}}"></template>

<!-- 确认弹窗 -->
<z-dialog />

<!-- 隐私同意弹窗 -->
<privacy />