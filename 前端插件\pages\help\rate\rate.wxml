<!--pages/help/rate/rate.wxml-->
<view class="rate-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 评价内容 -->
  <view class="rate-content" wx:else>
    <!-- 需求信息卡片 -->
    <view class="help-card">
      <view class="help-title">{{helpDetail.title}}</view>
      <view class="help-type">{{helpDetail.help_type}} • {{helpDetail.urgency}}</view>
    </view>

    <!-- 被评价用户信息 -->
    <view class="user-card">
      <view class="user-avatar">
        <image wx:if="{{targetUser.avatar}}" src="{{targetUser.avatar}}" mode="aspectFill"></image>
        <text wx:else class="avatar-text">{{targetUser.name.charAt(0)}}</text>
      </view>
      <view class="user-info">
        <view class="user-name">{{targetUser.name}}</view>
        <view class="user-desc" wx:if="{{ratingType === 'requester_to_helper'}}">为您提供了帮助</view>
        <view class="user-desc" wx:else>发布了互助需求</view>
      </view>
    </view>

    <!-- 星级评分 -->
    <view class="rating-section">
      <view class="section-title">请为本次服务评分</view>
      <view class="stars-container">
        <view
          class="star {{index < rating ? 'active' : ''}}"
          wx:for="{{[1,2,3,4,5]}}"
          wx:key="index"
          data-rating="{{item}}"
          bindtap="onStarTap"
        >
          <text class="iconfont icon-star"></text>
        </view>
      </view>
      <view class="rating-text">
        <text wx:if="{{rating === 0}}">请选择评分</text>
        <text wx:elif="{{rating === 1}}">很不满意</text>
        <text wx:elif="{{rating === 2}}">不满意</text>
        <text wx:elif="{{rating === 3}}">一般</text>
        <text wx:elif="{{rating === 4}}">满意</text>
        <text wx:elif="{{rating === 5}}">非常满意</text>
      </view>
    </view>

    <!-- 评价内容 -->
    <view class="comment-section">
      <view class="section-title">评价内容 *</view>
      <textarea
        class="comment-textarea"
        placeholder="请详细描述您的体验，帮助其他用户更好地了解..."
        value="{{comment}}"
        bindinput="onCommentInput"
        maxlength="500"
        auto-height
      ></textarea>
      <view class="char-count">{{comment.length}}/500</view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="cancel-btn" bindtap="cancelRating">取消</button>
      <button
        class="submit-btn {{rating > 0 && comment.trim() ? 'active' : 'disabled'}}"
        bindtap="submitRating"
        disabled="{{submitting}}"
      >
        {{submitting ? '提交中...' : '提交评价'}}
      </button>
    </view>
  </view>
</view>