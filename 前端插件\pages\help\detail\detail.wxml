<!--pages/help/detail/detail.wxml-->
<view class="help-detail-container">

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 详情内容 -->
  <view class="detail-content" wx:if="{{!loading && helpDetail.id}}">

    <!-- 需求基本信息 -->
    <view class="help-info-card">
      <!-- 状态和紧急程度 -->
      <view class="status-bar">
        <view class="status-tag status-{{helpDetail.status}}">
          {{statusMap[helpDetail.status]}}
        </view>
        <view class="urgency-tag urgency-{{helpDetail.urgency}}">
          {{urgencyMap[helpDetail.urgency]}}
        </view>
      </view>

      <!-- 标题 -->
      <view class="help-title">{{helpDetail.title}}</view>

      <!-- 基本信息 -->
      <view class="help-meta">
        <view class="meta-item">
          <text class="iconfont icon-category"></text>
          <text class="meta-text">{{helpDetail.help_type}}</text>
        </view>
        <view class="meta-item">
          <text class="iconfont icon-points"></text>
          <text class="meta-text">{{helpDetail.points_reward}}积分</text>
        </view>
        <view class="meta-item" wx:if="{{helpDetail.location_info}}">
          <text class="iconfont icon-location"></text>
          <text class="meta-text">{{helpDetail.location_info}}</text>
        </view>
        <view class="meta-item">
          <text class="iconfont icon-time"></text>
          <text class="meta-text">{{helpDetail.created_at}}</text>
        </view>
      </view>

      <!-- 发布者信息 -->
      <view class="publisher-info">
        <image class="publisher-avatar" src="{{helpDetail.author_avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="publisher-details">
          <view class="publisher-name">{{helpDetail.author_name}}</view>
          <view class="publisher-stats">
            <text class="stat-item">信誉度: {{helpDetail.author_rating || '暂无'}}</text>
            <text class="stat-item">完成: {{helpDetail.author_completed || 0}}次</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细描述 -->
    <view class="help-description-card">
      <view class="card-title">详细描述</view>
      <view class="description-content">{{helpDetail.content}}</view>

      <!-- 图片展示 -->
      <view class="images-container" wx:if="{{helpDetail.images && helpDetail.images.length > 0}}">
        <view class="images-grid">
          <image
            wx:for="{{helpDetail.images}}"
            wx:key="index"
            class="help-image"
            src="{{item}}"
            mode="aspectFill"
            bindtap="previewImage"
            data-src="{{item}}"
            data-urls="{{helpDetail.images}}"
          ></image>
        </view>
      </view>
    </view>

    <!-- 响应列表 -->
    <view class="responses-card" wx:if="{{responses.length > 0 || userRole === 'requester'}}">
      <view class="card-title">
        <text>响应列表</text>
        <text class="response-count">({{responses.length}})</text>
      </view>

      <!-- 无响应提示 -->
      <view class="no-responses" wx:if="{{responses.length === 0}}">
        <text class="iconfont icon-empty"></text>
        <text class="no-responses-text">暂无响应</text>
      </view>

      <!-- 响应列表 -->
      <view class="response-list" wx:if="{{responses.length > 0}}">
        <view
          class="response-item {{item.status === 'selected' ? 'selected' : ''}}"
          wx:for="{{responses}}"
          wx:key="id"
        >
          <!-- 响应者信息 -->
          <view class="responder-info">
            <image class="responder-avatar" src="{{item.user_avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="responder-details">
              <view class="responder-name">{{item.user_name}}</view>
              <view class="responder-stats">
                <text class="stat-item">信誉度: {{item.user_rating || '暂无'}}</text>
                <text class="stat-item">完成: {{item.user_completed || 0}}次</text>
              </view>
            </view>
            <view class="response-status" wx:if="{{item.status === 'selected'}}">
              <text class="status-selected">已选中</text>
            </view>
          </view>

          <!-- 响应内容 -->
          <view class="response-content">
            <view class="response-text">{{item.content}}</view>
            <view class="response-time">
              <text class="iconfont icon-clock"></text>
              <text>预计完成时间：{{item.estimated_time}}</text>
            </view>
            <view class="response-date">{{item.created_at}}</view>
          </view>

          <!-- 选择按钮（仅发布者可见且状态为pending） -->
          <view class="response-actions" wx:if="{{userRole === 'requester' && helpDetail.status === 'pending' && item.status === 'pending'}}">
            <button
              class="select-btn"
              bindtap="selectHelper"
              data-id="{{item.id}}"
            >
              选择TA
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系信息（仅选中帮助者后显示） -->
    <view class="contact-card" wx:if="{{helpDetail.status === 'in_progress' && helpDetail.selected_helper}}">
      <view class="card-title">联系信息</view>
      <view class="contact-info">
        <view class="contact-item">
          <text class="iconfont icon-user"></text>
          <text>帮助者：{{helpDetail.selected_helper.user_name}}</text>
        </view>
        <view class="contact-item" wx:if="{{helpDetail.contact_info}}">
          <text class="iconfont icon-phone"></text>
          <text>联系方式：{{helpDetail.contact_info}}</text>
        </view>
        <view class="contact-item">
          <text class="iconfont icon-clock"></text>
          <text>预计完成：{{helpDetail.selected_helper.estimated_time}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{!loading && helpDetail.id}}">

    <!-- 访客状态：显示响应按钮 -->
    <view class="action-buttons" wx:if="{{userRole === 'visitor' && helpDetail.status === 'pending'}}">
      <button class="response-btn" bindtap="showResponseModal">
        <text class="iconfont icon-hand"></text>
        <text>我来帮忙</text>
      </button>
    </view>

    <!-- 发布者状态：显示管理按钮 -->
    <view class="action-buttons" wx:if="{{userRole === 'requester'}}">
      <button
        class="cancel-btn"
        wx:if="{{helpDetail.status === 'pending'}}"
        bindtap="cancelRequest"
      >
        取消需求
      </button>
      <button
        class="complete-btn"
        wx:if="{{helpDetail.status === 'in_progress'}}"
        bindtap="completeRequest"
      >
        确认完成
      </button>
    </view>

    <!-- 帮助者状态：显示状态信息 -->
    <view class="action-buttons" wx:if="{{userRole === 'helper'}}">
      <view class="helper-status">
        <text wx:if="{{helpDetail.status === 'pending'}}">等待发布者选择...</text>
        <text wx:if="{{helpDetail.status === 'in_progress'}}">服务进行中，请按时完成</text>
        <text wx:if="{{helpDetail.status === 'completed'}}">服务已完成</text>
      </view>
    </view>
  </view>

  <!-- 响应弹窗 -->
  <view class="modal-overlay" wx:if="{{showResponseModal}}" bindtap="hideResponseModal">
    <view class="response-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">提交响应</text>
        <text class="modal-close" bindtap="hideResponseModal">×</text>
      </view>

      <view class="modal-content">
        <view class="form-group">
          <view class="form-label">响应内容 *</view>
          <textarea
            class="form-textarea"
            placeholder="请描述您的帮助方案、经验或优势..."
            value="{{responseForm.content}}"
            bindinput="onResponseContentChange"
            maxlength="500"
          ></textarea>
          <view class="char-count">{{responseForm.content.length}}/500</view>
        </view>

        <view class="form-group">
          <view class="form-label">预计完成时间 *</view>
          <input
            class="form-input"
            placeholder="如：今天下午、明天上午、本周末等"
            value="{{responseForm.estimated_time}}"
            bindinput="onEstimatedTimeChange"
            maxlength="50"
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="hideResponseModal">取消</button>
        <button class="modal-btn submit-btn" bindtap="submitResponse">提交响应</button>
      </view>
    </view>
  </view>
</view>