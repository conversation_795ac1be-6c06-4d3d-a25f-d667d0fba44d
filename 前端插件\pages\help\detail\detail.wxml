<!--pages/help/detail/detail.wxml-->
<view class="help-detail-container">

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 详情内容 -->
  <view class="detail-content" wx:if="{{!loading && helpDetail.id}}">

    <!-- 需求信息卡片 -->
    <view class="help-info-card">
      <!-- 紧急程度标签和图标 -->
      <view class="urgency-header">
        <view class="urgency-icon">
          <text class="iconfont icon-fire"></text>
        </view>
        <view class="urgency-tag urgency-{{helpDetail.urgency}}">
          {{urgencyMap[helpDetail.urgency]}}
        </view>
      </view>

      <!-- 标题（带emoji） -->
      <view class="help-title">
        <text wx:if="{{helpDetail.urgency === 'urgent'}}">🔥 </text>
        <text wx:elif="{{helpDetail.urgency === 'normal'}}">💡 </text>
        <text wx:else>⏰ </text>
        {{helpDetail.title}}
      </view>

      <!-- 详细描述 -->
      <view class="help-description">{{helpDetail.content}}</view>

      <!-- 需求信息网格 -->
      <view class="help-info-grid">
        <view class="info-item">
          <view class="info-label">位置距离</view>
          <view class="info-value">{{helpDetail.location_info || 'A区3栋'}} • 0.2km</view>
        </view>
        <view class="info-item">
          <view class="info-label">积分奖励</view>
          <view class="info-value points">{{helpDetail.points_reward}}积分</view>
        </view>
        <view class="info-item">
          <view class="info-label">发布时间</view>
          <view class="info-value">{{helpDetail.created_at}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">互助类型</view>
          <view class="info-value">{{helpDetail.help_type}}</view>
        </view>
      </view>

      <!-- 发布者信息 -->
      <view class="publisher-section">
        <view class="publisher-info">
          <view class="publisher-avatar">
            <image wx:if="{{helpDetail.author_avatar}}" src="{{helpDetail.author_avatar}}" mode="aspectFill"></image>
            <text wx:else class="avatar-text">{{helpDetail.author_name.charAt(0)}}</text>
          </view>
          <view class="publisher-details">
            <view class="publisher-name">{{helpDetail.author_name}}</view>
            <view class="publisher-stats">
              <text class="stat-rating">⭐ {{helpDetail.author_rating || '4.8'}}分</text>
              <text class="stat-count">• 发布过{{helpDetail.author_completed || 12}}次需求</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细描述 -->
    <view class="help-description-card">
      <view class="card-title">详细描述</view>
      <view class="description-content">{{helpDetail.content}}</view>

      <!-- 图片展示 -->
      <view class="images-container" wx:if="{{helpDetail.images && helpDetail.images.length > 0}}">
        <view class="images-grid">
          <image
            wx:for="{{helpDetail.images}}"
            wx:key="index"
            class="help-image"
            src="{{item}}"
            mode="aspectFill"
            bindtap="previewImage"
            data-src="{{item}}"
            data-urls="{{helpDetail.images}}"
          ></image>
        </view>
      </view>
    </view>

    <!-- 响应列表 -->
    <view class="responses-card" wx:if="{{responses.length > 0 || userRole === 'requester'}}">
      <view class="section-header">
        <view class="section-indicator"></view>
        <view class="section-title">响应列表 ({{responses.length}}人)</view>
      </view>

      <!-- 无响应提示 -->
      <view class="no-responses" wx:if="{{responses.length === 0}}">
        <text class="iconfont icon-empty"></text>
        <text class="no-responses-text">暂无响应</text>
      </view>

      <!-- 响应列表 -->
      <view class="response-list" wx:if="{{responses.length > 0}}">
        <view
          class="response-item {{item.status === 'selected' ? 'selected' : ''}}"
          wx:for="{{responses}}"
          wx:key="id"
        >
          <!-- 响应者信息头部 -->
          <view class="responder-header">
            <view class="responder-info">
              <view class="responder-avatar">
                <image wx:if="{{item.user_avatar}}" src="{{item.user_avatar}}" mode="aspectFill"></image>
                <text wx:else class="avatar-text">{{item.user_name.charAt(0)}}</text>
              </view>
              <view class="responder-details">
                <view class="responder-name">{{item.user_name}}</view>
                <view class="responder-stats">
                  <text class="stat-rating">⭐ {{item.user_rating || '4.9'}}分</text>
                  <text class="stat-experience">• {{item.user_profession || '专业维修'}} • {{item.user_experience || '5'}}年经验</text>
                </view>
              </view>
            </view>

            <!-- 状态标签或选择按钮 -->
            <view class="response-status" wx:if="{{item.status === 'selected'}}">
              <text class="status-selected">已选择</text>
            </view>
            <button
              wx:elif="{{userRole === 'requester' && helpDetail.status === 'pending'}}"
              class="select-btn"
              bindtap="selectHelper"
              data-id="{{item.id}}"
            >
              选择
            </button>
          </view>

          <!-- 响应内容 -->
          <view class="response-content">
            <view class="response-text">"{{item.content}}"</view>
            <view class="response-time">{{item.created_at}}响应</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 进度跟踪 -->
    <view class="progress-card">
      <view class="section-header">
        <view class="section-indicator"></view>
        <view class="section-title">进度跟踪</view>
      </view>

      <view class="progress-timeline">
        <!-- 步骤1：需求已发布 -->
        <view class="timeline-item completed">
          <view class="timeline-icon">
            <text class="iconfont icon-check"></text>
          </view>
          <view class="timeline-content">
            <view class="timeline-title">需求已发布</view>
            <view class="timeline-time">{{helpDetail.created_at}}</view>
          </view>
        </view>

        <!-- 步骤2：帮助者已选择 -->
        <view class="timeline-item {{helpDetail.status === 'in_progress' || helpDetail.status === 'completed' ? 'completed' : 'pending'}}">
          <view class="timeline-icon">
            <text wx:if="{{helpDetail.status === 'in_progress' || helpDetail.status === 'completed'}}" class="iconfont icon-check"></text>
            <text wx:else>2</text>
          </view>
          <view class="timeline-content">
            <view class="timeline-title">帮助者已选择</view>
            <view class="timeline-time" wx:if="{{helpDetail.selected_helper}}">
              {{helpDetail.selected_helper.selected_at || '2分钟前'}} • {{helpDetail.selected_helper.user_name}}
            </view>
            <view class="timeline-time" wx:else>等待选择帮助者</view>
          </view>
        </view>

        <!-- 步骤3：服务进行中 -->
        <view class="timeline-item {{helpDetail.status === 'in_progress' ? 'current' : helpDetail.status === 'completed' ? 'completed' : 'pending'}}">
          <view class="timeline-icon">
            <text wx:if="{{helpDetail.status === 'completed'}}" class="iconfont icon-check"></text>
            <text wx:else>3</text>
          </view>
          <view class="timeline-content">
            <view class="timeline-title">服务进行中</view>
            <view class="timeline-time">
              <text wx:if="{{helpDetail.status === 'in_progress'}}">正在进行服务</text>
              <text wx:elif="{{helpDetail.status === 'completed'}}">服务已完成</text>
              <text wx:else>等待服务开始</text>
            </view>
          </view>
        </view>

        <!-- 步骤4：完成确认 -->
        <view class="timeline-item {{helpDetail.status === 'completed' ? 'completed' : 'pending'}}">
          <view class="timeline-icon">
            <text wx:if="{{helpDetail.status === 'completed'}}" class="iconfont icon-check"></text>
            <text wx:else>4</text>
          </view>
          <view class="timeline-content">
            <view class="timeline-title">完成确认</view>
            <view class="timeline-time">
              <text wx:if="{{helpDetail.status === 'completed'}}">{{helpDetail.completed_at || '刚刚完成'}}</text>
              <text wx:else>服务完成后确认</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{!loading && helpDetail.id}}">

    <!-- 访客状态：显示响应按钮 -->
    <view class="action-buttons" wx:if="{{userRole === 'visitor' && helpDetail.status === 'pending'}}">
      <button class="response-btn" bindtap="showResponseModal">
        <text class="iconfont icon-hand"></text>
        <text>我来帮忙</text>
      </button>
    </view>

    <!-- 发布者状态：显示管理按钮 -->
    <view class="action-buttons" wx:if="{{userRole === 'requester'}}">
      <!-- 待响应状态 -->
      <button
        class="cancel-btn"
        wx:if="{{helpDetail.status === 'pending'}}"
        bindtap="cancelRequest"
      >
        <text class="iconfont icon-close"></text>
        <text>取消需求</text>
      </button>

      <!-- 进行中状态 -->
      <button
        wx:if="{{helpDetail.status === 'in_progress'}}"
        class="complete-btn"
        bindtap="completeRequest"
      >
        <text class="iconfont icon-check"></text>
        <text>确认完成</text>
      </button>
    </view>

    <!-- 帮助者状态：显示状态信息 -->
    <view class="action-buttons" wx:if="{{userRole === 'helper'}}">
      <view class="helper-status-info">
        <text wx:if="{{helpDetail.status === 'pending'}}">⏳ 等待发布者选择...</text>
        <text wx:if="{{helpDetail.status === 'in_progress'}}">🔧 服务进行中，请按时完成</text>
        <text wx:if="{{helpDetail.status === 'completed'}}">✅ 服务已完成</text>
      </view>
    </view>
  </view>

  <!-- 响应弹窗 -->
  <view class="modal-overlay" wx:if="{{showResponseModal}}" bindtap="hideResponseModal">
    <view class="response-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">提交响应</text>
        <text class="modal-close" bindtap="hideResponseModal">×</text>
      </view>

      <view class="modal-content">
        <view class="form-group">
          <view class="form-label">响应内容 *</view>
          <textarea
            class="form-textarea"
            placeholder="请描述您的帮助方案、经验或优势..."
            value="{{responseForm.content}}"
            bindinput="onResponseContentChange"
            maxlength="500"
          ></textarea>
          <view class="char-count">{{responseForm.content.length}}/500</view>
        </view>

        <view class="form-group">
          <view class="form-label">预计完成时间 *</view>
          <input
            class="form-input"
            placeholder="如：今天下午、明天上午、本周末等"
            value="{{responseForm.estimated_time}}"
            bindinput="onEstimatedTimeChange"
            maxlength="50"
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="hideResponseModal">取消</button>
        <button class="modal-btn submit-btn" bindtap="submitResponse">提交响应</button>
      </view>
    </view>
  </view>
</view>