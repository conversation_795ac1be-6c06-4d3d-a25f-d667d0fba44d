@use "sass:color";

$highlight-color: #04a4cc;
$text-color: #333;
$menu-avatar-frame: #aaa;

@use "../_admin.scss" with (
	$scheme-name: "light",
	$base-color: #e5e5e5,
	$icon-color: #999,
	$text-color: $text-color,
	$highlight-color: $highlight-color,
	$notification-color: #d64e07,

	$body-background: #f5f5f5,

	$menu-highlight-text: #fff,
	$menu-highlight-icon: #ccc,
	$menu-highlight-background: #888,

	$menu-bubble-text: #fff,
	$menu-submenu-background: #fff,

	$menu-collapse-text: #777,
	$menu-collapse-focus-icon: #555,

	$dashboard-accent-1: $highlight-color,
	$dashboard-accent-2: color.adjust(color.adjust($highlight-color, $lightness: 7%), $saturation: -15%),
	$dashboard-icon-background: $text-color
);

/* Override the theme filter highlight color for this scheme */
.theme-section.current,
.theme-filter.current {
	border-bottom-color: admin.$highlight-color;
}
