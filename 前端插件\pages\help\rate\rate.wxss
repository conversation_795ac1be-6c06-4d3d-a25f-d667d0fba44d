/* pages/help/rate/rate.wxss */

.rate-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

/* ==================== 需求信息卡片 ==================== */
.help-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.help-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.help-type {
  font-size: 26rpx;
  color: #666;
}

/* ==================== 用户信息卡片 ==================== */
.user-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.avatar-text {
  font-size: 36rpx;
  color: white;
  font-weight: bold;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 26rpx;
  color: #666;
}

/* ==================== 评分区域 ==================== */
.rating-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  text-align: center;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stars-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.star {
  font-size: 60rpx;
  color: #ddd;
  margin: 0 8rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

.star.active {
  color: #FFB74D;
  transform: scale(1.1);
}

.star-icon {
  font-size: 60rpx;
  line-height: 1;
}

.rating-text {
  font-size: 28rpx;
  color: #666;
  height: 40rpx;
  line-height: 40rpx;
}

/* ==================== 评价内容区域 ==================== */
.comment-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.comment-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
  margin-top: 20rpx;
  background: #fafafa;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

/* ==================== 操作按钮 ==================== */
.action-buttons {
  display: flex;
  gap: 20rpx;
  padding: 0 20rpx 40rpx;
}

.cancel-btn, .submit-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.submit-btn {
  background: #ddd;
  color: #999;
}

.submit-btn.active {
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  color: white;
}

.submit-btn.disabled {
  opacity: 0.6;
}