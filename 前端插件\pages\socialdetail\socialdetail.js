/*
 * 微慕小程序
 * author: jianbo
 * organization:  微慕 www.minapper.com
 * 技术支持微信号：Jianbo
 * Copyright (c) 2018 https://www.minapper.com All rights reserved.
 */

const API = require("../../utils/api.js");
const Auth = require("../../utils/auth.js");
const Adapter = require("../../utils/adapter.js");
const util = require("../../utils/util.js");
const NC = require("../../utils/notificationcenter.js");
import config from "../../utils/config.js";
import { ModalView } from "../../templates/modal-view/modal-view.js";
import Poster from "../../templates/components/wxa-plugin-canvas-poster/poster/poster";
const app = getApp();
const pageCount = 10
let isFocusing = false;
const backgroundAudioManager = wx.getBackgroundAudioManager();
const behavior = require('../../utils/new/behavior.js')

const options = {
  behaviors: [behavior],
  data: {
    parentId: "0",
    shareTitle: "",
    pageTitle: "",
    postId: "",
    topicId: "",

    detail: {},
    commentCounts: 0,

    relatedPostList: [],
    commentsList: [],
    repliesList: [],
    display: false,
    total_replies: 0,

    page: 1,
    isLastPage: false,
    isLoading: false,
    isPull: false,

    toolbarShow: true,
    commentInputDialogShow: false,
    iconBarShow: false,
    menuBackgroup: false,

    focus: false,
    placeholder: "说点什么...",
    toUserId: "",
    toFormId: "",
    commentdate: "",
    content: "",

    dialog: {
      title: "",
      content: "",
      hidden: true
    },
    userSession: {},
    wxLoginInfo: {},
    memberUserInfo: {},
    userInfo: {},

    isLike: false,
    downloadFileDomain: '',
    businessDomain: '',
    logo: app.globalData.appLogo,
    domain: config.getDomain,
    vid: "",
    isPlaying: 0,
    // 当前用户的手机系统
    system: "",
    // isIpx: '',
    popupShow: false,
    pageName: "socialdetail",
    forums: [],
    isShowSubscribe: true,

    insertWxPopupShow: false, // 嵌入公众号弹出层
    appID: "",
    pagePath: "",
    banner: {},
    showPopPhone:false,
    insertChannelsPopupShow:false,

    imgCurrent: 0,
    swiperImgs: [],
    isFixedSwiper: false,
    swiperImgHieght: 300,
    hasWechatInstall: app.globalData.hasWechatInstall
  },

  onLoad: function(option) {
    this.setPageInfo()

    // 页面路径
    this.setData({
      pagePath: `pages/socialdetail/socialdetail?id=${option.id}`,
      postId: option.id,
      topicId: option.id
    });

    var self = this;
    this.getTopicDetail()
    new ModalView();
    let system =/ios/i.test(app.globalData.deviceInfo.system)? 'iOS' : 'Android'
    self.setData({
      system
    });

    backgroundAudioManager.onEnded(() => {
      self.setData({
        isPlaying: 0
      });
    });

    // 设置系统分享菜单
    wx.showShareMenu({
      withShareTicket: false,
      menus: ["shareAppMessage", "shareTimeline"]
    })
    Adapter.getCustomBanner(self,API,'topic_detail_top_nav')
  },

  onShow: function() {
    let self = this;
    Auth.setUserMemberInfoData(self);
    if (this.data.userSession.sessionId) {
      Auth.checkGetMemberUserInfo(this.data.userSession, this, API);
    }
  },

  // 自定义分享朋友圈
  onShareTimeline: function() {
    let self = this
    let video = self.data.detail.videoList
    let poster = video.length ? video[0].poster : ''
    let imageUrl = poster ? poster : this.data.detail.post_full_image

    return {
      title: self.data.detail.title,
      query: {
        id: self.data.topicId
      },
      imageUrl
    };
  },

  onShareAppMessage: function() {
    let video = this.data.detail.videoList;
    let poster = video.length ? video[0].poster : "";
    let imageUrl = poster ? poster : this.data.detail.post_full_image;
    let path= "/pages/socialdetail/socialdetail?id=" + this.data.topicId;
    let invitecode=this.data.memberUserInfo.invitecode;
    if(invitecode)
    {
      path +='&invitecode='+invitecode;
    }
    console.log(path);
    return {
      title: this.data.detail.title,
      path:path,
      imageUrl
    };
  },

  // 监听页面滚动
  onPageScroll: function(res) {
    //this.handleScroll(res)
  },

  handleScroll(res) {
    const scrollTop = res.scrollTop
    const isShowSubscribe = scrollTop < 50
    const isFixedSwiper = scrollTop > 10
    this.setData({
      isFixedSwiper,
      isShowSubscribe
    })
  },

  /**
   * 获取详情数据
   */
  getTopicDetail() {
    Auth.checkSession(app, API, this, "isLoginLater", util)
    const { userSession, topicId } = this.data
    const params = {
      userId: userSession.userId,
      sessionId: userSession.sessionId,
      topicId
    }
    Adapter.loadBBTopic(params, this, API, util)
  },

  swiperImgLoad(e) {
    const { index } = e.currentTarget.dataset
    const { width, height } = e.detail
    this.data.swiperImgs[index] = {
      width,
      height,
      h: 750 * height / width
    }
    let minHeight = 0
    this.data.swiperImgs.map(m => {
      if (!minHeight) {
        minHeight = m.h
      } else if (m.h < minHeight) {
        minHeight = m.h
      }
    })
    // 设置最大高度 600，否则高度最小的图片太高可能导致滑动区域太小
    if (minHeight > 600) minHeight = 600

    this.setData({
      swiperImgHieght: minHeight
    })
  },
  previewImg(e) {
    const { url, urls } = e.currentTarget.dataset
    wx.previewImage({
      current: url,
      urls
    })
  },

  changeImgSwiper(e) {
    const { current } = e.detail
    this.setData({
      imgCurrent: current
    })
  },
 
  // 跳转
  toDetail(e) {
    let { type, appid, url, path } = e.currentTarget.dataset

    if (type === 'apppage') { // 小程序页面         
      wx.navigateTo({
        url: path
      })
    }
    if (type === 'webpage') { // web-view页面
      url = '../webview/webview?url=' + url
      wx.navigateTo({
        url
      })
    }
    if (type === 'miniapp') { // 其他小程序
      // #if MP
      wx.navigateToMiniProgram({
        appId: appid,
        path
      })
      // #elif NATIVE
      wx.showToast({
        title: '暂不支持，请在微信小程序中使用此功能！',
        icon: "none"
      })
      // #endif
    }
  },

  open_link_doc: function() {
    var self = this;
    var url = self.data.detail.fileLink;
    var fileType = self.data.detail.fileType;

    wx.downloadFile({
      url: url,
      success: function(res) {
        const filePath = res.tempFilePath;
        wx.openDocument({
          showMenu:true,
          filePath: filePath,
          fieldType: fileType
        });
      }
    });
  },
  // 打开地图查看位置
  openmap(e) { 
    var latitude = Number(e.currentTarget.dataset.latitude)
    var longitude = Number(e.currentTarget.dataset.longitude)
    var address = e.currentTarget.dataset.address
    var name=e.currentTarget.dataset.location;
    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      scale: 15,
      name: name,
      address: address
    })
  },

  onReady: function() {},

  onPullDownRefresh: function() {

    Auth.checkLogin(this);
    this.setData({
      detail: {},
      commentCounts: 0,

      relatedPostList: [],
      commentsList: [],
      repliesList: [],
      display: false,
      total_replies: 0,

      page: 1,
      isLastPage: false,
      isLoading: false,
      isPull: false,

      toolbarShow: true,
      commentInputDialogShow: false,
      iconBarShow: false,
      menuBackgroup: false,

      focus: false,
      placeholder: "说点什么...",
      toUserId: "",
      toFormId: "",
      commentdate: "",
      content: ""
    });

    var self = this;
    var data = {};
    data.userId = self.data.userSession.userId;
    data.sessionId = self.data.userSession.sessionId;
    data.topicId = self.data.topicId;
    self.setData({ topicId: self.data.topicId });
    Adapter.loadBBTopic(data, self, API, util);
    wx.stopPullDownRefresh();
  },
  onReachBottom: function() {
    let args = {};
    args.userId = this.data.userSession.userId;
    args.sessionId = this.data.userSession.sessionId;
    args.topicId = this.data.topicId;
    args.per_page = pageCount;
    args.page = this.data.page;
    if (!this.data.isLastPage) {
      Adapter.loadReplayTopic(args, this, API);
    }
  },

  // 动态设置页面信息
  setPageInfo() {
    let app = getApp()
    let downloadFileDomain = getApp().globalData.downloadDomain;
    let businessDomain =  getApp().globalData.businessDomain

    this.setData({
      downloadFileDomain,
      businessDomain
    })
  },

  fristOpenComment: function() {
    let args = {};
    args.userId = this.data.userSession.userId;
    args.sessionId = this.data.userSession.sessionId;
    args.topicId = this.data.topicId;
    args.per_page = pageCount;
    args.page = 1;
    this.setData({ repliesList: [] });
    Adapter.loadReplayTopic(args, this, API);
  },

  // 跳转至查看文章详情
  redictDetail: function(e) {
    Adapter.redictDetail(e, "post");
  },
  //--------------------------------------
  removeArticleChange: function() {
    //NC.removeNotification("articleChange", this)
  },
  goHome: function() {
    wx.switchTab({
      url: "../index/index"
    });
  },

  // 发送评论
  sendComment(e) {
    const { userSession, userInfo, detail, parentId } = this.data
    if (!userSession.sessionId) {
      self.setData({ isLoginPopup: true })
    } else {
      const info = {
        sessionid: userSession.sessionId,
        userid: userSession.userId,
        content:  e.detail,
        parentid: parentId,
        name: userInfo.nickName
      }
      Adapter.replyBBTopic(detail.id, info, this, API)
    }
  },
  // 评论成功
  commentSuccess() {
    this.selectComponent('#action-bar').closePopup()
  },

  //回复这个话题
  replySubmit: function(e) {
    var self = this;
    if (!self.data.userSession.sessionId) {
      self.setData({ isLoginPopup: true });
    } else {
      var name = self.data.userInfo.nickName;
      var sessionId = self.data.userSession.sessionId;
      var userId = self.data.userSession.userId;
      var replycontent = e.detail.value.inputComment;
      var topicID = e.detail.value.inputTopicID;

      var parentId = self.data.parentId;
     
      if (replycontent.length === 0) {
        self.setData({
          "dialog.hidden": false,
          "dialog.title": "提示",
          "dialog.content": "回复内容为空"
        });

        return;
      }

      var data = {
        sessionid: sessionId,
        userid: userId,
        content: replycontent,
        parentid: parentId,
        name: name
      };
      Adapter.replyBBTopic(topicID, data, self, API);
      // 隐藏评论框
      this.hiddenBar();
    }
  },

  replay: function(e) {
    var self = this;
    if (self.data.detail.enableComment == "0") {
      return;
    }
    var parentId = e.currentTarget.dataset.id;
    var toUserName = e.currentTarget.dataset.name;
    var toUserId = e.currentTarget.dataset.userid;
    var commentdate = e.currentTarget.dataset.commentdate;
    isFocusing = true;
    self.showToolBar("replay");
    self.setData({
      parentId: parentId,
      placeholder: "回复" + toUserName + ":",
      focus: true,
      toUserId: toUserId,
      commentdate: commentdate
    });
  },

  postLike: function() {
    var self = this;
    var id = self.data.detail.id;
    if (!self.data.userSession.sessionId) {
      self.setData({ isLoginPopup: true });
    } else {
      Adapter.postLike(id, self, app, API, "topicDetail");
    }
  },
  agreeGetUser: function(e) {
    let self = this;
    Auth.checkAgreeGetUser(e, app, self, API, "0");
  },
  onBindBlur: function(e) {
    var self = this;
    if (!isFocusing) {
      {
        const text = e.detail.value.trim();
        if (text === "") {
          self.setData({
            parentID: "0",
            placeholder: "说点什么...",
            userid: "",
            toFromId: "",
            commentdate: ""
          });
        }
      }
    }
  },
  onBindFocus: function(e) {
    var self = this;
    isFocusing = false;
    if (!self.data.focus) {
      self.setData({ focus: true });
    }
  },
  //显示或隐藏评论输入框
  showToolBar: function(e) {
    var self = this;
    var member = self.data.memberUserInfo.member;
    var _member = 10;
    if (member != "00" && member != "01") {
      _member = parseInt(member);
    }
    var min_comment_user_member = self.data.detail.min_comment_user_member;
    var min_comment_user_memberName =
      self.data.detail.min_comment_user_memberName;
    if (member != "00" && member != "01" && _member < min_comment_user_member) {
      if (e != "replay") {

        wx.z.showDialog({
          type: "confirm",
          title: "提示",      
          confirmText: "确认",
          // isCoverView: true,
          content: "权限不足,需"+min_comment_user_memberName+"及以上等级方可发表评论。是否去赚积分提高等级?",
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '../earnIntegral/earnIntegral'
              });
            }
  
            
          }
        })

        // Adapter.toast(
        //   "权限不足,需" +
        //     min_comment_user_memberName +
        //     "及以上等级方可发表评论。",
        //   5000
        // );
      }
    } else {
      let userId = self.data.userSession.userId
      let sessionId = self.data.userSession.sessionId
      if (!userId || !sessionId) {
        self.setData({ isLoginPopup: true })
        return
      }

      self.setData({
        toolbarShow: false,
        commentInputDialogShow: true,
        iconBarShow: false,
        menuBackgroup: !this.data.menuBackgroup,
        focus: true
      });
    }
  },

  //显示或隐藏评论图标工具栏
  showIconBar: function() {
    this.setData({
      toolbarShow: false,
      iconBarShow: true,
      commentInputDialogShow: false,
      menuBackgroup: !this.data.menuBackgroup,
      focus: false
    });
  },
  //点击非评论区隐藏评论输入框或图标栏
  hiddenBar: function() {
    this.setData({
      iconBarShow: false,
      toolbarShow: true,
      menuBackgroup: false,
      commentInputDialogShow: false,
      focus: false
    });
  },

  confirm: function() {
    this.setData({
      "dialog.hidden": true,
      "dialog.title": "",
      "dialog.content": ""
    });
  },
  closeLoginPopup() {
    Auth.logout(this);
    this.setData({ isLoginPopup: false });
  },
  openLoginPopup() {
    this.setData({ isLoginPopup: true });
  },
  postRefresh: function() {
    this.onPullDownRefresh();
    this.hiddenBar();
    Adapter.toast("已刷新", 1500);
  },

  // 嵌入公众号
  insertWxPost() {
    this.getAppId();
    this.hiddenBar();
    this.getPostShortlink();
    this.setData({
      insertWxPopupShow: true
    });
  },
  // 复制小程序链接
  async copyAppLink() {    
    var args = {id: this.data.postId};
    const res = await app.$api.getPostShortlink(args) 
    let shortlink = res.shortlink || []    
    if(shortlink)
    {
      Adapter.copyLink(shortlink, "已复制")
    }
  },

  // 获取appID
  getAppId() {
    var self = this;
    API.getSettings().then(res => {
      self.setData({
        appID: res.settings.appid
      });
    });
  },

  // 复制嵌入信息
  copyInsertInfo(e) {
    let data = e.currentTarget.dataset;
    let id = data.id;
    let path = data.path;
    let shortlink=data.shortlink
    let info = `AppID：${id}，小程序路径：${path}，小程序短链接：${shortlink}`
    

    this.closeInsertWxPopup();
    Adapter.copyLink(info, "复制成功");
  },

  async getPostShortlink() {
    var args = {id: this.data.postId};
    const res = await app.$api.getPostShortlink(args) 
    let shortlink = res.shortlink || []
    this.setData({
      shortlink
    })
  },

  // 关闭嵌入微信弹出
  closeInsertWxPopup() {
    this.setData({
      insertWxPopupShow: false
    });
  },

  copyLink: function() {
    var url = this.data.detail.permalink;
    this.hiddenBar();
    Adapter.copyLink(url, "复制成功");
  },
  gotoWebpage: function() {
    var url = this.data.detail.permalink;
    var enterpriseMinapp = this.data.detail.enterpriseMinapp;
    this.hiddenBar();
    Adapter.gotoWebpage(enterpriseMinapp, url);
  },
  creatPoster: function() {
    var self = this;
    self.hiddenBar();
    Adapter.creatPoster(self, app, API, util, self.modalView, "topic");
  },
  onPosterSuccess(e) {
    const { detail } = e;
    // wx.previewImage({
    //   current: detail,
    //   urls: [detail]
    // })
    this.showModal(detail);
  },
  onPosterFail(err) {
    //console.error(err);
    Adapter.toast(err, 2000);
  },
  onCreatePoster() {
    var self = this;
    if (!self.data.userSession.sessionId) {
      self.setData({ isLoginPopup: true });
    } else {
      Adapter.creatArticlePoster(
        self,
        app,
        API,
        util,
        self.modalView,
        "topic",
        Poster
      );
    }
  },
  showModal: function(posterPath) {
    this.modalView.showModal({
      title: "保存至相册可以分享给好友",
      confirmation: false,
      confirmationText: "",
      inputFields: [
        {
          fieldName: "posterImage",
          fieldType: "Image",
          fieldPlaceHolder: "",
          fieldDatasource: posterPath,
          isRequired: false
        }
      ]
    });
  },
  payment: function() {
    var self = this;
    var enterpriseMinapp = this.data.detail.enterpriseMinapp;
    if (enterpriseMinapp == "1") {
      if (!self.data.userSession.sessionId) {
        self.setData({ isLoginPopup: true });
      } else {
        wx.navigateTo({
          url:
            "../payment/payment?postid=" +
            self.data.topicId +
            "&categoryid=" +
            self.data.detail.categories[0] +
            "&posttitle=" +
            self.data.detail.title.rendered
        });
      }
    } else {
      Adapter.toast("个人主体小程序无法使用此功能", 2000);
    }
  },
  postPraise: function() {
    var self = this;
    var system = self.data.system;
    var enterpriseMinapp = this.data.detail.enterpriseMinapp;
    var authorZanImage = self.data.detail.author_zan_image;
    var praiseimgurl = self.data.detail.praiseimgurl;
    if (authorZanImage != "") {
      wx.previewImage({
        urls: [authorZanImage]
      });
    } else {
      if (system == "iOS") {
        if (praiseimgurl != "") {
          wx.previewImage({
            urls: [praiseimgurl]
          });
        } else if (praiseimgurl == "" && enterpriseMinapp == "1") {
          Adapter.toast("根据相关规定，该功能暂时只支持在安卓手机上使用", 1500);
        } else {
          Adapter.toast("设置错误", 1500);
        }
      } else {
        if (enterpriseMinapp == "1") {
          if (!self.data.userSession.sessionId) {
            self.setData({ isLoginPopup: true });
          } else {
            wx.navigateTo({
              url:
                "../postpraise/postpraise?postid=" +
                self.data.topicId +
                "&touserid=" +
                self.data.userSession.userId +
                "&posttype=post"
            });
          }
        } else if (enterpriseMinapp != "1" && praiseimgurl != "") {
          wx.previewImage({
            urls: [praiseimgurl]
          });
        } else {
          Adapter.toast("设置错误", 1500);
        }
      }
    }

    self.hiddenBar();
  },
  playRemoteAudio: function(e) {
    var self = this;
    var audioUrl = self.data.detail.audioUrl;
    backgroundAudioManager.src = audioUrl;
    backgroundAudioManager.title = "录音";
    self.setData({
      isPlaying: 1
    });
  },
  stopRemoteAudio: function() {
    backgroundAudioManager.stop();
    this.setData({
      isPlaying: 0
    });
  },
  deleteComment: function(e) {
    var self = this;
    var id = e.currentTarget.dataset.id;
    var topicId = self.data.detail.id;
    var data = {};
    var userId = self.data.userSession.userId;
    var sessionId = self.data.userSession.sessionId;
    var repliesList = self.data.repliesList;

    if (!sessionId || !userId) {
      Adapter.toast("请先授权登录", 3000);
      return;
    }
    data.id = id;
    data.userid = userId;
    data.sessionid = sessionId;
    data.topicId = topicId;
    data.deletetype = "publishStatus";
    wx.z.showDialog({
      type: "confirm",
      title: "提示",
      // isCoverView: true,
      confirmText: "确认",   
      content: "确认删除？",
      success: res => {
        if (res.confirm) {
          API.deleteReplyById(data).then(res => {
            if (res.code == "error") {
              wx.showToast({
                title: res.message,
                mask: false,
                icon: "none",
                duration: 3000
              });
            } else {
              // wx.pageScrollTo({
              //     selector : '.entry-title1'

              //   })
              //self.setData({page:1,isLastPage:false,commentsList:[]})
              //self.onReachBottom();

              var hasChild = false;
              repliesList.forEach(element => {
                if (element.id == id && element.child.length > 0) {
                  hasChild = true;
                }
              });
              if (hasChild) {
                self.onPullDownRefresh();
              } else {
                repliesList = repliesList.filter(function(item) {
                  return item["id"] !== id;
                });
                self.setData({ repliesList: repliesList });
              }

              var total_replies = parseInt(self.data.total_replies) - 1;
              self.setData({
                total_replies: total_replies
              });

              wx.showToast({
                title: res.message,
                mask: false,
                icon: "none",
                duration: 3000
              });
            }
          });
        } else if (res.cancel) {
        }
      }
    });
  },
   // a标签跳转和复制链接
   tagATap(e) {
    Adapter.tagATap(e,this,config.getDomain,API,util);
    // let self = this
    // let href = e.detail.src || e.detail.href
    // let domain = config.getDomain
    // let appid = e.detail.appid
    // let redirectype = e.detail.redirectype
    // let path = e.detail.path
    // let feedid = e.detail.feedid
    // let jumptype = e.detail.jumptype

    
    // //判断是否是锚链接
    // let isAnchor=/(#)$/.test(href)
    // if(isAnchor)
    // {
    //   return;
    // }
    // // 判断a标签src里是不是插入的文档链接
    // let isDoc = /\.(doc|docx|xls|xlsx|ppt|pptx|pdf)$/.test(href)
    // if (isDoc) {
    //   this.openLinkDoc(e.detail)
    //   return
    // }

    // if(redirectype) {
    //   if (redirectype == 'apppage') { //跳转到小程序内部页面         
    //     wx.navigateTo({
    //       url: path
    //     })
    //   } else if (redirectype == 'webpage') //跳转到web-view内嵌的页面
    //   {
    //     href = '../webview/webview?url=' +  encodeURIComponent(href);
    //     wx.navigateTo({
    //       url: href
    //     })
    //   }
    //   else if (redirectype == 'miniapp') //跳转其他小程序
    //    {
    //     if(jumptype=='embedded')
    //     {
    //       wx.openEmbeddedMiniProgram({
    //         appId: appid,
    //         path: path,
    //         allowFullScreen:true
    //       })

    //     }
    //     else
    //     {
    //       wx.navigateToMiniProgram({
    //         appId: appid,
    //         path: path
    //       })
    //     }
    //   }
    //   else if ('channelsActivity' == redirectype) //跳转其他小程序
    //    {
    //     var channelsId=getApp().globalData.channelsId;
    //     wx.openChannelsActivity({
    //       finderUserName: channelsId,
    //       feedId: feedid
    //     })
    //   }

    //   return;
    // }

    // let enterpriseMinapp = self.data.detail.enterpriseMinapp;   
    // // 可以在这里进行一些路由处理
    // if (href.indexOf(domain) == -1) { //  域名不是小程序所在网站的域名

    //   var n=0;
    //   for (var i = 0; i < self.data.businessDomain.length; i++) {
  
    //     if (href.indexOf(self.data.businessDomain[i]) != -1) {
    //       n++;
    //       break;
    //     }
    //   }

    //   if(n>0)
    //   {
    //     var url = ''
    //     if (enterpriseMinapp == "1") {
    //       url = '../webview/webview';
    //       wx.navigateTo({
    //         url: url + '?url=' + +  encodeURIComponent(href)
    //       })
    //     }
    //     else {
    //       Adapter.copyLink(href, "复制成功")
    //     }
    //   }
    //   else
    //   {
    //     Adapter.copyLink(href, "复制成功")

    //   }
      
    // } else {
    //   var slug = util.GetUrlFileName(href, domain)
    //   if(slug=="")
    //   {
          
    //       if (enterpriseMinapp == "1") {
    //         url = '../webview/webview';
    //         wx.navigateTo({
    //           url: url + '?url=' +  encodeURIComponent(href)
    //         })
    //       }
    //       else {
    //         Adapter.copyLink(href, "复制成功")
    //       }
    //     return;

    //   }
    //   else {
    //     API.getPostBySlug(slug).then(res => {
    //       if (res.length && res.length > 0) {
    //         var postId = res[0].id
    //         var openLinkCount = wx.getStorageSync('openLinkCount') || 0
    //         if (openLinkCount > 4) {
    //           wx.redirectTo({
    //             url: '../detail/detail?id=' + postId
    //           })
    //         } else {
    //           wx.navigateTo({
    //             url: '../detail/detail?id=' + postId
    //           })
    //           openLinkCount++
    //           wx.setStorageSync('openLinkCount', openLinkCount)
    //         }
    //       } else {            
    //         var url = '../webview/webview'
    //         if (enterpriseMinapp == "1") {
    //           url = '../webview/webview'
    //           wx.navigateTo({
    //             url: url + '?url=' +  encodeURIComponent(href)
    //           })
    //         } else {
    //           Adapter.copyLink(href, "链接已复制")
    //         }
    //       }
    //     })
    //   }
    // }
  },
  // 打开网盘
  openbaidupan(e) { 
    let self = this
    let baiduPancode = e.currentTarget.dataset.baidupancode;
    if(baiduPancode)
    {
      let path='pages/netdisk_share/share?scene='+baiduPancode;
      // #if MP
      wx.navigateToMiniProgram({
        appId: 'wxdcd3d073e47d1742',
        path: path
      })
      // #elif NATIVE
      wx.showToast({
        title: '暂不支持，请在微信小程序中使用此功能！',
        icon: "none"
      })
      // #endif
    }
  },
  // 打开文档
  async openLinkDoc(e) {
    let self = this
    let url
    let fileType

    // 如果是a标签href中插入的文档
    let downloadFileDomain=self.data.downloadFileDomain;
    if(downloadFileDomain)
    {
      const res = await app.$api.getBaseConfig();
      let info = res.settings || {}
      downloadFileDomain=info.downloadfile_domain;
    }
    let src = e.src || e.href
    var n=0;
    for (var i = 0; i < downloadFileDomain.length; i++) {

      if (src.indexOf(downloadFileDomain[i]) != -1) {
        n++;
        break;
      }
    }

    if(n==0)
    {
      self.copyLink(src);
      return;
    }

    let docType
    let isDoc = /\.(doc|docx|xls|xlsx|ppt|pptx|pdf)$/.test(src)

    if (src && isDoc){
      url = src
      fileType = /doc|docx|xls|xlsx|ppt|pptx|pdf$/.exec(src)[0]
    } else {
      url = e.filelink || e.href
      fileType = e.filetype
    }

    wx.downloadFile({
      url: url,
      success: function (res) {
        const filePath = res.tempFilePath
        wx.openDocument({
          showMenu:true,
          filePath: filePath,
          // fieldType: fileType
        })
      }
    })
  },
  onHidePupopTap(e) {
    this.setData({ popupShow: false });
  },
  showPupop(e) {
    var args = {};
    var self = this;
    self.loadBBForums(self);
  },
  // 加载帖子列表
  loadBBForums(appPage) {
    var forums = [];
    var sessionId = appPage.data.userSession.sessionId;
    var userId = appPage.data.userSession.userId;
    appPage.setData({
      forums: []
    });

    var args = {};
    args.userId = userId;
    args.sessionId = sessionId;
    API.getBBForums(args)
      .then(res => {
        if (res.length && res.length > 0) {
          forums = forums.concat(res);
          appPage.setData({
            forums: forums,
            popupShow: true
          });
        } else {
          wx.showToast({
            title: res,
            duration: 1500
          });
        }
      })
    wx.stopPullDownRefresh();
  },
  postsub: function(e) {
    var self = this;
    if (!self.data.userSession.sessionId) {
      Auth.checkSession(app, API, self, "isLoginNow", util);
      return;
    } else {
      var extid = e.currentTarget.dataset.id;
      var subscribetype = "categorySubscribe";
      var subscribemessagesid = e.currentTarget.dataset.subid;
      Adapter.subscribeMessage(
        self,
        subscribetype,
        API,
        subscribemessagesid,
        extid,
        util
      );
    }
  },

  // 评论点赞
  postCommentLike(e) {
    let self = this;

    if (!self.data.userSession.sessionId) {
      Auth.checkSession(app, API, self, "isLoginNow", util);
      return;
    }

    let id = e.currentTarget.dataset.id;
    let extype = "replay";

    let args = {
      id: id,
      extype: extype,
      userid: self.data.userSession.userId,
      sessionid: self.data.userSession.sessionId
    };

    API.commentLike(args).then(res => {
      if (res.success) {
        let list = self.data.repliesList;
        list = list.map(item => {
          let isCur = item.id === id;

          if (isCur && item.likeon === "0") {
            item.likeon = "1";
            item.likecount++;
          } else if (isCur && item.likeon === "1") {
            item.likeon = "0";
            item.likecount--;
          }
          return item;
        });

        self.setData({
          repliesList: list
        });
      } else {
        wx.showToast({
          title: res.message,
          mask: false,
          icon: "none",
          duration: 3000
        });
      }
    });
  },

  // 去用户主页
  goUserDetail(e) {
    let id = e.currentTarget.dataset.id;
    let url = "../author/author?userid=" + id + "&postype=topic";
    wx.navigateTo({
      url: url
    });
  },
  // 预览图片
  previewImage(e) {
    var imgallsrc = e.currentTarget.dataset.imgallsrc
    var imgsrc = e.currentTarget.dataset.imgsrc
    wx.previewImage({
      current: imgsrc,
      urls: imgallsrc
    })
  },
  getPhoneNumber(e){
    Adapter.getPhoneNumber(e,this,API,Auth);
  },
  logoutTap(e){    
    Auth.logout(this);   
    this.onPullDownRefresh();
    this.closeLoginPopup();     
  },
  wxParseToRedict(e){
    var appid=e.currentTarget.dataset.appid;
    var redirectype=e.currentTarget.dataset.redirectype;   
    var path=e.currentTarget.dataset.path;
    var url=e.currentTarget.dataset.url;

    if (redirectype == 'apppage') { //跳转到小程序内部页面         
      wx.navigateTo({
        url: path
      })
    } else if (redirectype == 'webpage') //跳转到web-view内嵌的页面
    {
      url = '../webview/webview?url=' +  encodeURIComponent(url)
      wx.navigateTo({
        url: url
      })
    }
    else if (redirectype == 'miniapp') //跳转其他小程序
    {
      // #if MP
      if (jumptype=='embedded')
      {
        wx.openEmbeddedMiniProgram({
          appId: appid,
          path: path,
          allowFullScreen:true
        })
      }
      else
      {
        wx.navigateToMiniProgram({
          appId: appid,
          path: path
        })
      }
      // #elif NATIVE
      wx.showToast({
        title: '暂不支持，请在微信小程序中使用此功能！',
        icon: "none"
      })
      // #endif
    }
  },
  // 复制嵌入视频号
  copyInsertChannels(e) {
    let data = e.currentTarget.dataset
    let url = data.url
    this.closeInsertChannelsPopup()
    Adapter.copyLink(url, "复制成功")
  },

  // 关闭视频号弹出
  closeInsertChannelsPopup() {
    this.setData({
      insertChannelsPopupShow: false
    })
  },
  // 嵌入公众号
  insertChannelsPost(e) { 
    let userId = this.data.userSession.userId
      let sessionId = this.data.userSession.sessionId
      if (!userId || !sessionId) {
        self.setData({ isLoginPopup: true })
        return
      }  
    var sourceurl=this.data.detail.permalink;
    Adapter.insertChannelsPost(this,API,this.data.detail.id,"pages/socialdetail/socialdetail?id="+this.data.detail.id,"socialdetail",sourceurl,userId,sessionId)
    
  },

  adbinderror(e) {
    // var self = this
    // if (e.detail.errCode) {
    //   self.setData({
    //     listAdsuccess: false
    //   })
    // }
  },

  // dount分享
  dountShare() {
    const data = this.data
    wx.miniapp.shareMiniProgramMessage({
      userName: app.globalData.gh_id,
      path: 'pages/socialdetail/socialdetail?id=' + data.detail.id,
      imagePath: '/images/uploads/logo.png',
      webpageUrl: 'https://m.wolinwarm.com',
      title: data.detail.title,
      description: app.globalData.appDes,
      withShareTicket: false,
      miniprogramType: 0,
      scene: 0,
      success(res) {
        // console.log('分享小程序成功:' + res)
      }
    })
  },

  handleComplain() {
    this.hiddenBar()
    wx.showToast({
      title: '感谢您的举报，我们将立即对内容进行核实删除',
      icon: 'none'
    })
  }
};
//-------------------------------------------------
Page(options);
