/* pages/my-help/my-help.wxss */
.container {
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 筛选器样式 */
.filter-wrapper {
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

/* 互助列表样式 */
.help-list {
  padding: 20rpx;
}

.help-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.help-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
  gap: 12rpx;
}

.urgency-tag {
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
}

.urgency-urgent {
  background-color: #ff4757;
}

.urgency-normal {
  background-color: #ffa502;
}

.urgency-low {
  background-color: #7bed9f;
}

.help-type {
  padding: 6rpx 12rpx;
  background-color: #f1f2f6;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #57606f;
}

.points-reward {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 6rpx 12rpx;
  background-color: #fff3cd;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #856404;
  font-weight: 500;
}

.help-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2f3542;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.help-content {
  font-size: 28rpx;
  color: #57606f;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.help-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.user-info,
.location-info,
.time-info,
.response-count {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #a4b0be;
}

.help-status {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-pending {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-in_progress {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-completed {
  background-color: #e8f5e8;
  color: #388e3c;
}

.status-cancelled {
  background-color: #ffebee;
  color: #d32f2f;
}

.response-status {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-selected {
  background-color: #e8f5e8;
  color: #388e3c;
}

.status-rejected {
  background-color: #ffebee;
  color: #d32f2f;
}

/* 加载更多按钮 */
.load-more {
  padding: 20rpx;
}

/* 发布按钮 */
.publish-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 100;
}

/* 空状态样式 */
.van-empty {
  padding: 80rpx 40rpx;
}

/* 标签页样式调整 */
.van-tabs {
  background-color: #fff;
}

.van-tabs__nav {
  background-color: #fff;
}

.van-tab {
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .help-item {
    padding: 20rpx;
  }
  
  .help-title {
    font-size: 30rpx;
  }
  
  .help-content {
    font-size: 26rpx;
  }
}
