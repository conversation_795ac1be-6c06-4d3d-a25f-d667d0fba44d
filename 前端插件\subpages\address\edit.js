import config from '../../utils/config.js'
const app = getApp()

Component({
    data: {
      info: {},
      userName: '',
      phone: '',
      addr: '',
      checked: false,
      isEdit: false,
      loading: false,
      canChooseAddr: config.enableChooseAddress // 是否可以选择微信地址
    },
  
    methods: {
      onLoad: function (e) {
        if (e.id) {
          this.getAddrInfo(e.id)
          this.setData({
            isEdit: !!e.id
          })
        } else {
          wx.setNavigationBarTitle({
            title: '新增地址',
          })
        }
      },

      onChange(e){
        this.setData({
          checked: e.detail
        })
      },

      onSave() {
        const err = this.validate()
        if (err) {
          wx.showToast({
            title: err,
            icon: 'none'
          })
          return
        }

        let func = this.data.isEdit ? 'submitEdit' : 'submitAdd'

        if (this.data.loading) return
        this.data.loading = true
        this[func]()
      },

      validate() {
        const { userName, phone, addr } = this.data
        if (!userName) return '请输入联系人'
        if (!phone) return '请输入联系电话'
        // if (!/^1[3-9]\d{9}$/.test(phone)) return '联系电话格式不正确'
        if (!addr) return '请输入收件地址'
      },

      async submitAdd() {
        const { userName, phone, addr, checked } = this.data
        let userInfo = wx.getStorageSync('userSession')
        let params = {
          sessionid: userInfo.sessionId,
          userid: userInfo.userId,
          receivename: userName,
          receivephone: phone,
          receiveaddress: addr,
          isdefault: checked
        }
        const res = await app.$api.addAddr(params)
        if (res.code=="error") {
          wx.showToast({
            title: res.message,
             icon: "none",
             duration: 3000,
          })
        } else {
          wx.showToast({
            title: '新增成功',
            icon: "none",
            duration: 3000,
            success: function (res) {
              app.$util.refreshtPrevPage({ isRefresh: true })
              wx.navigateBack()
           }
          })
        }
        this.data.loading = false
      },

      async submitEdit() {
        const { userName, phone, addr, checked } = this.data
        let userInfo = wx.getStorageSync('userSession')
        let params = {
          sessionid: userInfo.sessionId,
          userid: userInfo.userId,
          receivename: userName,
          receivephone: phone,
          receiveaddress: addr,
          isdefault: checked,
          id: this.data.info.ID
        }
        const res = await app.$api.editAddr(params)
        this.data.loading = false

        wx.showToast({
          title: '修改成功',
        })
        app.$util.refreshtPrevPage({ isRefresh: true })
        wx.navigateBack()
      },
      
      onDelete() {
        let userInfo = wx.getStorageSync('userSession')
        wx.showModal({
          title: '提示',
          content: '您确定要删除此地址吗？',
          success: async (res) => {
            if (res.confirm) {
              let id = this.data.info.ID
              const res = await app.$api.deleteAddr({
                id,
                sessionid: userInfo.sessionId,
                userid: userInfo.userId,
              })
              if (app.addr && app.aadr.ID === id) {
                app.addr = ''
              }

              wx.showToast({
                title: '删除成功',
              })
              app.$util.refreshtPrevPage({ isRefresh: true })
              wx.navigateBack()
            }
          }
        })
      },

      onImport() {
        console.log('开始导入微信地址...')

        // 只针对国内主体且具备使用收货地址场景的小程序开放，在小程序管理后台，「开发」-「开发管理」-「接口设置」中自助开通该接口权限
        wx.chooseAddress({
          success: (res) => {
            console.log('微信地址导入成功:', res)

            // 拼接完整地址
            const fullAddr = (res.provinceName || '') + (res.cityName || '') + (res.countyName || '') + (res.detailInfo || '')

            this.setData({
              userName: res.userName || '',
              phone: res.telNumber || '',
              addr: fullAddr,
            })

            // 成功提示
            wx.showToast({
              title: '地址导入成功',
              icon: 'success',
              duration: 2000
            })
          },
          fail: (error) => {
            console.error('微信地址导入失败:', error)

            let errorMsg = '地址导入失败'

            // 根据不同错误类型给出具体提示
            if (error.errMsg) {
              if (error.errMsg.includes('cancel')) {
                // 用户取消，不显示错误提示
                return
              } else if (error.errMsg.includes('auth deny') || error.errMsg.includes('authorize deny')) {
                errorMsg = '请授权使用收货地址功能'
              } else if (error.errMsg.includes('not declared')) {
                errorMsg = '小程序未配置收货地址权限'
              } else if (error.errMsg.includes('no permission')) {
                errorMsg = '小程序暂无收货地址接口权限，请联系管理员'
              } else if (error.errMsg.includes('system error')) {
                errorMsg = '系统错误，请稍后重试'
              } else {
                errorMsg = `导入失败: ${error.errMsg}`
              }
            }

            wx.showModal({
              title: '导入失败',
              content: errorMsg,
              showCancel: false,
              confirmText: '我知道了'
            })
          },
          complete: () => {
            console.log('微信地址导入操作完成')
          }
        })
      },

      async getAddrInfo(id) {
        let userInfo = wx.getStorageSync('userSession')
        let params = {
          sessionid: userInfo.sessionId,
          userid: userInfo.userId,
          id
        }
        const res = await app.$api.getAddrInfo(params)
        console.log(res)
        let info = res || {}        
        this.setData({
          info,
          userName: info.receivename,
          phone: info.receivephone,
          addr: info.receiveaddress,
          checked: info.isdefault === '1'
        })
      },
    }
  
  })