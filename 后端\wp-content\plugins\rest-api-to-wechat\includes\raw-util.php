<?php

//禁止直接访问
if (!defined('ABSPATH')) exit;

class  RAW_Util
{

    public function __construct()
    {
        //$this->rest_api_init();
    }

    //获取文章的第一张图片
    public static  function get_post_content_first_image($post_content)
    {
        if (!$post_content) {
            $the_post        = get_post();
            if (!empty($the_post)) {
                $post_content = $the_post->post_content;
            } else {
                return "";
            }
        }

        preg_match_all('/class=[\'"].*?wp-image-([\d]*)[\'"]/i', $post_content, $matches);
        if ($matches && isset($matches[1]) && isset($matches[1][0])) {
            $image_id = $matches[1][0];
            if ($image_url = self::get_post_image_url($image_id)) {
                return $image_url;
            }
        }

        //此前版本加入了do_shortcode，会导致pc页面的短代码显示不正常，do_shortcode($post_content)
        preg_match_all('|<img.*?src=[\'"](.*?)[\'"].*?>|i', $post_content, $matches);
        if ($matches && isset($matches[1]) && isset($matches[1][0])) {
            return $matches[1][0];
        }
    }

    public  static function get_post_qq_video($content)
    {
        
        preg_match('/https\:\/\/v.qq.com\/x\/(\S*)\/(\S*)\.html/', $content, $matches);
        if ($matches) {
            $vids = $matches[2];
            $content = preg_replace('~<video (.*?)></video>~s', '<iframe frameborder="0" src="https://v.qq.com/txp/iframe/player.html?vid=' . $vids . '" allowFullScreen="true" width="100%" height="500px"></iframe>', $content);

            // $videoUrl = self::get_qq_video_url($vids);
            // if (empty($videoUrl)) {
            //     $vcontent = preg_replace('~<video (.*?)></video>~s', '<iframe frameborder="0" src="https://v.qq.com/txp/iframe/player.html?vid=' . $vids . '" allowFullScreen="true" width="100%" height="500px"></iframe>', $content);
            // } else {
            //     $vcontent = preg_replace('~<video (.*?)></video>~s', '<video src="' . $videoUrl . '" poster="https://puui.qpic.cn/qqvideo_ori/0/' . $vids . '_496_280/0" controls="controls" width="100%"></video>', $content);
            // }
        }

        return $content;
    }

    // public  static function get_post_qq_video($content)
    // {
    //     $vcontent ='';
    //     preg_match('/https\:\/\/v.qq.com\/x\/(\S*)\/(\S*)\.html/',$content,$matches);
    //     if($matches)
    //     {
    //     	$vids=$matches[2];
    // 	    //$url='http://vv.video.qq.com/getinfo?vid='.$vids.'&defaultfmt=auto&otype=json&platform=1&defn=fhd&charge=0';
    // 	    //  defaultfmt： 1080P-fhd，超清-shd，高清-hd，标清-sd
    // 	    $url='http://vv.video.qq.com/getinfo?vid='.$vids.'&defaultfmt=auto&otype=json&platform=11001&defn=fhd&charge=0';
    // 	    //$res = file_get_contents($url);
    //         $res = self::https_request($url);
    // 	    if($res)
    // 	    {
    // 	    	$str = substr($res,13,-1);
    // 		    $newStr =json_decode($str,true);	    
    // 		    //$videoUrl= $newStr['vl']['vi'][0]['ul']['ui'][2]['url'].$newStr['vl']['vi'][0]['fn'].'?vkey='.$newStr['vl']['vi'][0]['fvkey']; 
    // 		    $videoUrl= $newStr['vl']['vi'][0]['ul']['ui'][0]['url'].$newStr['vl']['vi'][0]['fn'].'?vkey='.$newStr['vl']['vi'][0]['fvkey']; 
    // 		    $vcontent = preg_replace('~<video (.*?)></video>~s','<video src="'.$videoUrl.'" poster="https://puui.qpic.cn/qqvideo_ori/0/'.$vids.'_496_280/0" controls="controls" width="100%"></video>',$content);

    //         }	    

    //     }

    //     return $vcontent;
    // }

    public static function get_qq_video_url($vid)
    {
        $url = 'https://vv.video.qq.com/getinfo?vids=' . $vid . '&platform=101001&charge=0&otype=json';
        $json = file_get_contents($url);
        preg_match('/^QZOutputJson=(.*?);$/', $json, $json2);
        $tempStr = json_decode($json2[1], true);
        $vurl = "";
        if (!empty($tempStr['vl'])) {
            $vurl = 'https://ugcws.video.gtimg.com/' . $tempStr['vl']['vi'][0]['fn'] . "?vkey=" . $tempStr['vl']['vi'][0]['fvkey'];
        }

        return $vurl;
    }

    public static  function get_post_content_images($post_content)
    {
        if (!$post_content) {
            $the_post       = get_post();
            $post_content   = empty($the_post->post_content) ? '' : $the_post->post_content;
        }
        preg_match_all('|<img.*?src=[\'"](.*?)[\'"].*?>|i', do_shortcode($post_content), $matches);
        $images = array();
        if ($matches && isset($matches[1])) {

            for ($i = 0; $i < count($matches[1]); $i++) {
                if ($i > 8) {
                    break;
                }
                $imageurl['imagesurl'] = $matches[1][$i];
                $imageurl['id'] = 'image' . $i;
                $images[] = $imageurl;
            }

            //return $images;

        }

        return $images;
    }

    public static  function get_post_content_audio($post_content)
    {
        if (!$post_content) {
            $the_post       = get_post();
            $post_content   = $the_post->post_content;
        }
        $list = array();
        $c1 = preg_match_all('/<audio\s.*?>/', do_shortcode($post_content), $m1);  //先取出所有img标签文本  
        for ($i = 0; $i < $c1; $i++) {    //对所有的img标签进行取属性  
            $c2 = preg_match_all('/(\w+)\s*=\s*(?:(?:(["\'])(.*?)(?=\2))|([^\/\s]*))/', $m1[0][$i], $m2);   //匹配出所有的属性  
            for ($j = 0; $j < $c2; $j++) {    //将匹配完的结果进行结构重组  
                $list[$i][$m2[1][$j]] = !empty($m2[4][$j]) ? $m2[4][$j] : $m2[3][$j];
            }
        }
        return $list;
    }

    public  static function set_video_pre_ad_id($content, $preMovieAdId)
    {

        $content = preg_replace('/<video ([^]]+)\><\/video>/i', '<video $1 adunitid="' . $preMovieAdId . '" /></video>', $content);

        return $content;
    }

    public static  function get_topic_content_video($post_content)
    {
        $list = array();
        $c1 = preg_match_all('/<video\s.*?>/', do_shortcode($post_content), $m1);  //先取出所有video标签文本  
        for ($i = 0; $i < $c1; $i++) {
            $c2 = preg_match_all('/(\w+)\s*=\s*(?:(?:(["\'])(.*?)(?=\2))|([^\/\s]*))/', $m1[0][$i], $m2);   //匹配出所有的属性  
            for ($j = 0; $j < $c2; $j++) {    //将匹配完的结果进行结构重组  
                $list[$i][$m2[1][$j]] = !empty($m2[4][$j]) ? $m2[4][$j] : $m2[3][$j];
            }
        }

        $_list = array();
        foreach ($list as $item) {
            if (strpos($item['src'], 'qq.com') !== false) {
                $item['filetype'] = "qqvideo";
            } else {
                $item['filetype'] = "noqqvideo";
            }
            $_list[] = $item;
        }

        return $_list;
    }

    public static function get_content_minappershortcode($content)
    {

        $content = preg_replace('/\[minappershortcode ([^]]+)\]/i', '<minappershortcode $1/>', $content);
        $content = preg_replace('/\[minapperad ([^]]+)\]/i', '<minapperad $1/>', $content);
        $content = preg_replace('/\[minapperqqvideo ([^]]+)\]/i', '<minapperqqvideo $1/>', $content);
        $content = preg_replace('/\[minappermap ([^]]+)\]/i', '<minappermap $1/>', $content);
        $content = preg_replace('/\[wechatshopproduct ([^]]+)\]/i', '<wechatshopproduct $1>', $content);
        return  $content;
    }

    public static  function get_content_gallery($content, $flag, $style)
    {
        $list = array();
        //$content=self::nl2p($content,true,false);//把换行转换成p标签
        if ($flag) {
            $content = nl2br($content);
        }
        //$vcontent=$content;

        $c1 = preg_match_all('|\[gallery.*?ids=[\'"](.*?)[\'"].*?\]|i', $content, $m1);  //先取出所有gallery短代码
        for ($i = 0; $i < $c1; $i++) {    //对所有的img标签进行取属性  
            $c2 = preg_match_all('/(\w+)\s*=\s*(?:(?:(["\'])(.*?)(?=\2))|([^\/\s]*))/', $m1[0][$i], $m2);   //匹配出所有的属性  
            for ($j = 0; $j < $c2; $j++) {    //将匹配完的结果进行结构重组  
                $list[$i][$m2[1][$j]] = !empty($m2[4][$j]) ? $m2[4][$j] : $m2[3][$j];
            }
        }

        $ids = $list[0]['ids'];
        $galleryStyle = get_option("raw_gallery_style");
        if (!empty($ids)) {

            $ids = explode(',', $ids);
            $img = '';
            $realwidth = '';
            $real_height = '';
            $i = 0;
            foreach ($ids as $id) {
                $image = wp_get_attachment_image_src((int)$id, 'full');
                if ($style && $galleryStyle == 'swiper') {
                    $img = $i == 0 ? $img . $image[0] : $img . ',' . $image[0];
                    $realwidth = $i == 0 ? $realwidth . $image[1] : $realwidth . ',' . $image[1];
                    $real_height = $i == 0 ? $real_height . $image[2] : $real_height . ',' . $image[2];
                    $i++;
                } else {
                    $img .= '<img width="' . $image[1] . '" height="' . $image[2] . '" src="' . $image[0] . '" /><br/>';
                }
            }

            if ($style && $galleryStyle == 'swiper') {
                $minappergallery = '<minappergallery images="' . $img . '"  real-width="' . $realwidth . '"  real_height="' . $real_height . '">';
                $vcontent = preg_replace('~\[gallery (.*?)\]~s', $minappergallery, $content);
            } else {
                $vcontent = preg_replace('~\[gallery (.*?)\]~s', $img, $content);
            }
        }
        //$vcontent =preg_replace('/\[([^]]+)\]/i', '<$1>', $vcontent);
        //$vcontent =self::get_content_minappershortcode($vcontent);
        return $vcontent;
    }

    public static  function get_content_playlist($content, $flag)
    {
        $list = array();
        //$content=self::nl2p($content,true,false);//把换行转换成p标签
        if ($flag) {
            $content = nl2br($content);
        }
        $c1 = preg_match_all('|\[playlist.*?ids=[\'"](.*?)[\'"].*?\]|i', $content, $m1);  //先取出所有gallery短代码
        for ($i = 0; $i < $c1; $i++) {    //对所有的img标签进行取属性  
            $c2 = preg_match_all('/(\w+)\s*=\s*(?:(?:(["\'])(.*?)(?=\2))|([^\/\s]*))/', $m1[0][$i], $m2);   //匹配出所有的属性  
            for ($j = 0; $j < $c2; $j++) {    //将匹配完的结果进行结构重组  
                $list[$i][$m2[1][$j]] = !empty($m2[4][$j]) ? $m2[4][$j] : $m2[3][$j];
            }
        }
        $audios='';        
        $ids = $list[0]['ids'];
        if (!empty($ids)) {
            $ids = explode(',', $ids);           
            $i = 0;         
            foreach ($ids as $id) {
                $args = array(
                    'post_type' => 'attachment',
                    'post_mime_type' => 'audio', // 只查询音频文件
                    'p' => $id,
                    'posts_per_page' => 1, // 只查询一个文件
                );

                $src='';
                $name='';
                $poster='';
                $author='';
                $query = new WP_Query($args);
                if ($query->have_posts()) {
                    // 获取第一个匹配的音频文件
                    $query->the_post();                   
                    $post = get_post(get_the_id());
                    $src= $post->guid;
                    $name= $post->post_title;
                    $audioMatedata=wp_get_attachment_metadata(get_the_id());  
                    $poster=$audioMatedata['album']; 
                    $author=$audioMatedata['artist']; 

                }
                
                $audio = '<audio   author="' . $author . '" name="' . $name . '" poster="' . $poster . '" src="' . $src . '"  controls="controls"></audio>';
                $audios.= $audio;          
            }
            $vcontent = preg_replace('~\[playlist (.*?)\]~s', $audios, $content);  
        }
        return $vcontent;
    }

    public static  function get_content_singlegoods($content)
    {
        $list = array();
        $singlegoods = array();

        $c1 = preg_match_all('|\[minappergoods.*?id=[\'"](.*?)[\'"].*?\]|i', $content, $m1);  //先取出所有minappergoods短代码
        for ($i = 0; $i < $c1; $i++) {    //对所有的img标签进行取属性  
            $c2 = preg_match_all('/(\w+)\s*=\s*(?:(?:(["\'])(.*?)(?=\2))|([^\/\s]*))/', $m1[0][$i], $m2);   //匹配出所有的属性  
            for ($j = 0; $j < $c2; $j++) {    //将匹配完的结果进行结构重组  
                $list[$i][$m2[1][$j]] = !empty($m2[4][$j]) ? $m2[4][$j] : $m2[3][$j];
            }
        }
        if (!empty($list)) {
            foreach ($list as $l) {
                if (!empty($l['id'])) {
                    $data = array(
                        "product_id" => $l['id']

                    );
                    $product = RAW()->wxapi->get_product($data);
                    if ($product['errcode'] == 0) {
                        $goods = array();
                        $data = $product['data'];
                        $spu = $data['spu'];
                        $sku = $product['sku'];
                        $sale_price = ((int)$sku['sale_price']) / 100;
                        $goods['sale_price'] = $sale_price;
                        $goods['id'] = $l['id'];
                        $goods['path'] = $spu['path'];
                        $goods['poster'] = $spu['head_img'];
                        $goods['title'] = $spu['title'];
                        $singlegoods[] = $goods;
                    }
                }
            }
        }
        return $singlegoods;
    }

    public static  function get_content_minappershopsgoods($content)
    {
        $list = array();
        $minappershopsgoods = array();

        if (!function_exists('MinapperShop')) {
            return  $minappershopsgoods;
        }

        $c1 = preg_match_all('|\[minappershopsgoods.*?id=[\'"](.*?)[\'"].*?\]|i', $content, $m1);  //先取出所有minappergoods短代码
        for ($i = 0; $i < $c1; $i++) {    //对所有的img标签进行取属性  
            $c2 = preg_match_all('/(\w+)\s*=\s*(?:(?:(["\'])(.*?)(?=\2))|([^\/\s]*))/', $m1[0][$i], $m2);   //匹配出所有的属性  
            for ($j = 0; $j < $c2; $j++) {    //将匹配完的结果进行结构重组  
                $list[$i][$m2[1][$j]] = !empty($m2[4][$j]) ? $m2[4][$j] : $m2[3][$j];
            }
        }
        if (!empty($list)) {
            foreach ($list as $l) {
                if (!empty($l['id'])) {
                    $post = Minapper_Shop_Util::get_minapper_shop_goods_byid($l['id'], 0);
                    if (!empty($post)) {
                        $goods = array();
                        $specification = $post->specifications[0];
                        $paytype = isset($specification['paytype']) ? $specification['paytype'] : '';
                        $goods['paytype'] = $paytype;
                        $payintegral = isset($specification['payintegral']['concessionalIntegral']) ? $specification['payintegral']['concessionalIntegral'] : '0';
                        $paymoney = isset($specification['paymoney']['concessionalPrice']) ? $specification['paymoney']['concessionalPrice'] : '0';
                        $goods['payintegral'] = $payintegral;
                        $goods['paymoney'] = $paymoney;
                        $goods['id'] = $post->ID;
                        $goods['path'] = "/subpages/points/goods-detail?id=" . $post->ID;
                        $goods['poster'] = $post->goodsthumbnail;
                        $goods['title'] = $post->post_title;
                        $minappershopsgoods[] = $goods;
                    }
                }
            }
        }
        return $minappershopsgoods;
    }

    public static  function nl2p($string, $line_breaks = true, $xml = true)
    {
        // Remove existing HTML formatting to avoid double-wrapping things
        $string = str_replace(array('<p>', '</p>', '<br>', '<br />'), '', $string);

        // It is conceivable that people might still want single line-breaks
        // without breaking into a new paragraph.
        if ($line_breaks == true)
            return '<p>' . preg_replace(array("/([\n]{2,})/i", "/([^>])\n([^<])/i"), array("</p>\n<p>", '<br' . ($xml == true ? ' /' : '') . '>'), trim($string)) . '</p>';
        else
            return '<p>' . preg_replace("/([\n]{1,})/i", "</p>\n<p>", trim($string)) . '</p>';
    }

    public static  function get_topic_content_qq_video($post_content)
    {
        $list = array();
        $c1 = preg_match_all('/<iframe\s.*?>/', do_shortcode($post_content), $m1);  //先取出所有iframe标签文本  
        for ($i = 0; $i < $c1; $i++) {    //对所有的img标签进行取属性  
            $c2 = preg_match_all('/(\w+)\s*=\s*(?:(?:(["\'])(.*?)(?=\2))|([^\/\s]*))/', $m1[0][$i], $m2);   //匹配出所有的属性  
            for ($j = 0; $j < $c2; $j++) {    //将匹配完的结果进行结构重组  
                $list[$i][$m2[1][$j]] = !empty($m2[4][$j]) ? $m2[4][$j] : $m2[3][$j];
            }
        }

        return $list;
    }

    public static  function get_video_url($video)
    {
        $url = "";
        $videoType = "";
        if (strpos($video['src'], 'v.douyin.com')) {
            $url = $video['src'];
            $videoType = "douyin";
        }
        if (strpos($video['src'], 'm.weibo.cn')) {
            $url = $video['src'];
            $videoType = "weibo";
        }
        $_video['url'] = $url;
        $_video['videoType'] = $videoType;
        return $_video;
    }

    public  static function get_weibo($url)
    {
        $html = minapper_file_get_html($url);
        $text = '/\"text\"\:\ "(.*?)\"\,/';
        $video = '/\"stream_url_hd\"\: \"(.*?)\"/';
        $title = '';
        if (preg_match($text, $html, $t)) {
            $title = wp_filter_nohtml_kses($t[1]);
        }
        $content = $title;
        $cover = "";
        $videourl = "";
        $images = "";

        if (preg_match($video, $html, $v)) {
            $image = '/\"url\"\: \"(.*?)\"\,/';
            if (preg_match($image, $html, $i)) {
                $cover = $i[1];
            }
            $videourl = $v[1];
        } else {
            $image = '/\"url\"\: \"(.*?)\"\,\s+\"geo/';
            if (preg_match_all($image, $html, $m)) {
                $images = $m[1];
            }
        }
        $html->clear();
        $result['title'] = $title;
        $result['content'] = $content;
        $result['cover'] = $cover;
        $result['videourl'] = $videourl;
        $result['images'] = $images;

        return $result;
    }

    public  static function get_toutiao($url)
    {
        $cookie_jar = dirname(__FILE__) . '/tmp.txt'; //tempnam('./tmp','cookie');
        $contentdata = self::get_toutiao_content($url, $cookie_jar);
        $page = minapper_str_get_html($contentdata);

        // $content = "/content\: \'(.*?)\'\./";
        // $title ="/title\: \'(.*?)\'\./";
        // $cover="/coverImg\: \'(.*?)\'/";

        // $article = '';
        // if(preg_match($content, $html, $c)) {
        //     $content=$c[1];
        //     $content = preg_replace("/\\\\u([0-9a-f]{3,4})/i", "&#x\\1;", $content);
        //     $content =html_entity_decode($content, null, 'UTF-8');
        //     $content=htmlspecialchars_decode($content);
        // }
        // if(preg_match($title, $html, $t)) {
        //     $title = htmlspecialchars_decode($t[1]);
        // }

        // if(preg_match($cover, $html, $v)) {
        //     $cover = $v[1];
        // }

        // $html->clear();
        // $result['title']=$title;
        // $result['content']=$content;
        // $result['cover']=$cover;
        // $result['videourl']="";
        // $result['images']="";


        return $page;
    }


    public static function get_toutiao_content($url, $cookie, $referfer = '')
    {

        $useragent = "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; SV1; .NET CLR 1.1.4322; .NET CLR 2.0.50727)";

        if ($referfer == '') {
            $referfer = 'https://www.minapper.com';
        }
        $header = array("Referer: " . $referfer);
        $curl = curl_init(); //初始化curl模块 
        curl_setopt($curl, CURLOPT_URL, $url); //登录提交的地址 
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); //不验证证书
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); //不验证证书
        curl_setopt($curl, CURLOPT_HEADER, 1); //是否显示头信息 
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); //是否自动显示返回的信息 
        curl_setopt($curl, CURLOPT_COOKIEFILE, $cookie); //设置Cookie信息保存在指定的文件中 
        curl_setopt($curl, CURLOPT_COOKIEJAR, $cookie); //设置Cookie信息保存在指定的文件中 
        curl_setopt($curl, CURLOPT_USERAGENT, $useragent);

        $data = curl_exec($curl); //执行cURL 
        //   $ret = $data;
        //   list($header, $data) = explode("\r\n\r\n", $data, 2);
        //   $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        //   $last_url = curl_getinfo($curl, CURLINFO_EFFECTIVE_URL);

        if (curl_errno($curl)) {
            return 'ERROR';
            curl_close($curl);
        }
        curl_close($curl); //关闭cURL资源，并且释放系统资源 
        return $data;


        //   if ($http_code == 301 || $http_code == 302) {
        //         $matches = array();
        //         preg_match('/Location:(.*?)\n/', $header, $matches);
        //         $url = @parse_url(trim(array_pop($matches)));
        //         if (!$url) {
        //               $curl_loops = 0;
        //               return $data;
        //         }
        //         $new_url = $url['scheme'] . '://' . $url['host'] . $url['path']
        //               . (isset($url['query']) ? '?' . $url['query'] : '');
        //         $new_url = stripslashes($new_url);
        //         return self::get_douyin_content($new_url,$cookie);
        //     } else {
        //         $curl_loops = 0;
        //         list($header, $data) = explode("\r\n\r\n", $ret, 2);
        //         return $data;
        //     }
    }

    public  static function get_weixin($url)
    {
        $html = minapper_file_get_html($url);
        $title = $html->find('#activity-name');
        $content = $html->find('#js_content');

        $html->clear();
        $result['title'] = $html;
        $result['content'] = $content;
        $result['cover'] = "";
        $result['videourl'] = "";
        $result['images'] = "";


        return $result;
    }

    //参考 https://www.cnblogs.com/seller/p/12696496.html
    public static function  get_douyin_video($url)
    {
        $cookie_jar = dirname(__FILE__) . '/tmp.txt'; //tempnam('./tmp','cookie');
        $contentdata = self::get_douyin_content($url, $cookie_jar);

        if ($contentdata == "ERROR") {
            return 'ERROR';
        }
        $page = minapper_str_get_html($contentdata);
        $data = array(
            'base' => array(
                'headimg' => false, // 头像
                'name' => false, // 昵称
                'title' => false, // 标题（姑且叫标题吧）
                'description' => false // 描述
            ),
            'video' => array(
                'cover' => false, // 封面
                'src' => false, // 路径
                'width' => false, // 宽度
                'height' => false // 高度
            )
        );
        $user = $page->find('div[class=user-info]');
        // 头像、 昵称
        if (count($user) > 0) {
            $img = $user[0]->find('div[class=avatar]');
            if (count($img) > 0) {
                $img = $img[0]->find('img');
                if (count($img) > 0) {
                    // 头像
                    $data['base']['headimg'] = $img[0]->src;
                    // 昵称
                    $data['base']['name'] = $img[0]->alt;
                }
            }
        }
        // 标题、描述
        $title = $page->find('p[class=desc]');
        //$data['base']['title'] = $title;
        if (count($title) > 0) {
            $title = $title[0]->innertext;
            //    $title = $title[0]->first_child()->first_child();
            //    $data['base']['title'] = $title;
            //     $data['base']['description'] = $title->innertext;
        }

        $video = $page->find('div[id=pageletReflowVideo]');
        if (count($video) > 0) {
            $script = $video[0]->next_sibling();
            if (!empty($script)) {
                $script = $script->next_sibling();
                if (!empty($script)) {
                    $script = $script->next_sibling()->innertext;
                    $data['video'] = self::get_douyin_video_src($script);
                }
            }
        }

        $returnData['title'] = $title;
        $returnData['desc'] = $title;
        $returnData['cover'] = $data['video']['cover'];
        $returnData['videourl'] = $data['video']['src'];
        $returnData['width'] = $data['video']['width'];
        $returnData['height'] = $data['video']['height'];
        // $returnData['contentdata']=$title ;             
        return $returnData;
    }

    public static function get_douyin_video_src($scripts)
    {
        $video = array();
        $scripts = preg_replace('/\s+/', '', $scripts);
        // 宽度
        preg_match('/videoWidth:([0-9.]*),/', $scripts, $matches);
        if (empty($matches) || count($matches) < 2) {
            $video['width'] = false;
        } else {
            $video['width'] = $matches[1];
        }
        // 高度
        preg_match('/videoHeight:([0-9.]*),/', $scripts, $matches);
        if (empty($matches) || count($matches) < 2) {
            $video['height'] = false;
        } else {
            $video['height'] = $matches[1];
        }
        // 视频路径
        preg_match('/playAddr:"(.*)",/', $scripts, $matches);
        if (empty($matches) || count($matches) < 2) {
            $video['src'] = false;
        } else {
            $video['src'] = $matches[1];
        }
        // 封面
        preg_match('/cover:"(.*)"}/', $scripts, $matches);
        if (empty($matches) || count($matches) < 2) {
            $video['cover'] = false;
        } else {
            $video['cover'] = $matches[1];
        }
        return $video;
    }





    public static function get_douyin_content($url, $cookie, $referfer = '')
    {

        $useragent = "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; SV1; .NET CLR 1.1.4322; .NET CLR 2.0.50727)";

        if ($referfer == '') {
            $referfer = 'https://www.minapper.com';
        }
        $header = array("Referer: " . $referfer);
        $curl = curl_init(); //初始化curl模块 
        curl_setopt($curl, CURLOPT_URL, $url); //登录提交的地址 
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); //不验证证书
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); //不验证证书
        curl_setopt($curl, CURLOPT_HEADER, 1); //是否显示头信息 
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); //是否自动显示返回的信息 
        curl_setopt($curl, CURLOPT_COOKIEFILE, $cookie); //设置Cookie信息保存在指定的文件中 
        curl_setopt($curl, CURLOPT_COOKIEJAR, $cookie); //设置Cookie信息保存在指定的文件中 


        curl_setopt($curl, CURLOPT_USERAGENT, $useragent);

        $data = curl_exec($curl); //执行cURL 
        $ret = $data;
        list($header, $data) = explode("\r\n\r\n", $data, 2);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $last_url = curl_getinfo($curl, CURLINFO_EFFECTIVE_URL);

        if (curl_errno($curl)) {
            return 'ERROR';
            curl_close($curl);
        }
        curl_close($curl); //关闭cURL资源，并且释放系统资源 



        if ($http_code == 301 || $http_code == 302) {
            $matches = array();
            preg_match('/Location:(.*?)\n/', $header, $matches);
            $url = @parse_url(trim(array_pop($matches)));
            if (!$url) {
                $curl_loops = 0;
                return $data;
            }
            $new_url = $url['scheme'] . '://' . $url['host'] . $url['path']
                . (isset($url['query']) ? '?' . $url['query'] : '');
            $new_url = stripslashes($new_url);
            return self::get_douyin_content($new_url, $cookie);
        } else {
            $curl_loops = 0;
            list($header, $data) = explode("\r\n\r\n", $ret, 2);
            return $data;
        }
    }
    // public static function  get_douyin_video($url)
    // {   

    //     $headers = get_headers($url, TRUE);
    //     $header = [
    //         ':authority:www.iesdouyin.com',
    //         ':method:GET',
    //         ':path:' . str_replace('https://www.iesdouyin.com', '', $headers['Location']),
    //         ':scheme:https',
    //         'accept:text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
    //         'accept-encoding:deflate, br',
    //         'accept-language:zh-CN,zh;q=0.9',
    //         'cache-control:max-age=0',
    //         'upgrade-insecure-requests:1',
    //         'user-agent:Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.84 Safari/537.36'
    //     ];
    //     $curl = curl_init();
    //     curl_setopt($curl, CURLOPT_URL, $headers['Location']);
    //     curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    //     curl_setopt($curl, CURLOPT_HEADER, 0);
    //     curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    //     curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    //     curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    //     $data = curl_exec($curl); 
    //     if (curl_errno($curl)){
    //         return 'ERROR';
    //         curl_close($curl);
    //     }
    //     curl_close($curl);

    //     $_videourl = '/playAddr\: \"(.*?)\"/';
    //     $_cover = '/cover\: \"(.*?)\"/';
    //     $_desc = '/\<p class=\"desc\">(.*?)\</';
    //     $cover="";
    //     $desc="";
    //     $videourl="";
    //     if (preg_match($_desc, $data, $d)) {				
    //         $desc = $d[1]; 
    //     }              		
    //     if (preg_match($_cover, $data, $c)) {
    //         $cover = $c[1]; 
    //     }
    //     if (preg_match($_videourl, $data, $v)) {
    //         $videourl=  $v[1];
    //     }    
    //     $result['desc']=$desc;
    //     $result['cover']=$cover;
    //     $result['videourl']=$videourl;

    //     return $data;       
    // }

    public static  function getBetween($content, $start, $end)
    {
        $r = explode($start, $content);
        if (isset($r[1])) {
            $r = explode($end, $r[1]);
            return $r[0];
        }
        return '';
    }

    public static function getrealurl($url)
    {
        $header = get_headers($url, 1);
        if (strpos($header[0], '301') || strpos($header[0], '302')) {
            if (is_array($header['Location'])) {
                return $header['Location'][count($header['Location']) - 1];
            } else {
                return $header['Location'];
            }
        } else {
            return $url;
        }
    }


    public static  function get_post_content_images_src($post_content)
    {

        if (!$post_content) {
            $the_post       = get_post();
            $post_content   = $the_post->post_content;
        }
        preg_match_all('|<img.*?src=[\'"](.*?)[\'"].*?>|i', do_shortcode($post_content), $matches);
        $images = array();
        if ($matches && isset($matches[1])) {
            $_images = $matches[1];

            for ($i = 0; $i < count($matches[1]); $i++) {
                $images[] = $matches[1][$i];
            }
            return $images;
        }

        return null;
    }

    //获取文章图片的地址
    public static  function get_post_image_url($image_id, $size = 'full')
    {
        if ($thumb = wp_get_attachment_image_src($image_id, $size)) {
            return $thumb[0];
        }
        return false;
    }

    public static  function  getPostImages($content, $postId)
    {
        //获取文章的首图
        $postcover = get_post_meta($postId, '_postcover', true);
        $video_poster = get_post_meta($postId, '_video_poster', true);
        $default_video_poster = get_option('raw_default_poster_image');
        if (empty($postcover) && !empty($video_poster) && $video_poster != $default_video_poster) {
            $postcover = $video_poster;
        }
        $post_frist_image = empty($postcover) ? self::get_post_content_first_image($content) : $postcover;
        if (empty($post_frist_image) && !empty(get_option('raw_default_thumbnail_image'))) {
            $post_frist_image = get_option('raw_default_thumbnail_image');
        }
        $post_thumbnail_image = "";
        $post_medium_image = "";
        $post_large_image = "";
        $post_full_image = "";
        $thumbnailId = "";
        $data = array();
        if (has_post_thumbnail($postId)) {
            //获取缩略的ID
            $thumbnailId = get_post_thumbnail_id($postId);
            //特色图缩略图
            $image = wp_get_attachment_image_src($thumbnailId, 'thumbnail');
            if ($image != false) {
                $post_thumbnail_image = $image[0];
                $post_thumbnail_image_150 = $image[0];
            }
            //特色中等图
            $image = wp_get_attachment_image_src($thumbnailId, 'medium');
            if ($image != false) {
                $post_medium_image = $image[0];
                $post_medium_image_300 = $image[0];
            }

            //特色大图
            $image = wp_get_attachment_image_src($thumbnailId, 'large');
            if ($image != false) {
                $post_large_image = $image[0];
                $post_thumbnail_image_624 = $image[0];
            }
            //特色原图
            $image = wp_get_attachment_image_src($thumbnailId, 'full');
            if ($image != false) {
                $post_full_image = $image[0];
            }
        }

        if (!empty($post_frist_image) && empty($post_thumbnail_image)) {
            $post_thumbnail_image = $post_frist_image;
        }

        if (!empty($post_frist_image) && empty($post_medium_image)) {
            $post_medium_image = $post_frist_image;
        }

        if (!empty($post_frist_image) && empty($post_large_image)) {
            $post_large_image = $post_frist_image;
        }

        if (!empty($post_frist_image) && empty($post_full_image)) {
            $post_full_image = $post_large_image;
        }

        //$post_all_images = get_attached_media( 'image', $postId);     
        $post_all_images = self::get_post_content_images($content);
        $data['post_frist_image'] = $post_frist_image;
        $data['post_thumbnail_image'] = $post_thumbnail_image;
        $data['post_medium_image'] = $post_medium_image;
        $data['post_large_image'] = $post_large_image;
        $data['post_full_image'] = $post_full_image;
        $data['post_all_images'] = $post_all_images;
        $data['postcover'] = $postcover;
        //$data['has_post_thumbnail']=has_post_thumbnail($postId);
        //$data['thumbnailId']=$thumbnailId;

        return  $data;
    }

    public static  function getPostImages2($post_content, $post_id)
    {
        $content_first_image = self::get_post_content_first_image($post_content);
        if (empty($content_first_image)) {
            $content_first_image = '';
        }

        $post_thumbnail_image_150 = '';
        $post_medium_image_300 = '';
        $post_thumbnail_image_624 = '';
        $post_thumbnail_image = '';
        $_data = array();
        $thumbnail_id = get_post_thumbnail_id($post_id);
        if ($thumbnail_id) {
            $thumb = wp_get_attachment_image_src($thumbnail_id, 'thumbnail');
            $post_thumbnail_image = $thumb[0];
        } else if ($content_first_image) {
            $attachments = get_attached_media('image', $post_id); //查找文章的附件
            $index = array_keys($attachments);
            $flag = 0;

            for ($i = 0; $i < sizeof($index); $i++) {
                $arr = $attachments[$index[$i]];
                $imageName = $arr->{"post_title"};
                if (strpos($content_first_image, $imageName) !== false) {  //附件的名称如果和第一张图片相同,就取这个附件的缩略图
                    {
                        $post_thumbnail_image_150 = wp_get_attachment_image_url($arr->{"ID"}, 'thumbnail');
                        $post_medium_image_300 = wp_get_attachment_image_url($arr->{"ID"}, 'medium');
                        $post_thumbnail_image_624 = wp_get_attachment_image_url($arr->{"ID"}, 'post-thumbnail');
                        $id = $arr->{"ID"};
                        $flag++;
                        break;
                    }
                }
            }
            if ($flag > 0) {
                $post_thumbnail_image = $post_thumbnail_image_150;
            } else {
                $post_thumbnail_image = $content_first_image;
            }
        } else {
            $post_thumbnail_image = '';
        }

        if (strlen($post_medium_image_300) > 0) {
            $_data['post_medium_image_300'] = $post_medium_image_300;
        } else {
            $_data['post_medium_image_300'] = $content_first_image;
        }
        if (strlen($post_thumbnail_image_624) > 0) {
            $_data['post_thumbnail_image_624'] = $post_thumbnail_image_624;
        } else {
            $_data['post_thumbnail_image_624'] = $content_first_image;
        }
        $_data['post_thumbnail_image'] = $post_thumbnail_image;
        $_data['content_first_image'] = $content_first_image;
        return  $_data;
    }




    public static  function get_content_post($url, $post_data = array(), $header = array())
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书检查
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);  // 从证书中检查SSL加密算法是否存在
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_AUTOREFERER, true);
        $content = curl_exec($ch);
        $info = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($code == '200') {
            $errcode = 0;
        } else {
            $errcode = (int)$code;
        }
        $result =  array('code' => $code, 'errcode' => $errcode, 'buffer' => $content);
        return $result;
    }

    //发起https请求
    public static function  https_request($url)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl,  CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        $data = curl_exec($curl);
        if (curl_errno($curl)) {
            return 'ERROR';
        }
        curl_close($curl);
        return $data;
    }



    public static  function time_tran($the_time)
    {
        date_default_timezone_set('Asia/Shanghai');
        //date_default_timezone_set('PRC');
        // $now_time = date("Y-m-d H:i:s",time()+8*60*60); 
        $now_time = date("Y-m-d H:i:s", time());
        $now_time = strtotime($now_time);
        $show_time = strtotime($the_time);
        $dur = $now_time - $show_time;
        if ($dur < 0) {
            return $the_time;
        } else {
            if ($dur < 60) {
                return $dur . '秒前';
            } else {
                if ($dur < 3600) {
                    return floor($dur / 60) . '分钟前';
                } else {
                    if ($dur < 86400) {
                        return floor($dur / 3600) . '小时前';
                    } else {
                        if ($dur < 259200) { //3天内
                            return floor($dur / 86400) . '天前';
                        } else {
                            return date("Y-m-d", $show_time);
                        }
                    }
                }
            }
        }
    }



    /**
     * 检验数据的真实性，并且获取解密后的明文.
     * @param $sessionKey string 用户在小程序登录后获取的会话密钥
     * @param $appid string 小程序的appid
     * @param $encryptedData string 加密的用户数据
     * @param $iv string 与用户数据一同返回的初始向量
     * @param $data string 解密后的原文
     *
     * @return int 成功0，失败返回对应的错误码
     */
    public static function decrypt_data($appid, $sessionKey, $encryptedData, $iv, &$data)
    {

        $errors = array(
            'OK'                => 0,
            'IllegalAesKey'     => -41001,
            'IllegalIv'         => -41002,
            'IllegalBuffer'     => -41003,
            'DecodeBase64Error' => -41004
        );

        if (strlen($sessionKey) != 24) {
            return $errors['IllegalAesKey'];
        }
        $aesKey = base64_decode($sessionKey);


        if (strlen($iv) != 24) {
            return $errors['IllegalIv'];
        }
        $aesIV = base64_decode($iv);

        $aesCipher = base64_decode($encryptedData);

        $result = openssl_decrypt($aesCipher, 'AES-128-CBC', $aesKey, 1, $aesIV);

        $dataObj = json_decode($result);
        if ($dataObj  == NULL) {
            return $errors['IllegalBuffer'];
        }
        if ($dataObj->watermark->appid != $appid) {
            return $errors['IllegalBuffer'];
        }
        $data = $result;
        return $errors['OK'];
    }

    // 生成Session
    public static function generate_session()
    {
        date_default_timezone_set('Asia/Shanghai');
        $sessionexpire = empty(get_option('raw_sessionexpire')) ? 31104000 : (int)get_option('raw_sessionexpire');
        $raw_sessionId = md5(uniqid(md5(microtime(true)), true));
        $raw_sessionExpire = date('Y-m-d H:i:s', time() + $sessionexpire);
        $raw_session = array(
            'sessionId' => $raw_sessionId,
            'sessionExpire' => $raw_sessionExpire
        );
        return $raw_session;
    }

    // 生成invitecode
    public static function generate_invitecode()
    {
        date_default_timezone_set('Asia/Shanghai');
        $invitecode = md5(uniqid(md5(microtime(true)), true));
        return $invitecode;
    }


    public static function get_weixin_user($sessionId, $userId)
    {
        // if (empty($sessionId)) {
        //     return false;
        // }
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';


        $sql = $wpdb->prepare("select * FROM " . $wpdb->minapper_weixin_users . "  WHERE userid =%d", $userId);
        $user = $wpdb->get_row($sql);
        if (!empty($user)) {
            // return array(
            //     'userid' => $user['userid'],
            //     'openid' => $user['openid'],
            //     'sessioneEpire' => $user['sessionExpire']

            // );
            return $user;
        }
        return false;
    }

    public static function getLikeAvatarurl($postId, $top = 0)
    {
        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        if ($top == 0) {
            $sql = "SELECT (SELECT avatarurl from " . $wpdb->minapper_weixin_users . " u WHERE u.userid=t.userid) as avatarurl FROM " . $wpdb->minapper_ext . "  t where (t.extype='post' or t.extype='topic') and t.extkey='like'  and t.extvalue='1' and t.extid =" . $postId . "  order by t.creatdate desc";
        } else {

            $sql = "SELECT (SELECT avatarurl from " . $wpdb->minapper_weixin_users . " u WHERE u.userid=t.userid) as avatarurl FROM " . $wpdb->minapper_ext . "  t where (t.extype='post' or t.extype='topic') and t.extkey='like'  and t.extvalue='1' and t.extid =" . $postId . "  order by t.creatdate desc limit 0," . $top;
        }

        $likes = $wpdb->get_results($sql);
        $avatarurls = array();
        if (!empty($likes)) {
            foreach ($likes as $like) {
                //$_avatarurl['avatarurl']  =$like->avatarurl;   
                $avatarurls[] = $like->avatarurl;
            }
        }


        return $avatarurls;
    }

    public static function getLikeCount($postId)
    {
        //获取点赞的数量
        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $like_count = $wpdb->get_var("SELECT COUNT(1) FROM " . $wpdb->minapper_ext . " where (extype='post' or extype='topic') and extkey='like'  and extvalue='1' and extid =" . $postId);
        return $like_count;
    }

    public static function getMylike($userId, $postId)
    {
        $myLike = "0";
        global $wpdb;

        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $sql = $wpdb->prepare("SELECT COUNT(1) FROM " . $wpdb->minapper_ext . " where (extype='post' or extype='topic') and extkey='like'  and extvalue='1' and extid =%d and userid=%d", $postId, $userId);

        $like_count = (int)$wpdb->get_var($sql);
        if ($like_count == 1) {
            $myLike = "1";
        } else {
            $myLike = "0";
        }
        return  $myLike;
    }

    public static function updatePageviews($pageviews, $postId)
    {
        $result = update_post_meta($postId, 'raw_pageviews', $pageviews);
        if (!$result) {
            $result = add_post_meta($postId, 'raw_pageviews', 1, true);
        }
        return $result;
    }


    public static function getRelatedPosts($tags, $postId, $category_id, $format)
    {
        global $wpdb;
        date_default_timezone_set('Asia/Shanghai');
        $fristday = date("Y-m-d H:i:s", strtotime("-5 year"));
        $today = date("Y-m-d H:i:s"); //获取今天日期时间
        $relatedPosts = array();
        $sql = "";
        if ($format == "video") {

            if (is_array($tags) && count($tags) > 0) {
                $tags = implode(",", $tags);
                $sql = "SELECT DISTINCT
                    t.ID,
                    t.post_title,
                    t.post_content
                    FROM
                        $wpdb->posts t,
                        $wpdb->term_relationships rel1,
                        $wpdb->term_taxonomy tax1,
                        $wpdb->term_relationships ral,
                        $wpdb->term_taxonomy tax,
                        $wpdb->terms ter
                        WHERE
                            tax1.term_taxonomy_id = rel1.term_taxonomy_id 
                            AND t.ID = rel1.object_id 
                            AND tax1.taxonomy = 'post_tag' 
                            AND t.post_status = 'publish' 
                            AND t.post_type = 'post' 
                            AND tax1.term_id IN(" . $tags . ")
                            AND t.ID != " . $postId . "                   
                            AND t.`ID` = ral.object_id 
                            AND ral.term_taxonomy_id = tax.term_taxonomy_id 
                            AND tax.taxonomy = 'post_format' 
                            AND tax.term_id = ter.term_id 
                            AND ter.slug = 'post-format-video'
                        ORDER BY
                            RAND()
                        LIMIT 5";
            } else {
                if (!empty($category_id)) {

                    $sql = "SELECT DISTINCT
                    t.ID,
                    t.post_title,
                    t.post_content
                    FROM
                        $wpdb->posts t,
                        $wpdb->term_relationships rel1,
                        $wpdb->term_taxonomy tax1,
                        $wpdb->term_relationships ral,
                        $wpdb->term_taxonomy tax,
                        $wpdb->terms ter
                        WHERE
                            tax1.term_taxonomy_id = rel1.term_taxonomy_id 
                            AND t.ID = rel1.object_id 
                            AND tax1.taxonomy = 'category' 
                            AND t.post_status = 'publish' 
                            AND t.post_type = 'post' 
                            AND tax1.term_id =  " . $category_id . "
                            AND t.ID != " . $postId . "                   
                            AND t.`ID` = ral.object_id 
                            AND ral.term_taxonomy_id = tax.term_taxonomy_id 
                            AND tax.taxonomy = 'post_format' 
                            AND tax.term_id = ter.term_id 
                            AND ter.slug = 'post-format-video'
                        ORDER BY
                            RAND()
                        LIMIT 5";
                }
            }
        } else {

            if (is_array($tags) && count($tags) > 0) {
                $tags = implode(",", $tags);
                $sql = "
                SELECT distinct ID, post_title,post_content
                FROM " . $wpdb->posts . " , " . $wpdb->term_relationships . ", " . $wpdb->term_taxonomy . "
                WHERE " . $wpdb->term_taxonomy . ".term_taxonomy_id =  " . $wpdb->term_relationships . ".term_taxonomy_id
                AND ID = object_id
                AND taxonomy = 'post_tag'
                AND post_status = 'publish'
                AND post_type = 'post'
                AND term_id IN (" . $tags . ")
                AND ID != '" . $postId . "'
                ORDER BY  RAND() LIMIT 5";
            } else {
                if (!empty($category_id)) {
                    $sql = "
                    SELECT distinct ID, post_title,post_content
                    FROM " . $wpdb->posts . " , " . $wpdb->term_relationships . ", " . $wpdb->term_taxonomy . "
                    WHERE " . $wpdb->term_taxonomy . ".term_taxonomy_id =  " . $wpdb->term_relationships . ".term_taxonomy_id
                    AND ID = object_id
                    AND taxonomy = 'category'
                    AND post_status = 'publish'
                    AND post_type = 'post'
                    AND term_id = (" . $category_id . ")
                    AND ID != '" . $postId . "'
                    ORDER BY  RAND() LIMIT 5";
                }
            }
        }
        if (!empty($sql)) {

            $_relatedPosts = $wpdb->get_results($sql);
            if (empty($_relatedPosts) && $format == "video") {
                $sql = "SELECT DISTINCT
                t.ID,
                t.post_title,
                t.post_content
                FROM
                    $wpdb->posts t,
                    
                    $wpdb->term_relationships ral,
                    $wpdb->term_taxonomy tax,
                    $wpdb->terms ter
                    WHERE
                        t.post_status = 'publish' 
                        AND t.post_type = 'post' 
                        AND t.ID != " . $postId . "                   
                        AND t.`ID` = ral.object_id 
                        AND ral.term_taxonomy_id = tax.term_taxonomy_id 
                        AND tax.taxonomy = 'post_format' 
                        AND tax.term_id = ter.term_id 
                        AND ter.slug = 'post-format-video'
                    ORDER BY
                        RAND()
                    LIMIT 5";
            }
            $_relatedPosts = $wpdb->get_results($sql);
            foreach ($_relatedPosts as $relatedPost) {
                $relatedPostsImage = self::getPostImages($relatedPost->post_content, $relatedPost->ID);
                $post_id = $relatedPost->ID;
                $rpData['ID'] = $post_id;
                $postformat = get_post_format($post_id)?: 'standard';
                if ($postformat == 'postformatlink') {
                    $postformat = "link";
                }
                $rpData['format'] = $postformat;
                $channelsFeedId = empty(get_post_meta($post_id, '_channelsFeedId', true)) ? "" : get_post_meta($post_id, '_channelsFeedId', true);
                $rpData['channelsFeedId'] = $channelsFeedId;

                $channelsId = empty(get_post_meta($post_id, '_channelsId', true)) ? "" : get_post_meta($post_id, '_channelsId', true);
                $rpData['channelsId'] = $channelsId;

                $mpPostLink = empty(get_post_meta($post_id, '_mpPostLink', true)) ? "" : get_post_meta($post_id, '_mpPostLink', true);
                $rpData['mpPostLink'] = $mpPostLink;

                $unassociated = empty(get_post_meta($post_id, '_unassociated', true)) ? "" : get_post_meta($post_id, '_unassociated', true);
                $rpData['unassociated'] = $unassociated;

                $rpData['post_title'] = $relatedPost->post_title;
                $rpData['post_frist_image'] = $relatedPostsImage['post_frist_image'];
                $rpData['post_thumbnail_image'] = $relatedPostsImage['post_thumbnail_image'];
                $rpData['post_medium_image'] = $relatedPostsImage['post_medium_image'];
                $rpData['post_large_image'] = $relatedPostsImage['post_large_image'];
                $rpData['post_full_image'] = $relatedPostsImage['post_full_image'];
                $relatedPosts[] = $rpData;
            }
        }
        return $relatedPosts;
    }

    public static function  checkUser($sessionId, $userId)
    {
        global $wpdb;
        $wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';
        $userDeviceInfo = self::getUserDeviceInfo($userId, $sessionId);
        $haveSessionData = $userDeviceInfo['haveSessionData'];
        $count = 0;
        $result = false;
        if (!empty($sessionId) &&  !empty($userId) &&  is_int($userId)) {


            if ($haveSessionData) {
                $result = true;
            }
        }
        return $result;
    }

    public static function  checkUserEnable($sessionId, $userId)
    {

        $error = "0";
        $message = "";
        $forumslevel = "1";
        if (empty($sessionId) || empty($userId) || !is_int($userId)) {
            $error = "01";
            $message = "用户参数为空";
        } else {
            $chkUser = self::checkUser($sessionId, $userId);
            if (!$chkUser) {
                $error = "01";
                $message = "用户参数错误,请重新登录";
            }

            global $wpdb;
            $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
            // $sql = $wpdb->prepare( "select count(1) as count,forumslevel  FROM ".$wpdb->minapper_weixin_users." WHERE sessionid = %s and userid =%d",$sessionId,$userId);
            $sql = $wpdb->prepare("select count(1) as count,forumslevel  FROM " . $wpdb->minapper_weixin_users . " WHERE userid =%d", $userId);
            $user = $wpdb->get_row($sql);
            $count = (int) $user->count;
            $forumslevel = $user->forumslevel;
            if ($count == 0) {

                $error = "01";
                $message = "未找到该用户";
            } else {
                if ($forumslevel == "0") {
                    $error = "02";
                    $message = "此用户已被禁止互动";
                }
            }
        }

        $result = array(
            'error' => $error,
            'message' => $message
        );
        return $result;
    }

    //检查用户的权限
    public static  function checkUserRight($userId, $rightype)
    {

        $min_comment_user_member = empty(get_option("raw_min_comment_user_member")) ? '10' : get_option("raw_min_comment_user_member");
        $min_comment_pass_user_member = empty(get_option("raw_min_comment_pass_user_member")) ? '10' : get_option("raw_min_comment_pass_user_member");
        $min_postarticle_pass_user_member = empty(get_option("raw_min_postarticle_pass_user_member")) ? '10' : get_option("raw_min_postarticle_pass_user_member");
        $min_posttopic_pass_user_member = empty(get_option("raw_min_posttopic_pass_user_member")) ? '10' : get_option("raw_min_posttopic_pass_user_member");
        $min_posttopic_user_member = empty(get_option("raw_min_posttopic_user_member")) ? '10' : get_option("raw_min_posttopic_user_member");
        $min_postarticle_user_member = empty(get_option("raw_min_postarticle_user_member")) ? '10' : get_option("raw_min_postarticle_user_member");
        global $wpdb;
        $result = false;
        if (empty($userId)) {
            return $result;
        }
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = $wpdb->prepare("select `member` FROM " . $wpdb->minapper_weixin_users . " WHERE userid =%d", $userId);
        $member = $wpdb->get_var($sql);
        if ($member == "00") {
            $result = true;
        } else {
            $_member = (int)$member;

            if ($rightype == 'publishComment') {

                if ($min_comment_user_member == '01') {
                    if ($member == "01") {
                        $result = true;
                    } else {
                        $result = false;
                    }
                } else if ($min_comment_user_member == '00') {
                    $result = false;
                } else {

                    if ($member == "01") {
                        $result = true;
                    } else {
                        $_min_comment_user_member = (int)$min_comment_user_member;
                        if ($_member >= $_min_comment_user_member) {
                            $result = true;
                        }
                    }
                }
            }

            if ($rightype == 'approveComment') {

                if ($min_comment_pass_user_member == '01') {
                    if ($member == "01") {
                        $result = true;
                    } else {
                        $result = false;
                    }
                } else if ($min_comment_pass_user_member == '00') {
                    $result = false;
                } else {
                    if ($member == "01") {
                        $result = true;
                    } else {
                        $_min_comment_pass_user_member = (int)$min_comment_pass_user_member;
                        if ($_member >= $_min_comment_pass_user_member) {
                            $result = true;
                        }
                    }
                }
            }

            if ($rightype == 'approveArticle') {
                if ($min_postarticle_pass_user_member  == "01") {

                    if ($member == "01") {
                        $result = true;
                    } else {
                        $result = false;
                    }
                } else if ($min_postarticle_pass_user_member  == "00") {
                    $result = false;
                } else {
                    if ($member == "01") {
                        $result = true;
                    } else {
                        $_min_postarticle_pass_user_member = (int)$min_postarticle_pass_user_member;
                        if ($_member >= $_min_postarticle_pass_user_member) {
                            $result = true;
                        }
                    }
                }
            }

            if ($rightype == 'publishTopic') {
                if ($min_posttopic_user_member == '01') {

                    if ($member == "01") {
                        $result = true;
                    } else {
                        $result = false;
                    }
                } else if ($min_posttopic_user_member == '00') {
                    $result = false;
                } else {
                    if ($member == "01") {
                        $result = true;
                    } else {
                        $_min_posttopic_user_member = (int)$min_posttopic_user_member;
                        if ($_member >= $_min_posttopic_user_member) {
                            $result = true;
                        }
                    }
                }
            }

            if ($rightype == 'publishPost') {
                if ($min_postarticle_user_member == '01') {

                    if ($member == "01") {
                        $result = true;
                    } else {
                        $result = false;
                    }
                } else if ($min_postarticle_user_member == '00') {
                    $result = false;
                } else {
                    if ($member == "01") {
                        $result = true;
                    } else {
                        $_min_postarticle_user_member = (int)$min_postarticle_user_member;
                        if ($_member >= $_min_postarticle_user_member) {
                            $result = true;
                        }
                    }
                }
            }

            if ($rightype == 'approveTopic') {

                if ($min_posttopic_pass_user_member == '01') {
                    if ($member == "01") {
                        $result = true;
                    } else {
                        $result = false;
                    }
                } else if ($min_posttopic_pass_user_member == '00') {
                    $result = false;
                } else {
                    if ($member == "01") {
                        $result = true;
                    } else {
                        $_min_posttopic_pass_user_member = (int)$min_posttopic_pass_user_member;
                        if ($_member >= $_min_posttopic_pass_user_member) {
                            $result = true;
                        }
                    }
                }
            }
        }

        // $r =array(
        //     'result' => $result,
        //     'member' => $_member,
        //     'min_postarticle_pass_user_member' => $min_postarticle_pass_user_member,
        // );
        return $result;
    }

    public static function getMemberName($member)
    {
        $memberName = "";
        switch ($member) {
            case "00":
                $memberName = empty(get_option('raw_admin_nickname')) ? "管理员" : get_option('raw_admin_nickname');
                break;
            case "01":
                $memberName = empty(get_option('raw_vip_nickname')) ? "vip" : get_option('raw_vip_nickname');
                break;
            case "10":
                $memberName = empty(get_option('raw_new_user_nickname')) ? "新手会员" : get_option('raw_new_user_nickname');
                break;
            case "11":
                $memberName = empty(get_option('raw_first_level_nickname')) ? "一星会员" : get_option('raw_first_level_nickname');
                break;
            case "12":
                $memberName = empty(get_option('raw_second_level_nickname')) ? "二星会员" : get_option('raw_second_level_nickname');
                break;
            case "13":
                $memberName = empty(get_option('raw_third_level_nickname')) ? "三星会员" : get_option('raw_third_level_nickname');
                break;
            case "14":
                $memberName = empty(get_option('raw_fourth_level_nickname')) ? "四星会员" : get_option('raw_fourth_level_nickname');
                break;
            case "15":
                $memberName = empty(get_option('raw_fifth_level_nickname')) ? "五星会员" : get_option('raw_fifth_level_nickname');
                break;
        }
        return $memberName;
    }



    // 组合参数
    public static function param_atts($pairs, $atts)
    {
        $atts = (array)$atts;
        $out = array();
        foreach ($pairs as $name => $default) {
            if (array_key_exists($name, $atts)) {
                $out[$name] = $atts[$name];
            } else {
                $out[$name] = $default;
            }
        }

        return $out;
    }

    public static  function get_client_ip()
    {
        foreach (array(
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ) as $key) {
            if (array_key_exists($key, $_SERVER)) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    //会过滤掉保留地址和私有地址段的IP，例如 127.0.0.1会被过滤
                    //也可以修改成正则验证IP
                    if ((bool) filter_var(
                        $ip,
                        FILTER_VALIDATE_IP,
                        FILTER_FLAG_IPV4 |
                            FILTER_FLAG_NO_PRIV_RANGE |
                            FILTER_FLAG_NO_RES_RANGE
                    )) {
                        return $ip;
                    }
                }
            }
        }
        return null;
    }


    // 检查付款通知签名
    public static function check_notify_sign($data, $key)
    {

        ksort($data);
        $buff = '';

        foreach ($data as $k => $v) {
            if ($k == 'sign' ||  is_array($v)) continue;
            $buff .= $k . '=' . $v . '&';
        }

        $string_sign = $buff . 'key=' . $key;
        $sign = strtoupper(md5($string_sign));

        if ($sign == $data['sign']) {

            return true;
        }
    }

    public static function insertFormId($data_array)
    {

        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $inserExtFormId = $wpdb->insert($wpdb->minapper_ext, $data_array);

        return $inserExtFormId;
    }

    public static function  getExtName($extype, $extId)
    {
        $extname = "";
        if ($extype == "postpraise" || $extype == "postpayment" || $extype == "postsubscribe" || $extype == "catsubscribe" || $extype == "vipmember"  || $extype == "minappershop") {

            if ($extype == "postpraise"  || $extype == "postpayment" || $extype == "postsubscribe") {
                $post = get_post($extId);
                $extname = $post->post_title;
                if ($extype == "postpayment" || $extype == "postsubscribe") {
                    $extname = "单篇付费阅读：" . $extname;
                } else if ($extype == "postpraise") {
                    $extname = "文章赞赏：" . $extname;
                }
            }
            if ($extype == "catsubscribe") {
                $cat = get_category($extId);
                $extname = "专栏付费订阅：";
                if (!empty($cat)) {
                    $extname .= $cat->name;
                }
            }
            if ($extype == "vipmember") {
                $extname = "购买vip";
            }
            if ($extype == "minappershop") {
                $post = get_post($extId);
                $extname = "积分兑换商品:" . $post->post_title;
            }
            // if ($extype == "minapperspecial") {
            //     $minapperspecial =get_term_by('id',$extId,'minapperspecialcategory');
            //     $extname = "付费专题:".$minapperspecial->name;

            // }
        }
        return $extname;
    }

    public static function insertOrder($data_array)
    {

        global $wpdb;
        $wpdb->minapper_order = $wpdb->prefix . 'minapper_order';
        $inserOrder = $wpdb->insert($wpdb->minapper_order, $data_array);

        return $inserOrder;
    }

    public static  function checkOrderIsExist($extype, $userId, $extId)
    {
        global $wpdb;
        $wpdb->minapper_order = $wpdb->prefix . 'minapper_order';
        $sql = "SELECT count(1) from " . $wpdb->minapper_order . "  where status=1 and ordertype ='" . $extype . "'  and  userid=" . $userId . " and extid =" . $extId . " order by updatedate desc";
        $orderCount = (int)$wpdb->get_var($sql);
        return $orderCount;
    }

    public static function updateOrderStatus($data_array, $noncestr, $userId)
    {
        global $wpdb;
        $wpdb->minapper_order = $wpdb->prefix . 'minapper_order';
        $sql = $wpdb->prepare("select count(1) from " . $wpdb->minapper_order . " t where t.noncestr=%s", $noncestr);
        $count = $wpdb->get_var($sql);
        $updateOrderStatusResult = 0;
        if ($count == 0) {
            $updateOrderStatusResult = 0;
        } else {
            $where  = array('noncestr' => $noncestr, 'userid' => $userId);
            $updateOrderStatusResult = $wpdb->update($wpdb->minapper_order, $data_array, $where);
        }
        return $updateOrderStatusResult;
    }




    /**
     * 
     * 产生随机字符串，不长于32位
     * @param int $length
     * @return 产生的随机字符串
     */
    public static function getNonceStr($length = 32)
    {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }




    public static function getOrders($key, $flag)
    {
        global $wpdb;
        $wpdb->minapper_order = $wpdb->prefix . 'minapper_order';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';


        $sql = "SELECT t.orderid,t.userid,t.extname,CONCAT_WS('/',t.totalfee,t.integral) as totalfee ,t.ordertype,t.integral,t.extid,t.updatedate,t.paydate,case t.status WHEN '0' then '未支付' WHEN '1' then '已支付' end as status,case t.ordertype WHEN 'postpraise' then '文章赞赏' WHEN 'postpayment' then '单篇付费'  WHEN 'catsubscribe' then '订阅付费' WHEN 'postsubscribe' then '订阅付费' WHEN 'postIntegral' then '积分阅读'  WHEN 'redpack' then '积分兑红包' WHEN 'vipmember' then '购买vip会员' end as ordertypename,u.nickname FROM  " . $wpdb->minapper_order . " t ," . $wpdb->minapper_weixin_users . " u where  ";
        if (!empty($key) && !empty($flag)) {
            if ($flag == 'userid') {
                $sql .= "t.userid =" . $key . " and ";
            } elseif ($flag == 'nickname') {
                $sql .= "(u.nickname like '%" . $key . "%' or u.userid= " . (int)$key . ") and ";
            }
        }


        $sql .= "t.userid=u.userid";
        $sql .= "  order by updatedate desc";
        $orders = $wpdb->get_results($sql, ARRAY_A);
        if (!empty($orders)) {
            return $orders;
        } else {
            return null;
        }
    }

    public static function getIntegrals($key, $flag, $current_page, $per_page)
    {
        global $wpdb;
        $wpdb->minapper_integral = $wpdb->prefix . 'minapper_integral';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "SELECT t.id,t.userid,t.integraltype,t.itemid,t.itemname,t.flag,
        case t.flag WHEN 'add' then concat('+',t.integral) WHEN 'minus' then concat('-',t.integral)  end as  integral,
        t.updatedate as integralDate,u.nickname FROM  " . $wpdb->minapper_integral . " t ," . $wpdb->minapper_weixin_users . " u where  ";
        if (!empty($key) && !empty($flag)) {
            if ($flag == 'userid') {
                $sql .= "t.userid =" . $key . " and ";
            } elseif ($flag == 'nickname') {
                $sql .= "(u.nickname like '%" . $key . "%' or u.userid= " . (int)$key . ")  and ";
            }
        }
        $sql .= " t.userid=u.userid";
        $sql .= "  order by t.updatedate desc";
        if (!empty($per_page)) {
            $sql .= "  limit " . $current_page . "," . $per_page;
        }
        $_integrals = $wpdb->get_results($sql, ARRAY_A);
        $integrals = array();
        if (!empty($_integrals)) {

            foreach ($_integrals as $integral) {
                $integraltype = $integral['integraltype'];
                $itemid = (int)$integral['itemid'];
                $itemcontent = "";

                if ($integraltype == 'raw_newpost_integral' || $integraltype == 'raw_readpost_integral' || $integraltype == 'raw_comment_integral' || $integraltype == 'raw_like_integral') {
                    $post = get_post($itemid);
                    if (!empty($post)) {
                        $itemcontent = $post->post_title;
                    }
                } else if ($integraltype == 'raw_newtopic_integral' || $integraltype == 'raw_replaytopic_integral') {
                    $itemcontent = wp_filter_nohtml_kses(bbp_get_topic_title($itemid));
                }
                $integral['itemcontent'] = $itemcontent;
                $integrals[] = $integral;
            }

            return $integrals;
        } else {
            return null;
        }
    }


    public static function getIntegralsCount($key, $flag)
    {
        global $wpdb;
        $wpdb->minapper_integral = $wpdb->prefix . 'minapper_integral';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "SELECT count(1) FROM  " . $wpdb->minapper_integral . " t ," . $wpdb->minapper_weixin_users . " u where  ";
        if (!empty($key) && !empty($flag)) {
            if ($flag == 'userid') {
                $sql .= "t.userid =" . $key . " and ";
            } elseif ($flag == 'nickname') {
                $sql .= "(u.nickname like '%" . $key . "%' or u.userid= " . (int)$key . ")  and ";
            }
        }
        $sql .= " t.userid=u.userid";
        $integralsCount = (int)$wpdb->get_var($sql);
        return $integralsCount;
    }

    public static function getMessages($key, $flag)
    {
        global $wpdb;
        $wpdb->minapper_custom_form = $wpdb->prefix . 'minapper_custom_form';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_custom_fields = $wpdb->prefix . 'minapper_custom_fields';
        $wpdb->minapper_form_field = $wpdb->prefix . 'minapper_form_field';


        $sql = "SELECT
                    wcf.id,
                    wcf.content,
                    wcf.creatdate,
                    wcf.excerpt,
                    u.nickname,
                    u.userid,
                    (
                        SELECT
                            cf.field
                        FROM
                            (
                                SELECT
                                    tb.parent,
                                    group_concat(tb.field SEPARATOR ';<br/>') AS field
                                FROM
                                    (
                                        SELECT
                                            CONCAT(
                                                cf.fieldname,
                                                ':',
                                                t.fieldvalue
                                            ) AS field,
                                            t.parent
                                        FROM
                                            $wpdb->minapper_form_field t,
                                            $wpdb->minapper_custom_fields cf
                                        WHERE
                                            t.fieldkey = cf.fieldkey  and  cf.category='message'
                                    ) tb
                                GROUP BY
                                    tb.parent
                            )
                     cf
                WHERE
                    cf.parent = wcf.id
                ) as field
                FROM
                    $wpdb->minapper_custom_form wcf,
                    $wpdb->minapper_weixin_users u
                WHERE
                    wcf.author = u.userid";
        if (!empty($key) && !empty($flag)) {
            if ($flag == 'userid') {
                $sql .= " and wcf.author =" . $key . " ";
            } elseif ($flag == 'content') {
                $sql .= " and wcf.content like '%" . $key . "%'";
            }
        }
        $sql .= "  order by wcf.creatdate desc";
        $messages = $wpdb->get_results($sql, ARRAY_A);
        if (!empty($messages)) {
            return $messages;
        } else {
            return null;
        }
    }

    public static function getMesssage($id)
    {
        global $wpdb;
        $wpdb->minapper_custom_form = $wpdb->prefix . 'minapper_custom_form';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "select t.content ,u.nickname,t.excerpt from " . $wpdb->minapper_custom_form . " t  ," . $wpdb->minapper_weixin_users . " u where t.author =u.userid  and t.id=" . $id;
        $message = $wpdb->get_row($sql);
        if (!empty($message)) {
            return $message;
        } else {
            return null;
        }
    }


    public static function getCustomFields($key, $postyype, $category)
    {
        global $wpdb;
        $wpdb->minapper_custom_fields = $wpdb->prefix . 'minapper_custom_fields';
        $sql = "SELECT t.id, t.fieldname,t.datalength,t.orderby,t.posttypes,
        case t.posttypes WHEN 'post' then '文章' WHEN 'page' then '页面' WHEN 'form' then '自定义表单' WHEN 'topic' then '话题' WHEN 'reply' then '话题回复' end as posttypesname,t.category,
        case t.category WHEN 'business' then '企业信息' WHEN 'message' then '留言'  WHEN 'goods' then '商品' WHEN 'video' then '视频' WHEN 'weixinmp' then '公众号' WHEN 'topic' then '话题自定义字段' WHEN 'reply' then '话题回复自定义字段' end as categoryname,t.fieldkey,t.datatype FROM  " . $wpdb->minapper_custom_fields . " t  where  1=1 ";
        if (!empty($key)) {
            $sql .= "   and  t.fieldname like '%" . $key . "%' ";
        }
        if (!empty($category)) {
            $sql .= " and t.category ='" . $category . "'";
        }
        if (!empty($postyype)) {
            $sql .= " and t.posttypes ='" . $postyype . "'";
        }
        $sql .= "  order by t.posttypes ,t.category,t.orderby ";
        $customFields = $wpdb->get_results($sql, ARRAY_A);
        if (!empty($customFields)) {
            return $customFields;
        } else {
            return null;
        }
    }

    public static function getCustomField($id)
    {
        global $wpdb;
        $wpdb->minapper_custom_fields = $wpdb->prefix . 'minapper_custom_fields';
        $sql = "SELECT t.id, t.fieldname,t.datalength,t.orderby,t.posttypes,case t.posttypes WHEN 'post' then '文章' WHEN 'page' then '页面'  WHEN 'form' then '自定义表单' end as posttypesname,t.category,case t.category WHEN 'business' then '企业信息' WHEN 'message' then '留言' WHEN 'video' then '视频' WHEN 'weixinmp' then '公众号' end as categoryname,t.fieldkey,t.datatype FROM  " . $wpdb->minapper_custom_fields . " t  where   t.id=" . $id . " order by category,orderby";
        $field = $wpdb->get_row($sql);
        if (!empty($field)) {
            return $field;
        } else {
            return null;
        }
    }

    //重写内容
    public static function rewrite_content($content, $num)
    {
        $content = self::get_content_minappershortcode($content);
        $tempContent = substr($content, 0, $num);
        if (strlen($tempContent) == strlen($content))
            return $content;

        else {
            if (($post_content = strrpos($tempContent, "<")) > strrpos($tempContent, ">"))
                $tempContent = substr($tempContent, 0, $post_content);
            return '' . strip_tags(self::utf8_trim($tempContent), '<br/>') . '......';
        }
    }

    public static function utf8_trim($str)
    {

        $len = strlen($str);
        $hex = '';
        for ($i = strlen($str) - 1; $i >= 0; $i -= 1) {
            $hex .= ' ' . ord($str[$i]);
            $ch = ord($str[$i]);
            if (($ch & 128) == 0) return (substr($str, 0, $i));
            if (($ch & 192) == 192) return (substr($str, 0, $i));
        }
        return ($str . $hex);
    }

    // public static function filterEmoji($str)
    // {
    //     $str = preg_replace_callback(
    //         '/./u',
    //         function (array $match) {
    //             return strlen($match[0]) >= 4 ? '' : $match[0];
    //         },
    //         $str
    //     );



    //     return $str;
    // }

    public static function filterEmoji($nickname)
    {
        $nickname = preg_replace('/[\x{1F600}-\x{1F64F}]/u', '', $nickname);
        $nickname = preg_replace('/[\x{1F300}-\x{1F5FF}]/u', '', $nickname);
        $nickname = preg_replace('/[\x{1F680}-\x{1F6FF}]/u', '', $nickname);
        $nickname = preg_replace('/[\x{2600}-\x{26FF}]/u', '', $nickname);
        $nickname = preg_replace('/[\x{2700}-\x{27BF}]/u', '', $nickname);
        $nickname = str_replace(array('"', '\''), '', $nickname);
        $nickname = preg_replace_callback(
            '/./u',
            function (array $match) {
                return strlen($match[0]) >= 4 ? '' : $match[0];
            },
            $nickname
        );
        return addslashes(trim($nickname));
    }

    public static function getUserFollow($sessionId, $userId, $author_id)
    {
        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $checkUser = self::checkUser($sessionId, $userId);
        if ($checkUser) {

            // $author_id=bbp_get_topic_author_id( $topic_id );
            $sql = $wpdb->prepare("select count(1) from " . $wpdb->minapper_ext . " t where t.userid=%d and t.extid=%d and extype='follow'", $userId, $author_id);
            $count = (int)$wpdb->get_var($sql);
            if ($count == 1) {
                return true;
            } else {
                return  false;
            }
        } else {
            return false;
        }
    }

    public static function getMemberUser($userId)
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = $wpdb->prepare("SELECT t.`member`,t.invitecode,t.integral,t.memberbegindate,t.memberenddate,t.forumslevel,case t.`member` WHEN '00' then '管理员' WHEN '01' then 'VIP' WHEN '02' then '付费会员' WHEN '10' then '普通会员' end as membername,t.nickname,t.creatdate,t.province,t.city,t.avatarurl,
        (
                SELECT
				ud.meta_value
                FROM
                    " . $wpdb->usermeta . " ud
                WHERE
				ud.meta_key = 'description' 
                AND ud.user_id =t.userid
			) AS description FROM " . $wpdb->minapper_weixin_users . " t WHERE t.userid =%d", $userId);

        $user = $wpdb->get_row($sql);
        if (!empty($user)) {
            return $user;
        } else {
            return null;
        }
    }

    public static function getMemberUserbySessionId($sessionId, $userId)
    {
        if (empty($sessionId) || empty($userId)) {
            return null;
        } elseif (!is_int($userId)) {

            return null;
        }
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = $wpdb->prepare("SELECT `member`,integral,memberbegindate,memberenddate,case `member` WHEN '00' then '管理员' WHEN '01' then 'VIP' WHEN '02' then '付费会员' WHEN '10' then '普通会员' end as membername,nickname,creatdate,province,city,avatarurl FROM " . $wpdb->minapper_weixin_users . " WHERE userid =%d", $userId);
        $user = $wpdb->get_row($sql);
        if (!empty($user)) {
            return $user;
        } else {
            return null;
        }
    }

    public static function getMemberUserbyUserId($userId)
    {
        if (empty($userId)) {
            return null;
        } elseif (!is_int($userId)) {

            return null;
        }
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = $wpdb->prepare("SELECT `member`,integral,memberbegindate,memberenddate,case `member` WHEN '00' then '管理员' WHEN '01' then 'VIP' WHEN '02' then '付费会员' WHEN '10' then '普通会员' end as membername,nickname as nickName,creatdate,province,city,avatarurl as avatarUrl,userid FROM " . $wpdb->minapper_weixin_users . " WHERE userid =%d", $userId);
        $user = $wpdb->get_row($sql);
        if (!empty($user)) {
            return $user;
        } else {
            return null;
        }
    }





    public static function getMemberUsers($nickname, $level, $order, $orderby)
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $sql = "SELECT u.invitecode, u.userid,IFNULL(u.phone,'') as phone ,u.`member`,u.integral,u.memberbegindate,u.memberenddate,case u.forumslevel  WHEN '1' then '是' WHEN '0' then '否' end as forumslevel ,
        u.nickname,u.creatdate,u.province,u.city,u.avatarurl,case u.gender WHEN '1' then '男' WHEN '0' then '女' end as gender ,u.phone,
        (select count(1) from " . $wpdb->minapper_ext . " e where e.userid=u.userid and e.extype='invite' and e.extid='1' ) as invitecount
         FROM  " . $wpdb->minapper_weixin_users . " u where 1=1  ";
        if (!empty($nickname)) {
            $sql .= "  and  u.nickname like '%" . $nickname . "%' or u.userid= " . (int)$nickname;
        }

        if (!empty($level)) {
            $sql .= "  and  u.member='" . $level . "'";
        }

        if (!empty($orderby)) {
            $sql .= "  order by invitecount " . $order;
        } else {
            $sql .= "  order by u.creatdate desc";
        }




        $_users = $wpdb->get_results($sql, ARRAY_A);
        $users = array();

        if (!empty($_users)) {
            foreach ($_users as $user) {
                $membername = self::getMemberName($user['member']);
                $user['membername'] = $membername;
                $users[] = $user;
            }
            return $users;
        } else {
            return null;
        }
    }

    public  static  function getUserDeviceInfo($userId, $sessionId)
    {
        global $wpdb;
        $result = array();
        $wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';

        $deviceInfo =  empty($_SERVER['HTTP_MINAPPER_DEVICE']) ? '' : $_SERVER['HTTP_MINAPPER_DEVICE'];
        $ua = empty($_SERVER['HTTP_USER_AGENT']) ? '' : $_SERVER['HTTP_USER_AGENT'];
        $minapperVersion =  empty($_SERVER['HTTP_MINAPPER_VERSION']) ? '' : $_SERVER['HTTP_MINAPPER_VERSION'];
        $plugVersion = RAW()::$plugVersion;

        $device = RAW_Util::get_device($ua, $deviceInfo);
        $deviceId = RAW_Util::get_device_id($ua, $deviceInfo);
        $haveSessionData = false;
        if ($sessionId != '') {
            $sql = $wpdb->prepare("select count(1) from " . $wpdb->minapper_user_session . " where userid=%d and  deviceid=%s and sessionid=%s", $userId, $deviceId, $sessionId);
            $count = (int)$wpdb->get_var($sql);
            if ($count == 1) {
                $haveSessionData = true;
            }
        }





        $result["minapperVersion"] = $minapperVersion;
        $result["plugVersion"] = $plugVersion;
        $result["device"] = $device;
        $result["deviceId"] = $deviceId;
        $result["deviceInfo"] = $deviceInfo;
        $result["ua"] = $ua;
        $result["haveSessionData"] = $haveSessionData;
        // $result["server"]=$_SERVER;
        return $result;
    }
    public static function getMemberUserInfo($userId)
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $wpdb->minapper_mpsubscribe_user = $wpdb->prefix . 'minapper_mpsubscribe_user';

        $sql = "SELECT  w.userid,w.openid,
        IFNULL(w.phone,'') as phone,
        w.`member`,
        w.invitecode,
        w.integral,
        DATE_FORMAT(w.memberenddate, '%Y-%m-%d') as memberenddate,
        w.memberbegindate,w.nickname,w.avatarurl,
        w.forumslevel,        
         (
            SELECT
                u.meta_value
            FROM
                " . $wpdb->usermeta . " u
            WHERE
                u.meta_key = '" . $wpdb->prefix . "user_level' 
            AND u.user_id = w.userid
        ) AS level,
			(
                SELECT
				ud.meta_value
                FROM
                    " . $wpdb->usermeta . " ud
                WHERE
				ud.meta_key = 'description' 
                AND ud.user_id = w.userid
			) AS description,
          (SELECT  count(1) from " . $wpdb->posts . " p where p.post_type='post' and (p.post_status='publish' or p.post_status='pending')  and p.post_author=userid ) as postcount,
          (SELECT  count(1) from " . $wpdb->posts . " c where c.post_type='topic' and ( c.post_status='publish' or  c.post_status='pending')and c.post_author=userid ) as topiccount,
         (select count(1) from " . $wpdb->minapper_ext . "  e where e.extid=w.userid and e.extype='follow' ) as followmecount,
         (select count(1) from " . $wpdb->minapper_ext . "   f where f.userid=w.userid and f.extype='follow' ) as myfollowcount,
         (select s.extvalue from " . $wpdb->minapper_ext . "   s where s.userid=w.userid and s.extype ='subscribeMessage' and s.extkey = 'newcontent' ) as newcontentSubscribeCount,
         (select s.extvalue from " . $wpdb->minapper_ext . "   s where s.userid=w.userid and s.extype ='subscribeMessage' and s.extkey = 'newreplay' ) as newreplaySubscribeCount,
         (select count(1) from " . $wpdb->minapper_mpsubscribe_user . "   s where s.unionid=w.unionid and s.subscribe='1') as ismpsubscribe
        FROM
            " . $wpdb->minapper_weixin_users . "  w where w.userid=" . $userId;
        $memberUserInfo = $wpdb->get_row($sql);
        $memberUserInfo->membername = self::getMemberName($memberUserInfo->member);
        return $memberUserInfo;
    }

    public static function updateUserMember($userId, $flag = null)
    {
        global $wpdb;
        $first_level_integral = empty(get_option('raw_first_level_integral')) ? 0 : (int)get_option('raw_first_level_integral');
        $second_level_integral = empty(get_option('raw_second_level_integral')) ? 0 : (int)get_option('raw_second_level_integral');
        $third_level_integral = empty(get_option('raw_third_level_integral')) ? 0 : (int)get_option('raw_third_level_integral');
        $fourth_level_integral = empty(get_option('raw_fourth_level_integral')) ? 0 : (int)get_option('raw_fourth_level_integral');
        $fifth_level_integral = empty(get_option('raw_fifth_level_integral')) ? 0 : (int)get_option('raw_fifth_level_integral');
        $result = 0;
        if ($first_level_integral == 0 || $second_level_integral == 0 || $third_level_integral == 0 || $fourth_level_integral == 0 || $fifth_level_integral == 0) {
            $result = 0;
        }
        if ($first_level_integral > $second_level_integral) {

            $result = 0;
        } else if ($second_level_integral > $third_level_integral) {
            $result = 0;
        } else if ($third_level_integral > $fourth_level_integral) {
            $result = 0;
        } else if ($fourth_level_integral > $fifth_level_integral) {
            $result = 0;
        } else {

            $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
            $sql = "update " . $wpdb->minapper_weixin_users . " 
            set `member`=case 
            when (integral>=  " . $first_level_integral . " and integral< " . $second_level_integral . ")  then '11'   
            when (integral>=  " . $second_level_integral . " and integral< " . $third_level_integral . ")  then '12'   
            when (integral>=  " . $third_level_integral . " and integral< " . $fourth_level_integral . ")  then '13' 
            when (integral>=  " . $fourth_level_integral . " and integral< " . $fifth_level_integral . ")  then '14' 
            when integral>=  " . $fifth_level_integral . "  then '15' 
            else '10' end ";
            if (!empty($flag) && $flag == "updateEndDateVip") {
                $sql .= "where `member` <>'00' and  `member` <>'02'  and userid=" . $userId;
            } else {
                $sql .= "where `member` <>'00' and `member` <>'01' and  `member` <>'02'  and userid=" . $userId;
            }

            $result = $wpdb->query($sql);
        }

        return $result;
    }

    public static function getMmeber($integral)
    {
        $member = '10';
        $first_level_integral = empty(get_option('raw_first_level_integral')) ? 0 : (int)get_option('raw_first_level_integral');
        $second_level_integral = empty(get_option('raw_second_level_integral')) ? 0 : (int)get_option('raw_second_level_integral');
        $third_level_integral = empty(get_option('raw_third_level_integral')) ? 0 : (int)get_option('raw_third_level_integral');
        $fourth_level_integral = empty(get_option('raw_fourth_level_integral')) ? 0 : (int)get_option('raw_fourth_level_integral');
        $fifth_level_integral = empty(get_option('raw_fifth_level_integral')) ? 0 : (int)get_option('raw_fifth_level_integral');
        if ($integral == 0 || $first_level_integral == 0 || $second_level_integral == 0 || $third_level_integral == 0 || $fourth_level_integral == 0 || $fifth_level_integral == 0) {
            $member = "10";
        } else {
            if (($integral >= $first_level_integral) && ($integral < $second_level_integral)) {

                $member = "11";
            } else if (($integral >= $second_level_integral) && ($integral < $third_level_integral)) {

                $member = "12";
            } else if (($integral >= $third_level_integral) && ($integral < $fourth_level_integral)) {

                $member = "13";
            } else if (($integral >= $fourth_level_integral) && ($integral < $fifth_level_integral)) {
                $member = "14";
            } else if ($integral >= $fifth_level_integral) {
                $member = "15";
            }
        }
        return $member;
    }

    public static function deleteAttachments($postId)
    {
        global $wpdb;
        $sql  = $wpdb->prepare("select t.id from " . $wpdb->posts . "  t where t.post_type='attachment' and t.post_parent=%d and t.post_status='inherit'", $postId);
        $count = 0;
        $fileIds = $wpdb->get_results($sql);
        $result = array();
        foreach ($fileIds as $fileId) {
            $id = (int)$fileId->id;
            $file = get_attached_file($id);
            $post = wp_delete_attachment($id, true);
            delete_post_meta($id, '_wp_attached_file');
            $attachment = wp_delete_post($id, true);
            if (!empty($file)) {
                if (file_exists($file)) {
                    wp_delete_file($file);
                }
                $count++;
            }
        }
        return $count;
    }

    public static function updateUserIntegral($userId, $num, $flag)
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = $wpdb->prepare("select count(1) FROM " . $wpdb->minapper_weixin_users . " WHERE  userid =%d", $userId);
        $count = (int)$wpdb->get_var($sql);
        if ($count != 1) {
            return true;
        }
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        if ($flag == 'add') {
            $sql = $wpdb->prepare("update  " . $wpdb->minapper_weixin_users . " set integral=integral+" . $num . " WHERE  userid=%d", $userId);
        } else if ($flag == 'minus') {
            $sql = $wpdb->prepare("update  " . $wpdb->minapper_weixin_users . " set integral=integral-" . $num . " WHERE  userid=%d", $userId);
        }

        $updateUserIntegralResult = $wpdb->query($sql);
        return $updateUserIntegralResult;
    }

    public static function checkUserIntegral($userId, $needIntegral)
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = $wpdb->prepare("select integral from " . $wpdb->minapper_weixin_users . " WHERE  userid=%d", $userId);
        $userintegral = (int) $wpdb->get_var($sql);
        if ($userintegral >= $needIntegral) {
            return true;
        } else {

            return false;
        }
    }

    public static function  inserIntegral($data_array)
    {
        global $wpdb;
        $userId = (int)$data_array['userid'];
        $result = 0;
        $integraltype = $data_array['integraltype'];
        $integral = (int)$data_array['integral'];
        $flag = $data_array['flag'];
        $checkMaxFlag = self::checkMaxIntegral($userId, $integraltype);
        if ($checkMaxFlag) {

            $wpdb->minapper_integral = $wpdb->prefix . 'minapper_integral';
            $inserResultId = $wpdb->insert($wpdb->minapper_integral, $data_array);

            if (!empty($inserResultId)) {

                $updateUserIntegralResult = self::updateUserIntegral($userId, $integral, $flag);
                if (empty($updateUserIntegralResult)) {
                    $result = -2;
                } else {
                    $result = 1;
                }
            } else {
                $result = 0;
            }
        } else {

            $result = -1;
        }
        return $result;
    }

    public static function checkMaxIntegral($userId, $integraltype)
    {
        global $wpdb;
        $result = false;
        if ($integraltype == "postIntegral" || $integraltype == "catsubscribeIntegral" || $integraltype == "depositIntegral" || $integraltype == "vipmemverIntegral" || $integraltype == "minappershop" || $integraltype == "minapperspecial") {
            return true;
        }
        $raw_user_max_integral = empty(get_option('raw_user_max_integral')) ? 0 : (int)get_option('raw_user_max_integral');
        if ($raw_user_max_integral == 0) {
            return true;
        }
        $wpdb->minapper_integral = $wpdb->prefix . 'minapper_integral';
        $sql = "SELECT
        sum(t.integral) as allIntegral
            FROM
                " . $wpdb->minapper_integral . " t
            WHERE
                t.userid = " . $userId . "
            AND DATE_FORMAT(t.updatedate, '%Y-%m-%d') = DATE_FORMAT(now(), '%Y-%m-%d')
            AND t.flag = 'add'
            AND (t.integraltype !='depositIntegral' and t.integraltype !='postIntegral' and t.integraltype !='catsubscribeIntegral')";
        $allIntegral = (int)$wpdb->get_var($sql);
        if ($allIntegral <= $raw_user_max_integral) {
            $result = true;
        }
        return $result;
    }

    public static function  checkUserByUserId($userId)
    {
        if (empty($userId) || !is_int($userId)) {
            return false;
        } else {
            global $wpdb;
            $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
            $sql = $wpdb->prepare("select count(1) FROM " . $wpdb->minapper_weixin_users . " WHERE  userid =%d", $userId);
            $count = (int)$wpdb->get_var($sql);
            if ($count != 1) {
                $sql = $wpdb->prepare("select count(1) FROM " . $wpdb->users . " WHERE  ID =%d", $userId);
                $count = (int)$wpdb->get_var($sql);
                if ($count != 1) {
                    return false;
                }
            }
        }
        return true;
    }

    public static function getRewordIntegral($rewordCode)
    {
        $rewordList = array();
        $reword['rewordCode'] = 'raw_newpost_integral';
        $reword['rewordName'] = '发表文章奖励';
        $reword['integral'] = get_option('raw_newpost_integral');
        $reword['deleteFlag'] = empty(get_option('raw_newpost_integral')) ? false : true;
        $rewordList[] = $reword;

        $reword['rewordCode'] = 'raw_comment_integral';
        $reword['rewordName'] = '文章评论奖励';
        $reword['integral'] = get_option('raw_comment_integral');
        $reword['deleteFlag'] = empty(get_option('raw_comment_integral')) ? false : true;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_like_integral');
        $reword['rewordName'] = '点赞奖励';
        $reword['rewordCode'] = 'raw_like_integral';
        $reword['deleteFlag'] = false;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_praise_integral');
        $reword['rewordCode'] = 'raw_praise_integral';
        $reword['rewordName'] = '赞赏奖励';
        $reword['deleteFlag'] = false;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_payment_integral');
        $reword['rewordCode'] = 'raw_payment_integral';
        $reword['rewordName'] = '支付奖励';
        $reword['deleteFlag'] = false;
        $rewordList[] = $reword;



        $reword['integral'] = get_option('raw_newtopic_integral');
        $reword['rewordCode'] = 'raw_newtopic_integral';
        $reword['rewordName'] = '发表新话题奖励';
        $reword['deleteFlag'] = empty(get_option('raw_newtopic_integral')) ? false : true;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_newtopic_location_integral');
        $reword['rewordCode'] = 'raw_newtopic_location_integral';
        $reword['rewordName'] = '发表新话题时开启位置信息奖励';
        $reword['deleteFlag'] = empty(get_option('raw_newtopic_location_integral')) ? false : true;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_replaytopic_integral');
        $reword['rewordCode'] = 'raw_replaytopic_integral';
        $reword['rewordName'] = '话题回复奖励';
        $reword['deleteFlag'] = empty(get_option('raw_replaytopic_integral')) ? false : true;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_readpost_integral');
        $reword['rewordName'] = '阅读文章奖励';
        $reword['rewordCode'] = 'raw_readpost_integral';
        $reword['deleteFlag'] = false;

        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_share_integral');
        $reword['rewordName'] = '分享小程序奖励';
        $reword['rewordCode'] = 'raw_share_integral';
        $reword['deleteFlag'] = false;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_openAd_integral');
        $reword['rewordName'] = '打开激励视频奖励';
        $reword['rewordCode'] = 'raw_openAd_integral';
        $reword['deleteFlag'] = false;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_sign_integral');
        $reword['rewordName'] = '签到奖励';
        $reword['rewordCode'] = 'raw_sign_integral';
        $reword['deleteFlag'] = false;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_phone_integral');
        $reword['rewordName'] = '绑定(更新)手机号奖励';
        $reword['rewordCode'] = 'raw_phone_integral';
        $reword['deleteFlag'] = false;
        $rewordList[] = $reword;

        $reword['integral'] = get_option('raw_invite_integral');
        $reword['rewordName'] = '邀请用户奖励';
        $reword['rewordCode'] = 'raw_invite_integral';
        $reword['deleteFlag'] = false;
        $rewordList[] = $reword;

        foreach ($rewordList as $_reword) {
            if ($rewordCode == $_reword['rewordCode']) {
                return $_reword;
                break;
            }
        }
        return null;
    }

    public static function operateRewordIntegral($userId, $rewordCode, $itemId = '', $itemName = '', $flag)
    {

        $result = 0;
        $rewordIntegral = self::getRewordIntegral($rewordCode);
        if (empty($rewordIntegral)) {
            $result = -1;
            return $result;
        }
        $checkUserByUserId = self::checkUserByUserId($userId);
        if (empty($checkUserByUserId)) {
            $result =  -2;
            return $result;
        }
        $integral = (int)$rewordIntegral['integral'];
        $deleteFlag = $rewordIntegral['deleteFlag'];
        $rewordCode = $rewordIntegral['rewordCode'];
        $rewordName = $rewordIntegral['rewordName'];
        if (!empty($integral) && $flag == 'add') {
            $data_array = array(
                'userid'   => $userId,
                'itemid'   => $itemId,
                'itemname' => $rewordName,
                'integraltype'  => $rewordCode,
                'integral' => $integral,
                'flag'    => $flag
            );

            $inserResult = RAW_Util::inserIntegral($data_array);
            if ($inserResult != 1) {
                $result =  -3;
            } else {
                $result =  $integral;
            }
        } else if (!empty($integral) && $deleteFlag && $flag == 'minus') {
            $checkUserIntegral = RAW_Util::checkUserIntegral($userId, $integral);
            if (!$checkUserIntegral) {
                $result = -4;
            } else {

                $data_array = array(
                    'userid'   => $userId,
                    'itemid'   => $itemId,
                    'itemname' => '撤销' . $rewordName,
                    'integraltype'  => $rewordCode,
                    'integral' => $integral,
                    'flag'    => $flag
                );
                $inserResult = RAW_Util::inserIntegral($data_array);
                if ($inserResult != 1) {
                    $result = -5;
                } else {
                    $result =  $integral;
                }
            }
        }

        return $result;
    }

    public static  function getforums()
    {
        global $wpdb;
        $sql = "SELECT  *  from " . $wpdb->posts . "  t where t.post_type='forum' and t.post_status='publish'";
        $forums = $wpdb->get_results($sql);
        return $forums;
    }


    /**
     * 
     * 中英混合的字符串截取
     */
    public static  function assoc_substr($str, $len, $charset = "utf-8")
    {
        //如果截取长度小于等于0，则返回空
        if (!is_numeric($len) or $len <= 0) {
            return "";
        }

        //如果截取长度大于总字符串长度，则直接返回当前字符串
        $sLen = strlen($str);
        if ($len >= $sLen) {
            return $str;
        }

        //判断使用什么编码，默认为utf-8
        if (strtolower($charset) == "utf-8") {
            $len_step = 3; //如果是utf-8编码，则中文字符长度为3  
        } else {
            $len_step = 2; //如果是gb2312或big5编码，则中文字符长度为2
        }

        //执行截取操作
        $len_i = 0;
        //初始化计数当前已截取的字符串个数，此值为字符串的个数值（非字节数）
        $substr_len = 0; //初始化应该要截取的总字节数

        for ($i = 0; $i < $sLen; $i++) {
            if ($len_i >= $len) break; //总截取$len个字符串后，停止循环
            //判断，如果是中文字符串，则当前总字节数加上相应编码的中文字符长度
            if (ord(substr($str, $i, 1)) > 0xa0) {
                $i += $len_step - 1;
                $substr_len += $len_step;
            } else { //否则，为英文字符，加1个字节
                $substr_len++;
            }
            $len_i++;
        }
        $result_str = substr($str, 0, $substr_len);
        return $result_str;
    }

    public static function  isAllChinese($str) {
        // 正则匹配检查：全部字符都在中文字符范围内（包含常用中文和扩展区域）
        return preg_match('/^[\x{4e00}-\x{9fa5}\x{3400}-\x{4dbf}\x{20000}-\x{2a6df}\x{2a700}-\x{2b73f}\x{2b740}-\x{2b81f}]+$/u', $str);
    }


    public static  function getCategoryIds()
    {
        switch ($apptype) {
            case "wx":
                $categoryIds = empty(get_option("wf_wx_display_categories")) ? "0" : get_option("wf_wx_display_categories");
                break;
            case "bd":
                $categoryIds = empty(get_option("wf_bd_display_categories")) ? "0" : get_option("wf_bd_display_categories");
                break;
            case "qq":
                $categoryIds = empty(get_option("wf_qq_display_categories")) ? "0" : get_option("wf_qq_display_categories");
                break;
            case "alipay":
                $categoryIds = empty(get_option("wf_alipay_display_categories")) ? "0" : get_option("wf_alipay_display_categories");
                break;
            case "tt":
                $categoryIds = empty(get_option("wf_tt_display_categories")) ? "0" : get_option("wf_tt_display_categories");
                break;
            default:
                $categoryIds = "0";
                break;
        }
        $arrCategoriesIds = array();
        if ($categoryIds != 0) {
            $catIds = explode(",", $categoryIds);
            foreach ($catIds as $catId) {
                //$cat['cid']=$catId;
                $arrCategoriesIds[] = (int)$catId;
                $category = get_category($catId);
                if (empty($category->parent)) {

                    $childCats = get_categories("child_of=" . $catId . "&hide_empty=0");
                    foreach ($childCats as $ccat) {
                        // $cat['cid']=$ccat->term_id;
                        $arrCategoriesIds[] = $ccat->term_id;
                    }
                } else {

                    $arrCategoriesIds[] = $catId;
                }
            }
            //去重
            $arrCategoriesIds = array_flip($arrCategoriesIds);
            $arrCategoriesIds = array_keys($arrCategoriesIds);
            $categoryIds = $arrCategoriesIds;
            //转换为字符串
            //$categoriesIds=implode(',',$arrCategoriesIds);

        } else {
            $categoryIds = explode(",", $categoryIds);
        }
        return $categoryIds;
    }

    public static function getSubscribeCount($sessionId, $userId, $extid, $subscribeMessageType)
    {
        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $checkUser = self::checkUser($sessionId, $userId);
        if ($checkUser) {

            $sql = "select t.extvalue from " . $wpdb->minapper_ext . "  t where  t.userid=" . $userId . "  and t.extype='subscribeMessage'  and t.extkey='" . $subscribeMessageType . "' and t.extid=" . $extid;
            $count = $wpdb->get_var($sql);
            if (empty($count)) {
                $count = 0;
            } else {
                $count = (int)$count;
            }
        } else {
            $count = 0;
        }

        return $count;
    }

    public static function  getUserNameAvatarurl($userId)
    {
        global $wpdb;
        
        $user = new class
        {
        };
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = $wpdb->prepare("SELECT avatarurl,nickname  FROM " . $wpdb->minapper_weixin_users . " WHERE userid = %d",$userId);
        $wxuser = $wpdb->get_row($sql);
        if (empty($wxuser)) {
            $minapper_avatar = get_user_meta((int)$userId, 'minapper_avatar', true);
            if (!empty($minapper_avatar)) {
                $avatarurl = $minapper_avatar;
            } else {
                $avatarurl = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
            }
            $user->avatarurl = $avatarurl; 
            $wpuser = get_user_by("id", (int)$userId);
            if(!empty($wpuser))
            {
                $user->nickname = $wpuser->display_name;   
            }
            else
            {
                $user->nickname = 'wordprss用户';   
            }
			  
            
        } else {
            $user->avatarurl = $wxuser->avatarurl;     
            $user->nickname = $wxuser->nickname;          
        }

        return $user;
    }
    public static function categorySubscribeMessage($author, $postid, $posttype, $title, $extkey, $extid,$id)
    {
        global $wpdb;
        $page = 'pages/detail/detail?id=' . $postid;
        if ($posttype == 'topic') {
            $page = 'pages/socialdetail/socialdetail?id=' . $postid;
        }

        $title = wp_filter_nohtml_kses($title);
        $title=RAW_Util::filterEmoji($title);
        $title = RAW_Util::assoc_substr($title, 17);
        $author=RAW_Util::filterEmoji($author);
        $author = RAW_Util::assoc_substr($author, 17);

        date_default_timezone_set('PRC');
        $datetime = date('Y-m-d H:i:s');
        if (empty($postid) || empty($posttype) || empty($title) || empty($author) || empty($extkey) || empty($extid)) {
            return new WP_Error('error', '发送订阅消息参数错误', array('status' => 500, 'postid' => $postid, 'posttype' => $posttype, 'title' => $title, 'author' => $author, 'extkey' => $extkey, 'extid' => $extid));
        }
        $templateId = get_option('raw_new_post_message_id');
        $data = array(
            "thing2" => array(
                "value" => $author
            ),
            "thing4" => array(
                "value" => $title
            ),
            "time3" => array(
                "value" => $datetime
            )
        );
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        // $sql="select 
        // min(tn.ID) as ID,
        // tn.openid,
        // tn.userid,
        // tn.extvalue,
        // tn.extkey          
        // from (";

        $sql = "SELECT
			t.ID,
			u.openid,
			t.userid,
			t.extvalue,
			t.extkey
			FROM
				" . $wpdb->minapper_ext . " t,
				" . $wpdb->minapper_weixin_users . " u
			WHERE
				t.extype = 'subscribeMessage'
			and t.extvalue !='0'
			AND t.extkey = '" . $extkey . "'
            AND t.userid = u.userid  and t.extid=" . $extid;

        $sql .= "  UNION         
        SELECT
        t.ID,
        u.openid,
        t.userid,
        t.extvalue,
        t.extkey
        FROM
            " . $wpdb->minapper_ext . " t,
            " . $wpdb->minapper_weixin_users . " u
        WHERE
            t.extype = 'subscribeMessage'
        and t.extvalue !='0'
        AND t.extkey = 'newcontent'
        AND t.userid = u.userid  ";

        // $sql .=" ) tn

        // GROUP by tn.userid ";


        $users = $wpdb->get_results($sql);
        $count = 0;
        $subscribeCount = "0";

        if (!empty($users)) {
            $result = self::sendSubscribeMessage($users, $templateId, $page, $data);
            if($result['error']=='0' && $result['count'] > 0)
            {
                $updatevalue = array(
                    'flag' => 1
                );
                $where  = array('ID' =>$id);
                $updataResult = $wpdb->update($wpdb->minapper_subscribe_cron,$updatevalue,$where);
            }           
        
        } else {
            $result = array(
                'error' => "no Subscribe",
                'message' => "未找到订阅者",
                'count' => 0

            );
        }
        // $result['data']=$data;				
        return $result;
    }

    public static function commentSubscribeMessage($author, $postid, $posttype, $title, $extkey, $extid)
    {
        $page = 'pages/detail/detail?id=' . $postid;
        if ($posttype == 'topic') {
            $page = 'pages/socialdetail/socialdetail?id=' . $postid;
        }
        if (empty($postid) || empty($posttype) || empty($title) || empty($author) || empty($extkey) || empty($extid)) {
            $result = array(
                'error' => "subscribe parameter error",
                'message' => "订阅消息参数错误",
                'count' => 0


            );
            return $result;
        }
        $post = get_post((int)$postid);
        $post_author = (int)$post->post_author;
        $title = wp_filter_nohtml_kses($title);
        $title = RAW_Util::assoc_substr($title, 17);
        $author = RAW_Util::assoc_substr($author, 17);
        date_default_timezone_set('PRC');
        $datetime = date('Y-m-d H:i:s');
        $templateId = get_option('raw_new_comment_message_id');
        $data = array(
            "thing2" => array(
                "value" => $title  //评论标题
            ),
            "time3" => array(
                "value" => $datetime //评论时间
            ),
            "thing4" => array(
                "value" => $author  //评论者
            )
        );
        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';

        $sql = "SELECT
            t.ID,
			u.openid,
			t.userid,
			t.extvalue,
            t.extkey
			FROM
				" . $wpdb->minapper_ext . " t,
				" . $wpdb->minapper_weixin_users . " u
			WHERE
				t.extype = 'subscribeMessage'
			and t.extvalue !='0'
			AND t.extkey = '" . $extkey . "'
            AND t.userid = u.userid ";
        $sql .= " and t.userid=" . $post_author . " and t.extid=" . $extid;

        $users = $wpdb->get_results($sql);
        $count = 0;
        if (!empty($users)) {
            $result = self::sendSubscribeMessage($users, $templateId, $page, $data);
        } else {
            $result = array(
                'error' => "no Subscribe",
                'message' => "未找到订阅者",
                'count' => 0

            );
        }

        // $result['data']=$data;		
        return $result;
    }
    public static function replaySubscribeMessage($author, $postid, $posttype, $replayToTitle, $extkey, $extid)
    {

        if ($posttype == 'topic') {
            $page = 'pages/socialdetail/socialdetail?id=' . $postid;
            $replay = get_post((int)$extid);
            $replayAuthoer = (int)$replay->post_author;
            $replayTitle = $replay->post_content; //动态评论内容           

        } else if ($posttype == "post") {
            $page = 'pages/detail/detail?id=' . $postid;
            $replay = get_comment($extid);
            $replayAuthoer = (int)$replay->user_id;
            $replayTitle = $replay->comment_content; //动态评论内容
        }

        if (empty($postid) || empty($posttype) || empty($replayToTitle) || empty($author) || empty($extkey) || empty($extid)) {
            $result = array(
                'error' => "subscribe parameter error",
                'message' => "订阅消息参数错误",
                'count' => 0

            );
            return $result;
        }
        $replayTitle = RAW_Util::assoc_substr($replayTitle, 17);
        $replayToTitle = RAW_Util::assoc_substr($replayToTitle, 17);
        date_default_timezone_set('PRC');
        $datetime = date('Y-m-d H:i:s');
        $templateId = get_option('raw_new_replay_message_id');
        $data = array(
            "thing1" => array(
                "value" => $replayTitle  //评论内容
            ),
            "thing2" => array(
                "value" => $replayToTitle //回复内容
            ),
            "thing3" => array(
                "value" => $author  //回复作者
            ),
            "time4" => array(
                "value" => $datetime  //回复时间
            ),
        );

        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';

        $sql = "SELECT
            t.ID,
			u.openid,
			t.userid,
			t.extvalue
			FROM
				" . $wpdb->minapper_ext . " t,
				" . $wpdb->minapper_weixin_users . " u
			WHERE
				t.extype = 'subscribeMessage'
			and t.extvalue !='0'
			AND t.extkey = '" . $extkey . "'
            AND t.userid = u.userid ";
        $sql .= " and t.userid=" . $replayAuthoer . " and t.extid=" . $extid;


        $users = $wpdb->get_results($sql);
        $count = 0;
        if (!empty($users)) {
            $result = self::sendSubscribeMessage($users, $templateId, $page, $data);
        } else {
            $result = array(
                'error' => "no Subscribe",
                'message' => "未找到订阅者",
                'count' => 0

            );
        }
        // $result['data']=$data;
        // $result['sql']=$sql;				
        return $result;
    }


    public static function sendSubscribeMessage($users, $templateId, $page, $data)
    {
        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $appid = get_option('raw_appid');
        $secret  = get_option('raw_secret');
        $count = 0;
        $error = "0";
        $subscribeCount = "0";
        // 添加调试信息
        error_log("sendSubscribeMessage开始: appid={$appid}, secret=" . (empty($secret) ? '空' : '已配置') . ", templateId={$templateId}, 用户数量=" . count($users));

        if (empty($secret) || empty($appid)) {
            $error = "secret or  appid is empty";
            $message = "secret 或 appid 为空";
            error_log("配置错误: secret或appid为空");
        } else {

            foreach ($users as $user) {
                $id = (int)$user->ID;
                $openid = $user->openid;
                $userid = $user->userid;
                $subscribeCount = $user->extvalue;
                $_count = (int)$subscribeCount;

                error_log("处理用户: userid={$userid}, openid={$openid}, subscribeCount={$subscribeCount}");
                if (!empty($user->extkey) && $user->extkey == 'vipEnddateSubscribe') {
                    $data = array(
                        "thing1" => array(
                            "value" => $user->nickname //会员昵称
                        ),
                        "date2" => array(
                            "value" => date('Y-m-d', strtotime($user->memberenddate)) //到期时间
                        ),
                        "thing3" => array(
                            "value" => '您的VIP会员将在7天后过期,请续费'  //备注
                        )
                    );
                }

                try{
                    error_log("发送订阅消息: openid={$openid}, templateId={$templateId}, page={$page}, data=" . json_encode($data));
                    $response = RAW()->wxapi->send_subscribeMessage($openid, $templateId, $page, $data);
                    error_log("微信API响应: " . json_encode($response));

                    $errorNo = isset($response['error']) ? $response['error'] : (isset($response['errcode']) ? $response['errcode'] : '1');
                    if ($errorNo == '0' || $errorNo == 0) {
                        $count++;
                        $_count--;
                        $subscribeCount = (string)$_count;
                        $sql =  "update  " . $wpdb->minapper_ext . " set extvalue='" . $subscribeCount . "' WHERE  extype='subscribeMessage' and ID=" . $id;
                        $updataResult = $wpdb->query($sql);
                        error_log("消息发送成功，更新订阅次数: {$subscribeCount}");
                    } else {
                        error_log("消息发送失败: errorNo={$errorNo}, response=" . json_encode($response));
                    }

                }
                catch(Exception $e){
                    error_log("发送订阅消息异常: " . $e->getMessage());
                }
               
            }
            $message = "订阅消息发送完成";
        }
        $result = array(
            'error' => $error,
            'message' => $message,
            'count' => $count,
            'errorNo' => $errorNo,
            // 'sql'=>$sql,
            // 'updataResult'=>$updataResult 
        );
        return $result;
    }


    public static  function do_schedule_send_post_subscribe()
    {
        global $wpdb;
        $count = 0;
        $wpdb->minapper_subscribe_cron = $wpdb->prefix.'minapper_subscribe_cron';
        $sql = "select * from " . $wpdb->minapper_subscribe_cron. " where extkey='categorySubscribe' and flag =0 order by updatedate";
        $schedules = $wpdb->get_results($sql);  
        $result=array();      
        if (!empty($schedules)) {
            foreach ($schedules as $schedule) {
                $id=$schedule->ID;
                $author = $schedule->author;
                $postId = (int)$schedule->postid;
                $posttype = $schedule->posttype;
                $title = $schedule->title;
                $extkey = $schedule->extkey;
                $extid = (int)$schedule->extid;                
                $result = self::categorySubscribeMessage($author, $postId, $posttype, $title, $extkey, $extid,$id);
                
            }
        }
        return $result;
    }


    public static  function delete_schedule_send_post_subscribe()
    {
        global $wpdb;       
        $wpdb->minapper_subscribe_cron = $wpdb->prefix.'minapper_subscribe_cron';
        $sql = "DELETE FROM " . $wpdb->minapper_subscribe_cron . " WHERE extkey='categorySubscribe' and flag =1 ";
        $result = $wpdb->query($sql);
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $sql = "DELETE FROM " .  $wpdb->minapper_ext . " WHERE extype='subscribeMessage' and extvalue ='0' ";
        $result = $wpdb->query($sql);
        return $result;
    }

    public static  function do_schedule_sendVipEnddateSubscribe()
    {
        $page = 'pages/myself/myself';
        $templateId = get_option('raw_vip_enddate_message_id');
        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';

        $sql = "SELECT
            t.ID,
			u.openid,
            u.nickname,
			t.userid,
			t.extvalue,
            u.memberenddate,
            t.extkey
			FROM
				" . $wpdb->minapper_ext . " t,
				" . $wpdb->minapper_weixin_users . " u
			WHERE
				t.extype = 'subscribeMessage'
			and t.extvalue !='0'
			AND t.extkey = 'vipEnddateSubscribe'
            AND t.userid = u.userid  
            and u.member='01' and  (DATEDIFF(u.memberenddate,NOW()) <=7)";
        $users = $wpdb->get_results($sql);
        if (!empty($users)) {
            $data = array();

            $result = self::sendSubscribeMessage($users, $templateId, $page, $data);
        } else {
            $result = array(
                'error' => "no Subscribe",
                'message' => "未找到订阅者",
                'count' => 0

            );
        }

        return $result;
    }

    public static  function insert_subscribe_cron($scheduleData)
    {
        global $wpdb;
		$wpdb->minapper_subscribe_cron = $wpdb->prefix.'minapper_subscribe_cron';
		$result = $wpdb->insert($wpdb->minapper_subscribe_cron, $scheduleData);
        return $result;
    }
    public static  function send_message($data_array)
    {
        global $wpdb;
        $wpdb->minapper_message = $wpdb->prefix . 'minapper_message';
        $insertMessage = $wpdb->insert($wpdb->minapper_message, $data_array);
        return $insertMessage;
    }

    public static   function getCardcode($current_page, $per_page, $s, $orderby, $order, $codetype)
    {
        global $wpdb;
        $wpdb->minapper_cardcode = $wpdb->prefix . 'minapper_cardcode';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "select t.code,t.ID,t.salemoney,t.saleintegral,t.salediscount,t.creatdate,t.enddate,t.usedate,u.nickname,t.objectid,t.orderid,t.userobject,
        case t.codetype WHEN 'money' then '现金直减' WHEN 'integral' then '积分充值' WHEN 'discount' then '折扣' end as codetypename,
        case t.status WHEN '0' then '未使用' WHEN '1' then '已使用'  end as statusname,
        case t.userobject WHEN 'all' then '全部付费内容' WHEN 'allcat' then '全部付费专题(分类)' WHEN 'allminapperspecial' then '全部付费专栏' WHEN 'minapperspecial' then '指定付费专栏' WHEN 'allpost' then '全部付费文章' WHEN 'specialcat' then '指定付费专题(分类)'  WHEN 'specialpost' then '指定付费文章' WHEN 'vip' then 'vip会员' end as userobjectname
        from " . $wpdb->minapper_cardcode . "  t left join " . $wpdb->minapper_weixin_users . " u  on  t.userid=u.userid  where 1=1 ";

        if (!empty($s)) {
            $sql .= " and t.code like '%" . $s . "%'";
        }

        //状态查询
        if (isset($_REQUEST['status']) && $_REQUEST['status'] != '') {
            $status = $_REQUEST['status'];
            $sql .= " and status='" . $status . "'";
        }



        if (!empty($codetype)) {
            $sql .= " and t.codetype='" . $codetype . "'";
        }
        if (!empty($orderby)) {
            $sql .= "  order by t." . $orderby . " ";

            if (!empty($order)) {
                $sql .= "   " . $order;
            }
        } else {
            $sql .= "  order by  t.ID desc";
        }

        if (!empty($per_page)) {
            $sql .= "    limit " . $current_page . "," . $per_page;
        }
        //var_dump($sql);
        $_cardcodes = $wpdb->get_results($sql, ARRAY_A);
        $cardcodes = array();
        if (!empty($_cardcodes)) {
            foreach ($_cardcodes as $c) {
                if ($c['userobject'] == "specialcat") {
                    $c['objectname'] = get_category((int)$c['objectid'])->name;
                }else if ($c['userobject'] == "minapperspecial") {

                    $category = get_term( (int)$c['objectid'], 'minapperspecialcategory' );
                    if ( $category ) {
                        $c['objectname'] = $category->name;
                    }
                    else {
                        $c['objectname'] = null;
                    }
                } 
                else if ($c['userobject'] == "specialpost") {

                    $post = get_post((int)$c['objectid']);
                    $c['objectname'] = (!empty($post)) ? $post->post_title : null;
                } else {
                    $c['objectname'] = null;
                }

                $cardcodes[] = $c;
            }

            return $cardcodes;
        } else {
            return null;
        }
    }


    public static function getCardcodecount($s, $codetype)
    {

        global $wpdb;
        $wpdb->minapper_cardcode = $wpdb->prefix . 'minapper_cardcode';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "select count(1) from  " . $wpdb->minapper_cardcode . "  t  where 1=1 ";

        if (!empty($s)) {
            $sql .= " and t.code like '%" . $s . "%'";
        }

        //状态查询
        if (isset($_REQUEST['status']) && $_REQUEST['status'] != '') {
            $status = $_REQUEST['status'];
            $sql .= " and status='" . $status . "'";
        }

        if (!empty($codetype)) {
            $sql .= " and t.codetype='" . $codetype . "'";
        }
        $cardcodeCount = (int)$wpdb->get_var($sql);
        return $cardcodeCount;
    }

    public static function updateCardcode($cardcode, $data)
    {
        global $wpdb;
        $wpdb->minapper_cardcode = $wpdb->prefix . 'minapper_cardcode';
        $where  = array('code' => $cardcode);
        $updateresult = $wpdb->update($wpdb->minapper_cardcode, $data, $where);
    }

    public static  function calculateByCardcode($cardcodeInfo, $product, $_objectid)
    {
        $price = empty($product->price) ? 0 : (float)$product->price;
        $originalprice = empty($product->originalprice) ? 0 : (int)$product->originalprice;
        $productype = $product->productype;



        $codetype = $cardcodeInfo->codetype;
        $userobject = $cardcodeInfo->userobject;
        $objectid = empty($cardcodeInfo->objectid) ? 0 : (int)$cardcodeInfo->objectid;
        $salemoney = empty($cardcodeInfo->salemoney) ? 0 : (float)$cardcodeInfo->salemoney;
        $salediscount = empty($cardcodeInfo->salediscount) ? 1 : number_format((float)$cardcodeInfo->salediscount / 10, 2);

        if ($codetype == "money") {
            $price = $price - $salemoney;
        } else if ($codetype == "discount") {
            $price = $price * $salediscount;
            $originalprice = $originalprice * $salediscount;
        }
        $price = number_format($price, 2);
        $originalprice = (int)$originalprice;

        if (empty($userobject)) {
            return $product;
        }
        switch ($userobject) {
            case "all":
                $product->price = $price;
                $product->originalprice = $originalprice;
                break;
            case "allcat":
                if ($productype == "catsubscribe") {
                    $product->price = $price;
                    $product->originalprice = $originalprice;
                }
                break;
            case "allpost":
                if ($productype == "postsubscribe" || $productype == "postpayment") {
                    $product->price = $price;
                    $product->originalprice = $originalprice;
                }
                break;

            case "specialcat":
                if ($productype == "catsubscribe") {
                    if ($objectid == $_objectid) {
                        $product->price = $price;
                        $product->originalprice = $originalprice;
                    }
                }
                break;
            case "specialpost":
                if ($productype == "postsubscribe" || $productype == "postpayment") {
                    if ($objectid == $_objectid) {
                        $product->price = $price;
                        $product->originalprice = $originalprice;
                    }
                }
                break;
        }
        return $product;
    }

    public static   function getPosts($current_page, $per_page, $s, $postype)
    {
        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $sql = "SELECT t.*, case t.post_type WHEN 'post' then '文章' WHEN 'topic' then '话题' end as posttypename,
        (select s.meta_value from " . $wpdb->postmeta . " s where s.post_id =t.id and s.meta_key='_minapperSearhDataPost') as searhDataPostCount,
        (select s5.meta_value from " . $wpdb->postmeta . " s5 where s5.post_id =t.id and s5.meta_key='_minapperContentPost') as minapperContentPost,
        (select s9.meta_value from " . $wpdb->postmeta . " s9 where s9.post_id =t.id and s9.meta_key='_minapperContentTestPost') as minapperContentTestPost,
        (select s6.meta_value from " . $wpdb->postmeta . " s6 where s6.post_id =t.id and s6.meta_key='_URLScheme') as URLScheme,
        (select s7.meta_value from " . $wpdb->postmeta . " s7 where s7.post_id =t.id and s7.meta_key='_URLLink') as URLLink,
        (select s8.meta_value from " . $wpdb->postmeta . " s8 where s8.post_id =t.id and s8.meta_key='_ShortLink') as ShortLink,
        (select case s2.meta_value WHEN '1' then '启用' ELSE  '未启用' END enableExcitation   from " . $wpdb->postmeta . " s2 where s2.post_id =t.id and s2.meta_key='_excitation' and t.post_type='post') as enableExcitation,
        (select case s3.meta_value WHEN '1' then '启用' ELSE  '未启用' END enableInterstitialAd   from " . $wpdb->postmeta . " s3 where s3.post_id =t.id and (s3.meta_key='_raw-enable-post-interstitial-ad' or s3.meta_key='_raw-enable-topic-interstitial-ad' )  ) as enableInterstitialAd,
        (select case s4.extvalue when '0' then '正在生成中,请稍后刷新此页面...'  else  replace(s4.extvalue,'http://','https://') end extvalue from " . $wpdb->minapper_ext . " s4 where s4.extid=t.id and s4.extype='wechatReceviceData'  and (s4.extkey='detail' or s4.extkey='socialdetail' )  order by s4.creatdate desc limit 1 ) as articleUrl
            FROM  " . $wpdb->posts;
        if (empty($postype)) {
            $sql .= " t  where (post_type='post' or post_type='topic') and post_status='publish' ";
        } else {
            $sql .= " t  where post_type='" . $postype . "' and post_status='publish' ";
        }

        if (!empty($s)) {
            $sql .= " and post_title like '%" . $s . "%'";
        }
        if (!empty($per_page)) {
            $sql .= "  order by post_date desc  limit " . $current_page . "," . $per_page;
        }
        $posts = $wpdb->get_results($sql, ARRAY_A);
        if (!empty($posts)) {
            return $posts;
        } else {
            return null;
        }
    }

    public static   function getPostsCount($s, $postype)
    {
        global $wpdb;
        $sql = "SELECT count(1) FROM  " . $wpdb->posts;

        if (empty($postype)) {
            $sql .= " t  where (post_type='post' or post_type='topic') and post_status='publish' ";
        } else {
            $sql .= " t  where post_type='" . $postype . "' and post_status='publish' ";
        }
        if (!empty($s)) {
            $sql .= " and post_title like '%" . $s . "%'";
        }
        $postsCount = (int)$wpdb->get_var($sql);
        return $postsCount;
    }


    public static function setOrder($cates)
    {
        $_categories = array();
        foreach ($cates as $category) {

            $order = get_term_meta($category->term_id, 'raw_catorder', true);

            if (!empty($order)) {
                $order = (int)$order;
            } else {
                $order = 0;
            }

            $category->order = $order;
            $_categories[] = $category;
        }


        usort($_categories, function ($oba, $obb) {
            if ($oba->order < $obb->order) {
                return 1;
            } elseif ($oba->order >= $obb->order) {
                return  -1;
            }
        });

        return $_categories;
    }


    public static function adjustShopProductCat($shopcats)
    {
        $shopcatList = array();
        $shopcovers = get_option('raw-shop-cover');
        foreach ($shopcats as $shopcat) {
            if ($shopcat['cat_level'] == 1) {
                $shopcover = '';
                if (!empty($shopcovers)) {
                    foreach ($shopcovers as $cover) {
                        if ($cover['id'] == $shopcat['shopcat_id']) {
                            $shopcover = $cover['category_thumbnail_image'];
                            break;
                        }
                    }
                }

                $page = 1;
                $page_size = 100;               
                $productList = array();
                $cateid=$shopcat['shopcat_id'];
                if (function_exists('MinapperShop')) {
                    $productList = Minapper_Shop_Util::getminiShopProducts($page, $page_size, $cateid);
                    $spus =array();
                    if(!empty($productList['spus']))
                    {
                        $spus =$productList['spus'];
                        $shopcat['spus']=$spus;
                    }

                    
                }
                $shopcat['category_thumbnail_image'] = $shopcover;

                $shopcat['children'] = self::getChildShopProductCat($shopcats, $shopcat['shopcat_id']);
                $shopcatList[] = $shopcat;
            }
        }
        return $shopcatList;
    }

    public static function getChildShopProductCat($shopcats, $shopcat_id)
    {
        $shopcatList = array();
        $shopcovers = get_option('raw-shop-cover');
        foreach ($shopcats as $shopcat) {
            if ($shopcat['f_shopcat_id'] == $shopcat_id) {

                $shopcover = '';
                if (!empty($shopcovers)) {

                    foreach ($shopcovers as $cover) {
                        if ($cover['id'] == $shopcat['shopcat_id']) {
                            $shopcover = $cover['category_thumbnail_image'];
                            break;
                        }
                    }
                }

                $shopcat['category_thumbnail_image'] = $shopcover;
                $shopcatList[] = $shopcat;
            }
        }

        return $shopcatList;
    }


    public  static function  get_raw_categories($categoryTpye, $request)
    {
        $categoriesId = '';
        $categories = array();
        if ($categoryTpye == 'raw_recommend_categories'  || $categoryTpye == 'raw_subscribe_categories' || $categoryTpye == 'raw_topic_categories'  || $categoryTpye == 'raw_display_categories') {
            $categoriesId = get_option($categoryTpye);
        } else if ($categoryTpye == 'minapper-module') {
            $minapper_module = get_option($categoryTpye);
            if (!empty($minapper_module['index_options']['articlelist']['categories'])) {
                $categories = $minapper_module['index_options']['articlelist']['categories'];
                if (is_array($categories) && count($categories) > 0) {
                    $categoriesId = implode(",", $categories);
                }
            }
        }


        $sessionId = isset($request['sessionid']) ? $request['sessionid'] : "";
        $userId = isset($request['userid']) ? (int)$request['userid'] : 0;

        global $wpdb;
        $wpdb->minapper_product = $wpdb->prefix . 'minapper_product';

        $args = array();
        if (empty($categoriesId)) {
            if ($categoryTpye == 'raw_recommend_categories') {
                // $args = array(
                //     'orderby' => 'id',
                //     'order' => 'ASC',                   
                //     'hide_empty' => false,
                //     'pad_counts' => false
                // );
                return $categories;
            } else {
                $args = array(
                    'orderby' => 'id',
                    'order' => 'ASC',
                    'parent' => 0,
                    'hide_empty' => false,
                    'pad_counts' => false
                );
            }
        } else {
            $args = array(
                'orderby' => 'id',
                'order' => 'ASC',
                'pad_counts' => false,
                'hide_empty' => false,
                'include' => $categoriesId
            );
        }

        $cates = get_categories($args);
        $_categories = RAW_Util::setOrder($cates);


        foreach ($_categories as $category) {
            $category_thumbnail_image = '';
            $temp = '';
            if ($temp = get_term_meta($category->term_id, 'raw_catcover', true)) {
                $category_thumbnail_image = $temp;
            }

            $catlogo = get_term_meta($category->term_id, 'raw_catlogo', true);
            $categoryId = $category->term_id;


            $sql = "select price,originalprice from " . $wpdb->minapper_product . " where productid=" . $categoryId . " and productype='catsubscribe'";
            $catYearPriceIntegral = $wpdb->get_row($sql);
            $catYearPrice = !empty($catYearPriceIntegral) ? $catYearPriceIntegral->price : "";
            $catYearIntegral = !empty($catYearPriceIntegral) ? $catYearPriceIntegral->originalprice : "";

            $data['catyearprice'] = $catYearPrice;
            $data['catYearIntegral'] = $catYearIntegral;



            $data['id'] = $category->term_id;
            $data['order'] = $category->order;


            $data['subscribemessagesid'] = get_option('raw_new_post_message_id');

            $data['categorySubscribeCount'] = RAW_Util::getSubscribeCount($sessionId, $userId, $categoryId, "categorySubscribe");

            $data['name'] = $category->name;
            $data['text'] = $category->name;

            $data['description'] = $category->description;
            $data['category_thumbnail_image'] = $category_thumbnail_image;
            $data['catlogo'] = $catlogo;
            $data['count'] = $category->count;

            if ($categoryTpye != 'raw_recommend_categories') {
                $childCategories = get_categories("child_of=" . $category->term_id . "&hide_empty=0");
                $childCategories = RAW_Util::setOrder($childCategories);
                $children_categories = RAW_Util::get_child_categories($category->term_id, $childCategories, $sessionId, $userId);
                $data['children'] = $children_categories;
                $children_categories_Ids = "";
                $children_categories_names = "";
                if (!empty($children_categories)) {
                    $children_categories_Ids = $category->term_id;
                    $children_categories_names = $category->name;
                    foreach ($children_categories as $children_category) {
                        $children_categories_Ids = $children_categories_Ids . "," . $children_category['id'];
                        $children_categories_names = $children_categories_names . "," . $children_category['name'];
                    }
                } else {
                    $children_categories_Ids = $category->term_id;
                    $children_categories_names = $category->name;
                }
                $data['ids'] = $children_categories_Ids;
                $data['names'] = $children_categories_names;
                $data['hasChildren'] = empty($data['children']) ? "0" : "1";
            }


            $payForCount = RAW_Util::getPaymentIntegralCount("catsubscribe", $category->term_id);
            $integralForCount = RAW_Util::getPaymentIntegralCount("catsubscribeIntegral
          ", $category->term_id);
            $data['payForCount'] = $payForCount;
            $data['integralForCount'] = $integralForCount;
            $categories[] = $data;
        }

        //$response = rest_ensure_response($categories); 
        return $categories;
    }

    public  static function get_child_categories($categoryId, $categories, $sessionId, $userId)
    {
        global $wpdb;
        $childCategories = array();
        $wpdb->minapper_product = $wpdb->prefix . 'minapper_product';
        foreach ($categories as $category) {

            if ($category->parent == $categoryId) {
                $catId = $category->term_id;
                $sql = "select price,originalprice from " . $wpdb->minapper_product . " where productid=" . $catId . " and productype='catsubscribe'";
                $catYearPriceIntegral = $wpdb->get_row($sql);
                $catYearPrice = !empty($catYearPriceIntegral) ? $catYearPriceIntegral->price : "";
                $catYearIntegral = !empty($catYearPriceIntegral) ? $catYearPriceIntegral->originalprice : "";
                $child['categorySubscribeCount'] = RAW_Util::getSubscribeCount($sessionId, $userId, $catId, 'categorySubscribe');
                $child['catyearprice'] = $catYearPrice;
                $child['subscribemessagesid'] = get_option('raw_new_post_message_id');
                $child['catYearIntegral'] = $catYearIntegral;
                $child['id'] = $category->term_id;
                $child['order'] = $category->order;
                $child['name'] = $category->name;
                $child['text'] = $category->name;
                $child['description'] = $category->description;
                $child['count'] = $category->count;
                $category_thumbnail_image = '';
                $temp = '';
                if ($temp = get_term_meta($category->term_id, 'raw_catcover', true)) {
                    $category_thumbnail_image = $temp;
                }
                $child['category_thumbnail_image'] = $category_thumbnail_image;
                $catlogo = get_term_meta($category->term_id, 'raw_catlogo', true);
                $child['catlogo'] = $catlogo;
                $children_categories = self::get_child_categories($category->term_id, $categories, $sessionId, $userId);
                $child['children'] = $children_categories;
                $children_categories_Ids = "";
                $children_categories_names = "";
                if (!empty($children_categories)) {
                    $children_categories_Ids = $category->term_id;
                    $children_categories_names = $category->name;
                    foreach ($children_categories as $children_category) {
                        $children_categories_Ids = $children_categories_Ids . "," . $children_category['id'];
                        $children_categories_names = $children_categories_names . "," . $children_category['name'];
                    }
                } else {
                    $children_categories_Ids = $category->term_id;
                    $children_categories_names = $category->name;
                }
                $child['ids'] = $children_categories_Ids;
                $child['names'] = $children_categories_names;
                $child['hasChildren'] = empty($child['children']) ? "0" : "1";

                $payForCount = RAW_Util::getPaymentIntegralCount("catsubscribe", $category->term_id);
                $integralForCount = RAW_Util::getPaymentIntegralCount("catsubscribeIntegral
          ", $category->term_id);
                $child['payForCount'] = $payForCount;
                $child['integralForCount'] = $integralForCount;

                $childCategories[] = $child;
            }
        }
        return $childCategories;
    }

    // 获取设备名称
    public static function get_device($ua, $deviceInfo)
    {
        $device = array();
        if (!empty($deviceInfo)) {
            $deviceInfo = strtolower($deviceInfo);
            list($platform, $brand, $model) = explode(';', $deviceInfo);
            $model = preg_replace('/\<.*\>|\(.*\)/U', '', $model);
            $model = trim($model);
            $device['model'] = strtolower($model);
            $device['platform'] = strtolower($platform);
            $device['brand'] = strtolower($brand);
        } else if (!empty($ua)) {
            if (preg_match('/wechatdevtools/i', $ua)) {
                $device['platform'] = 'wechatdevtools';
                $device['brand'] = 'unknown';
                $device['model'] = 'unknown';
            } elseif (preg_match('/MiniProgramEnv\/(\S+)/i', $ua, $match)) {
                $device['platform'] = $match[1] . 'Wechat';
                $device['brand'] = 'unknown';
                $device['model'] = 'unknown';
            }
        }
        if (empty($device)) {
            $device['platform'] = 'unknown';
            $device['brand'] = 'unknown';
            $device['model'] = 'unknown';
        }
        return $device;
    }

    // 获取设备ID
    public static function get_device_id($ua, $deviceInfo)
    {
        if (empty($deviceInfo)) {
            return '';
        }
        $device = self::get_device($ua, $deviceInfo);
        $deviceId = $device['platform'] . $device['brand'] . $device['model'];
        return md5($deviceId);
    }

    public static function insertWPUser($user_login, $first_name, $nickname, $user_nicename, $display_name, $user_email, $user_pass = null)
    {
        $new_user_data = apply_filters('new_user_data', array(
            'user_login'    => $user_login,
            'first_name'    => $first_name,
            'nickname'      => $nickname,
            'user_nicename' => $user_nicename,
            'display_name'  => $display_name,
            'user_pass'     => $user_pass,
            'user_email'    => $user_email
        ));

        $userId = wp_insert_user($new_user_data);
        if (is_wp_error($userId) || empty($userId) ||  $userId == 0) {
            $userId = 0;
        }
        return $userId;
    }

    public static function updateWPUser($ID, $first_name, $nickname, $user_nicename, $display_name)
    {
        $userdata = array(
            'ID'            => $ID,
            'first_name'    => $first_name,
            'nickname'      => $nickname,
            'user_nicename' => $user_nicename,
            'display_name'  => $display_name
        );

        $userId = wp_update_user($userdata);
        if (is_wp_error($userId)) {
            $userId = 0;
        }
        return $userId;
    }

    public static function insertMinapperUser($data_user, $data_session, $openId)
    {

        global $wpdb;
        $result = array();
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $insertUserResult = $wpdb->insert($wpdb->minapper_weixin_users, $data_user);
        if ($insertUserResult === false) {
            $result['errcode'] = -1;
            $result['errmsg'] = -"插入wp_minapper_weixin_users表错误";

            return $result;
        }
        if (!empty($data_session)) {
            $insertSessionResult = $wpdb->insert($wpdb->minapper_user_session, $data_session);
            if ($insertSessionResult === false) {
                $result['errcode'] = -2;
                $result['errmsg'] = -"插入wp_minapper_user_session表错误";
                return $result;
            }
        }

        //extkey- 邀请者的invitecode
        //extvalue-被邀请者的openid
        //userid -邀请者的userid
        //extid- 0 未被确认  1 被确认
        //extype -'invite' 扩展类型
        //extkey-invitecode 邀请码 
        $sql = $wpdb->prepare("SELECT extkey,(select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as inviteuserid , (select userid from " . $wpdb->minapper_weixin_users . " where invitecode=extkey) as userid FROM " . $wpdb->minapper_ext . " where extype='invite'  and extvalue=%s and extid=0", $openId);
        $inviteuser = $wpdb->get_row($sql);
        $invitecode = '';
        $inviteuserId = 0;
        $updateinvate = false;
        if (!empty($inviteuser)) {
            $invitecode = $inviteuser->extkey;
            $inviteuserId = $inviteuser->inviteuserid;
            if (!empty($invitecode) && !empty($inviteuserId)) {

                $where  = array('extkey' => $invitecode, 'extvalue' => $openId, 'extid'  => 0, 'extype' => 'invite');
                $data = array(
                    'extid' => 1,
                    'userid' => $inviteuserId
                );
                $updateinvate = $wpdb->update($wpdb->minapper_ext, $data, $where);
            }
        }
        if (!empty($updateinvate) && !empty($inviteuserId)) {
            $operateRewordIntegral = RAW_Util::operateRewordIntegral((int)$inviteuserId, 'raw_invite_integral', $commentId, '', 'add');
        }

        $result['errcode'] = 0;
        $result['errmsg'] = "插入wp_minapper_weixin_users和wp_minapper_user_session成功";
        return $result;
    }

    public static function updateUserSession($data_session, $userId, $deviceId)
    {
        global $wpdb;
        $result = array();
        $where  = array('userid' => $userId, 'deviceid' => $deviceId);
        $updateSessionResult = $wpdb->update($wpdb->minapper_user_session, $data_session, $where);
        if ($updateSessionResult === false) {
            $result['errcode'] = -3;
            $result['errmsg'] = "更新wp_minapper_user_session表错误";
            return $result;
        }

        $result['errcode'] = 0;
        //$result['ee']=$openId;
        $result['errmsg'] = 'ok';
        return $result;
    }

    public static function updateMinapperUser($data_user, $data_session, $openId, $userId, $deviceId, $haveSessionData)
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';

        $where  = array('openid' => $openId);
        $updateUserResult = $wpdb->update($wpdb->minapper_weixin_users, $data_user, $where);
        $result = array();
        if ($updateUserResult == false) {
            $result['errcode'] = -1;
            $result['errmsg'] = "更新wp_minapper_weixin_users表错误" . $updateUserResult;
            return $result;
        }
        if (!empty($data_session)) {
            if (!$haveSessionData) {

                $insertSessionResult = $wpdb->insert($wpdb->minapper_user_session, $data_session);
                if ($insertSessionResult === false) {
                    $result['errcode'] = -2;
                    $result['errmsg'] = "插入wp_minapper_user_session表错误";
                    return $result;
                }
            } else {
                $where  = array('userid' => $userId, 'deviceid' => $deviceId);
                $updateSessionResult = $wpdb->update($wpdb->minapper_user_session, $data_session, $where);
                if ($updateSessionResult === false) {
                    $result['errcode'] = -3;
                    $result['errmsg'] = "更新wp_minapper_user_session表错误";
                    return $result;
                }
            }
        }

        $result['errcode'] = 0;
        //$result['ee']=$openId;
        $result['errmsg'] = 'ok';
        return $result;
    }

    public static function getPaymentIntegralCount($extkey, $id)
    {
        global $wpdb;
        $wpdb->minapper_order = $wpdb->prefix . 'minapper_order';
        $sql = "select count(1) from " . $wpdb->minapper_order . " t where t.status=1";
        switch ($extkey) {
            case "postpayment":
                $sql .= "   and  (t.ordertype='postpayment'  or t.ordertype='postsubscribe')";
                break;
            case "catsubscribe":
                $sql .= "   and  t.ordertype='catsubscribe' ";
                break;
            case "catsubscribeIntegral":
                $sql .= "   and  t.ordertype='catsubscribeIntegral' ";
                break;
            case "postIntegral":
                $sql .= "   and  t.ordertype='postIntegral' ";
                break;
        }

        $sql .= "  and t.extid=" . $id;
        $count = (int)$wpdb->get_var($sql);
        return $count;
    }

    //截取字符
    public static  function utf8_strlen($string, $len)
    {
        if (empty($string)) {
            return $string;
        }
        // 将字符串分解为单元
        preg_match_all('/./us', $string, $match);
        // 返回单元个数
        $count = (int)count($match[0]);

        if ($count > $len) {
            $string = substr($string, 0, $len);
        }

        return $string;
    }

    //截取字符
    public static  function cc_msubstr($str, $start = 0, $length, $charset = "utf-8", $suffix = true)
    {
        if (function_exists("mb_substr"))
            return mb_substr($str, $start, $length, $charset);
        elseif (function_exists('iconv_substr')) {
            return iconv_substr($str, $start, $length, $charset);
        }
        $re['utf-8'] = "/[/x01-/x7f]|[/xc2-/xdf][/x80-/xbf]|[/xe0-/xef][/x80-/xbf]{2}|[/xf0-/xff][/x80-/xbf]{3}/";
        $re['gb2312'] = "/[/x01-/x7f]|[/xb0-/xf7][/xa0-/xfe]/";
        $re['gbk'] = "/[/x01-/x7f]|[/x81-/xfe][/x40-/xfe]/";
        $re['big5'] = "/[/x01-/x7f]|[/x81-/xfe]([/x40-/x7e]|/xa1-/xfe])/";
        preg_match_all($re[$charset], $str, $match);
        $slice = join("", array_slice($match[0], $start, $length));
        if ($suffix) return $slice . "…";
        return $slice;
    }

    public static function share_app($invitecode, $openId)
    {

        date_default_timezone_set('Asia/Shanghai');
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_logs = $wpdb->prefix . 'minapper_logs';
        $sql = $wpdb->prepare("select userid from " . $wpdb->minapper_weixin_users . " where invitecode=%s", $invitecode);
        $userId = (int)$wpdb->get_var($sql);
        $raw_share_count = empty(get_option("raw_share_count")) ? 0 : (int)get_option("raw_share_count");
        if (!empty($userId)) {
            $taskDate = date('Y-m-d', time());
            $meta_key = 'raw_user_shareapp_' . $taskDate;
            $raw_user_shareapp = (int)get_user_meta($userId, $meta_key, true);
            // $raw_user_shareapp_count=0;
            if (empty($raw_user_shareapp)) {

                $raw_user_shareapp = 1;
            } else {
                if (is_int($raw_user_shareapp)) {
                    $raw_user_shareapp = $raw_user_shareapp;
                } else {
                    $raw_user_shareapp = 1;
                }
                $raw_user_shareapp = $raw_user_shareapp + 1;
            }

            // $raw_user_shareapp=(string)$raw_user_shareapp_count;
            $result = true;
            $canShareApp = true;
            if ($raw_share_count != 0 && $raw_user_shareapp > $raw_share_count) {
                $canShareApp = false;
                return $canShareApp;
            }

            // if($raw_share_count !=0 && $raw_user_shareapp==$raw_share_count)
            // {
            // 	$canShareApp=false;

            // }


            if (!update_user_meta($userId, $meta_key, $raw_user_shareapp)) {
                $result = add_user_meta($userId, $meta_key, $raw_user_shareapp, true);
            }
            if ($result == false) {
                return false;
            }

            // $data_array = array(			
            // 	'extid'   => $id,
            // 	'extype'  => 'wechatReceviceData',
            // 	'extkey' => $pagetype,
            // 	'userid'   =>$msg_id,
            // 	'extvalue' =>'0',
            // 	'mark'=>$path

            // );
            // $insertResult =$wpdb->insert($wpdb->minapper_logs,$data_array);
            $operateRewordIntegral = RAW_Util::operateRewordIntegral($userId, 'raw_share_integral', '', '', 'add');
            return $operateRewordIntegral;
        } else {
            return false;
        }
    }

    // 是否微信小程序内访问
    public static function is_in_miniprogram()
    {
        return !empty($_SERVER['HTTP_USER_AGENT']) && !empty($_SERVER['HTTP_REFERER']) && preg_match('/servicewechat\.com/i', $_SERVER['HTTP_REFERER']);
    }

    // 根据用户ID登录对应用户
    public static function set_current_user($userId)
    {
        if (!is_numeric($userId)) {
            return false;
        }
        $userid = wp_set_current_user($userId);
        //wp_set_auth_cookie($userId);
        return $userid;
    }
    // 32位随机数
    public static function get_random()
    {
        return md5(uniqid(md5(microtime(true)), true));
    }


    public static function isPaypequired($postId)
    {

        //获取文章第一个分类
        $category = get_the_category($postId);
        $categoryId = empty($category) ? '' : (int)$category[0]->term_id;

        global $wpdb;
        $wpdb->minapper_product = $wpdb->prefix . 'minapper_product';
        $excitation = empty(get_post_meta($postId, '_excitation', true)) ? 0 : (int)get_post_meta($postId, '_excitation', true);



        $sql = "select count(1) from " . $wpdb->minapper_product . " where productid=" . $categoryId . " and productype='catsubscribe'";
        $catcount = $wpdb->get_var($sql);

        $sql = "select count(1) from " . $wpdb->minapper_product . " where productid=" . $postId . " and productype='postpayment'";
        $postcount = $wpdb->get_var($sql);

        if ($catcount == 1 || $postcount == 1 || $excitation == 1) {
            return true;
        }

        return false;
    }

    public static function  do_schedule_updateEndDateVip()
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "select userid from " . $wpdb->minapper_weixin_users . " where  member='01' and  ( TO_DAYS(memberenddate) -TO_DAYS(NOW()))<0";
        $users = $wpdb->get_results($sql);
        if (!empty($users)) {
            foreach ($users as $user) {
                $userId = (int)$user->userid;
                $updateUserMember = self::updateUserMember($userId, 'updateEndDateVip');
                update_user_meta($userId, 'raw_uer_vip', $updateUserMember);
            }
        }
    }

    public static function  do_schedule_delete_sessionexpire()
    {
        global $wpdb;
        $wpdb->minapper_user_session = $wpdb->prefix . 'minapper_user_session';
        $sql = "delete from " . $wpdb->minapper_user_session . " where  sessionexpire < DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
        $wpdb->query($sql);
    }


    public static function  get_posts($category, $request)
    {
        $_category = $category;
        $category = empty(get_option($category)) ? 0 : (int)get_option($category);

        $per_page = empty($request['per_page']) ? 10 : (int)$request['per_page'];
        $page = empty($request['page']) ? 1 : (int)$request['page'];

        $sessionId = isset($request['sessionid']) ? $request['sessionid'] : '';
        $userId = isset($request['userid']) ? (int)$request['userid'] : 0;

        $postype = isset($request['postype']) ? $request['postype'] : "post";
        $page = ($page - 1) * $per_page;

        $posts = array();
        if (empty($category) && $_category != "user_posts") {
            return new WP_Error('error', '无法获取此分类', array('status' => 200));
        } else {

            $args = array();
            if ($_category == "user_posts" && !empty($userId)) {
                $args = array(
                    'post_type' => array($postype), // 'post_type' => array('topic', 'reply')  加上reply就包括回复的动态
                    'posts_per_page' => $per_page,
                    'post_status' => array('publish'),
                    'offset' => $page,
                    'order' => 'DESC',
                    'orderby' => 'date',
                    'author' => $userId
                );
            } else {
                $args = array(
                    'post_type' => array('post'), // 'post_type' => array('topic', 'reply')  加上reply就包括回复的动态
                    'posts_per_page' => $per_page,
                    'post_status' => array('publish'),
                    'offset' => $page,
                    'order' => 'DESC',
                    'orderby' => 'date',
                    'cat' => $category
                );
            }

            $the_query = new WP_Query($args);
            $_posts = array();
            if ($the_query->have_posts()) {
                while ($the_query->have_posts()) {

                    $the_query->the_post();
                    $_posts[] = get_post(get_the_id());
                }
            } else {
                return new WP_Error('error', '最后一页了', array('status' => 200));
            }

            $posts = self::custom_posts($_posts, "all", $userId, $sessionId, $_category);
            //$response = rest_ensure_response($posts); 
            return $posts;
        }
    }

    public static function custom_posts($_posts, $showType, $userId, $sessionId, $category)
    {
        global $wpdb;
        $posts = array();
        if ($showType == "all") {
            foreach ($_posts as $post) {
                $data['id'] = $post->ID;
                $post_id = (int)$post->ID;
                $postformat = get_post_format($post_id)?:'standard';
                if ($postformat == 'postformatlink') {
                    $postformat = "link";
                }
                $data['format'] = $postformat;
                $channelsFeedId = empty(get_post_meta($post_id, '_channelsFeedId', true)) ? "" : get_post_meta($post_id, '_channelsFeedId', true);
                $data['channelsFeedId'] = $channelsFeedId;

                $channelsId = empty(get_post_meta($post_id, '_channelsId', true)) ? "" : get_post_meta($post_id, '_channelsId', true);
                $data['channelsId'] = $channelsId;

                $mpPostLink = empty(get_post_meta($post_id, '_mpPostLink', true)) ? "" : get_post_meta($post_id, '_mpPostLink', true);
                $data['mpPostLink'] = $mpPostLink;

                $unassociated = empty(get_post_meta($post_id, '_unassociated', true)) ? "" : get_post_meta($post_id, '_unassociated', true);
                $data['unassociated'] = $unassociated;

                $data['author'] = $post->post_author;
                $data['date'] = RAW_Util::time_tran($post->post_date);
                $data['post_date'] = RAW_Util::time_tran($post->post_date);
                //获取自定义扩展字段
                $expandField = empty(get_post_meta($post_id, '_expandField', true)) ? '' : get_post_meta($post_id, '_expandField', true);
                $expandField = self::removeEmptyExfeild($expandField);
                $data['expandField'] = $expandField;
                if (empty($post->post_excerpt)) {
                    $_excerpt['rendered'] = mb_strimwidth(wp_filter_nohtml_kses($post->post_content), 0, 150, '...');
                    $temp_excerpt = mb_strimwidth(wp_filter_nohtml_kses($post->post_content), 0, 150, '...');
                } else {
                    $_excerpt['rendered'] = wp_filter_nohtml_kses($post->post_excerpt);
                    $temp_excerpt = wp_filter_nohtml_kses($post->post_excerpt);
                }
                $data['excerpt'] = $_excerpt;

                $_temp_excerpt["rendered"] = $temp_excerpt . "(本文需要付费或观看激励视频方可阅读全文)";

                $excitation = empty(get_post_meta($post_id, '_excitation', true)) ? 0 : (int)get_post_meta($post_id, '_excitation', true);
                global $wpdb;
                $wpdb->minapper_product = $wpdb->prefix . 'minapper_product';
                $paypequired = "0";
                $sql = "select count(1) from " . $wpdb->minapper_product . " where productid=" . $post_id . " and productype='postpayment'";
                $count = (int)$wpdb->get_var($sql);
                if ($count == 1) {
                    $paypequired = "1";
                }



                if ($category != 'raw_billboard_category') {
                    $_content['rendered'] = $post->post_content;
                    if ($excitation != 1 &&  $paypequired != '1') {
                        $data['content'] = $_content;
                    } else {
                        $data['content'] = $_temp_excerpt;
                    }
                }




                $_title['rendered'] = $post->post_title;
                $data['title'] = $_title;
                $data['post_status'] = $post->post_status;

                $data['subscribemessagesid'] = get_option('raw_new_comment_message_id');
                if (!empty($sessionId)) {
                    $data['subscribeCount'] = RAW_Util::getSubscribeCount($sessionId, $userId, $post->ID, "postCommentSubscribe");
                } else {
                    $data['subscribeCount'] = "";
                }


                $data['like_count'] = RAW_Util::getLikeCount($post->ID);

                //获取评论的数量
                $total_comments = 0;
                if ($post->post_type == "post") {
                    $comments_count = wp_count_comments($post->ID);
                    $total_comments = $comments_count->approved;
                } else if ($post->post_type == "topic") {
                    global $wpdb;
                    $sql = $wpdb->prepare("SELECT  count(1)  from " . $wpdb->posts . " t where t.post_type='reply' and t.post_status='publish' and t.post_parent= %d ", $post->ID);
                    $total_comments = $wpdb->get_var($sql);
                }

                $data['total_comments'] = $total_comments;

                if ($category != 'raw_billboard_category') {
                    $data['tag_name'] = wp_get_post_tags($post->ID);
                    $images = RAW_Util::getPostImages($post->post_content, $post->ID);
                    $data['post_frist_image'] = $images['post_frist_image'];
                    $data['post_thumbnail_image'] = $images['post_thumbnail_image'];
                    $data['post_medium_image'] = $images['post_medium_image'];
                    $data['post_large_image'] = $images['post_large_image'];
                    $data['post_full_image'] = $images['post_full_image'];
                    $data['post_all_images'] = $images['post_all_images'];
                    $data['postcover'] = $images['postcover'];
                    $comments_count = wp_count_comments($post->ID);
                }



                //$data['total_comments']=$comments_count->approved;
                // $data['like_count']= RAW_Util::getLikeCount($post->ID);
                $pageviews = (int) get_post_meta($post->ID, 'raw_pageviews', true);
                $data['pageviews'] = $pageviews;
                $data['postType'] = $post->post_type;

                //获取作者头像和名称
                $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
                $sql = $wpdb->prepare("SELECT avatarurl  FROM " . $wpdb->minapper_weixin_users . " WHERE userid = %d", (int)$data['author']);
                $author_avatar = $wpdb->get_var($sql);
                if (empty($author_avatar)) {
                    $minapper_avatar = get_user_meta((int)$data['author'], 'minapper_avatar', true);
                    if (!empty($minapper_avatar)) {
                        $author_avatar = $minapper_avatar;
                    } else {
                        $author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
                    }
                }
                $data['author_avatar'] = $author_avatar;
                $data['author_name'] = get_the_author_meta('display_name', $data['author']);

                $posts[] = $data;
            }

            return $posts;
        } else if ($showType == "only") {
            $post = $_posts[0];
            $data['id'] = $post->ID;
            $post_id = (int)$post->ID;
            $postformat = get_post_format($post_id)?: 'standard';     
            if ($postformat == 'postformatlink') {
                $postformat = "link";
            }
            $data['format'] = $postformat;
            $channelsFeedId = empty(get_post_meta($post_id, '_channelsFeedId', true)) ? "" : get_post_meta($post_id, '_channelsFeedId', true);
            $data['channelsFeedId'] = $channelsFeedId;

            $channelsId = empty(get_post_meta($post_id, '_channelsId', true)) ? "" : get_post_meta($post_id, '_channelsId', true);
            $data['channelsId'] = $channelsId;

            $mpPostLink = empty(get_post_meta($post_id, '_mpPostLink', true)) ? "" : get_post_meta($post_id, '_mpPostLink', true);
            $data['mpPostLink'] = $mpPostLink;

            $unassociated = empty(get_post_meta($post_id, '_unassociated', true)) ? "" : get_post_meta($post_id, '_unassociated', true);
            $data['unassociated'] = $unassociated;

            $data['author'] = $post->post_author;
            $data['date'] = RAW_Util::time_tran($post->post_date);
            $data['post_date'] = RAW_Util::time_tran($post->post_date);
            $_content['rendered'] = $post->post_content;
            $data['content'] = $_content;
            $_title['rendered'] = $post->post_title;
            $data['title'] = $_title;

            //获取自定义扩展字段
            $expandField = empty(get_post_meta($post_id, '_expandField', true)) ? '' : get_post_meta($post_id, '_expandField', true);
            $expandField = self::removeEmptyExfeild($expandField);
            $data['expandField'] = $expandField;

            $data['like_count'] = RAW_Util::getLikeCount($post->ID);

            //获取评论的数量
            $comments_count = wp_count_comments($post->ID);
            $data['total_comments'] = $comments_count->approved;

            $data['tag_name'] = wp_get_post_tags($post->ID);

            if (empty($post->post_excerpt)) {
                $_excerpt['rendered'] = mb_strimwidth(wp_filter_nohtml_kses($post->post_content), 0, 150, '...');
            } else {
                $_excerpt['rendered'] = wp_filter_nohtml_kses($post->post_excerpt);
            }
            $data['excerpt'] = $_excerpt;
            $images = RAW_Util::getPostImages($post->post_content, $post->ID);
            $data['post_frist_image'] = $images['post_frist_image'];
            $data['post_thumbnail_image'] = $images['post_thumbnail_image'];
            $data['post_medium_image'] = $images['post_medium_image'];
            $data['post_large_image'] = $images['post_large_image'];
            $data['post_full_image'] = $images['post_full_image'];
            $data['post_all_images'] = $images['post_all_images'];
            $comments_count = wp_count_comments($post->ID);
            $data['total_comments'] = $comments_count->approved;
            $data['like_count'] = RAW_Util::getLikeCount($post->ID);
            $pageviews = (int) get_post_meta($post->ID, 'raw_pageviews', true);
            $data['pageviews'] = $pageviews;
            $data['postType'] = $post->post_type;

            //获取作者头像和名称
            $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
            $sql = $wpdb->prepare("SELECT avatarurl  FROM " . $wpdb->minapper_weixin_users . " WHERE userid = %d", (int)$data['author']);
            $author_avatar = $wpdb->get_var($sql);
            if (empty($author_avatar)) {
                $minapper_avatar = get_user_meta((int)$data['author'], 'minapper_avatar', true);
                if (!empty($minapper_avatar)) {
                    $author_avatar = $minapper_avatar;
                } else {
                    $author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
                }
            }
            $data['author_avatar'] = $author_avatar;
            $data['author_name'] = get_the_author_meta('display_name', $data['author']);

            return $data;
        }
    }


    public static function custom_post_fields($_data, $post, $request)
    {

        global $wpdb;
        // $_data = $data->data; 
        //$post_id = ( null === $post_id ) ? get_the_ID() : $post_id;
        $post_id = $post->ID;
        // $temp_content =get_the_content();

        $excitationOpened  = empty($request->get_header('excitationopened')) ? 0 : (int)$request->get_header('excitationopened');
        $params = $request->get_params(); //获取请求的参数

        $sessionId = isset($params['sessionid']) ? $params['sessionid'] : "";
        $userId = isset($params['userid']) ? (int)$params['userid'] : 0;

        $checkUser = RAW_Util::checkUser($sessionId, $userId);
        if (!$checkUser) {
            $sessionId = "";
            $userId = 0;
        }
        $_data['excitationOpened'] = $excitationOpened;
        $device  = empty($_SERVER['HTTP_MINAPPER_DEVICE']) ? '' : $_SERVER['HTTP_MINAPPER_DEVICE'];
        $_data['device'] = $device;

        $content = $_data['content']['rendered'];
        $sql = "select post_content,post_excerpt from " . $wpdb->posts . " where id=" . $post_id;
        $_postContent = $wpdb->get_row($sql);        
        $postContent=$_postContent->post_content;
        // update 2023-11-5 最新wp版本如果文章里的摘要为空，$_data['excerpt']['rendered']会返回正文内容，所以此处直接取数据的值
        $postExcerpt=$_postContent->post_excerpt;  
        $_c = $postContent;
        // if (has_shortcode($postContent, 'gallery')) //处理内容里的相册显示
        // {
        //     //$content = RAW_Util::get_content_gallery($postContent, true, true);
        // }

        // if(has_shortcode($postContent, 'playlist'))
        // {
        //     $content=RAW_Util::get_content_playlist($postContent,true);           
        // }
        $singlegoods = RAW_Util::get_content_singlegoods($postContent);
        $_data['singlegoods'] = $singlegoods;

        $minappershopsgoods = RAW_Util::get_content_minappershopsgoods($postContent);
        $_data['minappershopsgoods'] = $minappershopsgoods;


        $content_protected = $_data['content']['protected'];
        $raw = empty($_data['content']['raw']) ? '' : $_data['content']['raw'];

        $excerpt = $postExcerpt;
        $excerpt_protected = $_data['excerpt']['protected'];
        $payvideolink = empty(get_post_meta($post_id, '_payvideolink', true)) ? '' : get_post_meta($post_id, '_payvideolink', true);
        $payexcerpt = empty(get_post_meta($post_id, '_payexcerpt', true)) ? '' : get_post_meta($post_id, '_payexcerpt', true);

        $payForCount = RAW_Util::getPaymentIntegralCount("postpayment", $post_id);
        $integralForCount = RAW_Util::getPaymentIntegralCount("postIntegral", $post_id);

        $_data['payForCount'] = $payForCount;
        $_data['integralForCount'] = $integralForCount;


        //扩展字段
        $expandField = empty(get_post_meta($post_id, '_expandField', true)) ? '' : get_post_meta($post_id, '_expandField', true);
        $_data['expandField'] = $expandField;

        //获取文章的相关图

        if (has_shortcode($postContent, 'gallery')) //处理内容里的相册显示
        {
            $_c = RAW_Util::get_content_gallery($_c, true, false);
            $images = RAW_Util::getPostImages($_c, $post_id);
        } else {
            $images = RAW_Util::getPostImages($content, $post_id);
        }
        // $_data['post_frist_image']=$images['post_frist_image']; 
        $_data['post_thumbnail_image'] = $images['post_thumbnail_image'];
        $_data['post_medium_image'] = $images['post_medium_image'];
        $_data['post_large_image'] = $images['post_large_image'];
        $_data['post_full_image'] = $images['post_full_image'];
        $_data['post_all_images'] = $images['post_all_images'];
        $_data['postcover'] = $images['postcover'];

        //获取广告参数

        $videoAdId = empty(get_option('raw_video_ad_id')) ? '' : get_option('raw_video_ad_id');
        $_data['videoAdId'] = $videoAdId;
        $gridAdId = empty(get_option('raw_grid_ad_id')) ? '' : get_option('raw_grid_ad_id');
        $_data['gridAdId'] = $gridAdId;
        $bannerAdId = empty(get_option('raw_bannner_ad_id')) ? '' : get_option('raw_bannner_ad_id');
        $_data['bannerAdId'] = $bannerAdId;



        $expand = get_option('minapper_expand_settings_page');
        $listAd = '';
        $listAdEvery = '';
        $listAdtype = '';
        if (!empty($expand) && !empty($expand['post_list_ad'])) {
            $post_list_ad = $expand['post_list_ad'];
            $listAd = empty($post_list_ad['enable']) ? '0' : ($post_list_ad['enable'] == 'yes' ? "1" : "0");
            $listAdEvery = empty($post_list_ad['listAdEvery']) ? 9 : (int)$post_list_ad['listAdEvery'];
            $listAdtype = $post_list_ad['type'];
        }
        $_data['listAd'] = $listAd;
        //$_data['listAdId']=$listAdId;
        $_data['listAdEvery'] = $listAdEvery;
        $_data['listAdtype'] = $listAdtype;



        //获取评论的数量
        $comments_count = wp_count_comments($post_id);
        $_data['total_comments'] = $comments_count->approved;

        //获取文章第一个分类
        $category = get_the_category($post_id);
        $category_id = empty($category) ? '' : (int)$category[0]->term_id;
        $_data['category_id'] = $category_id;
        $_data['category_name'] = empty($category) ? '' : $category[0]->cat_name;

        $title = $_data['title']['rendered'];
        $title_raw = $title;
        $_title['rendered'] = html_entity_decode($title);
        $_title['raw'] = $title_raw; //古腾堡编辑器需要用到这个值
        $_data['title'] = $_title;

        //处理文章的时间
        $post_date = $post->post_date;
        $_data['post_date'] = RAW_Util::time_tran($post_date);

        //获取文章点赞数  
        $_data['like_count'] = RAW_Util::getLikeCount($post_id);

        //获取文章浏览数
        $pageviews = (int) get_post_meta($post_id, 'raw_pageviews', true);

        //获取提交页面搜索的次数
        $searhDataPostCount = (int) get_post_meta($post_id, '_minapperSearhDataPost', true);
        $_data['searhDataPostCount'] = $searhDataPostCount;

        //获取提交内容搜索的次数
        $searhContentPostCount = (int) get_post_meta($post_id, '_minapperContentPost', true);
        $_data['searhContentPostCount'] = $searhContentPostCount;

        $_data['raw_praise_word'] = empty(get_option('raw_praise_word')) ? "鼓励" : get_option('raw_praise_word');




        $_tags = isset($params['tags']) ? $params['tags'] : "";
        $tagscover = "../../images/uploads/tagsbgpic.jpg";
        if (is_array($_tags) && count($_tags) == 1) {
            $tagscover = get_term_meta($_tags[0], 'raw_tagcover', true);
        }
        $_data['tagscover'] = $tagscover;

        $postType = $post->post_type;
        if ($postType == 'page') {
            $_data['postType'] = 'page';
        } else {
            $_data['postType'] = 'post';
        }

        //显示公众号
        $fields = RAW_Util::getCustomFields(null, "post", 'weixinmp');
        if (!empty($fields)) {
            foreach ($fields as $field) {

                $fieldkey = $field['fieldkey'];
                $_data[$fieldkey] = get_post_meta($post_id, '_' . $fieldkey, true);
            }
        }




        $raw_default_videoposter_image = get_option('raw_default_videoposter_image');
        $_data['raw_default_videoposter_image'] = empty($raw_default_videoposter_image) ? "" : $raw_default_videoposter_image;
        if (!empty(get_post_meta($post_id, '_video_poster', true))) {
            $_data['raw_default_videoposter_image'] = get_post_meta($post_id, '_video_poster', true);
        }


        $channelsFeedId = empty(get_post_meta($post_id, '_channelsFeedId', true)) ? "" : get_post_meta($post_id, '_channelsFeedId', true);
        $_data['channelsFeedId'] = $channelsFeedId;

        $channelsId = empty(get_post_meta($post_id, '_channelsId', true)) ? "" : get_post_meta($post_id, '_channelsId', true);
        $_data['channelsId'] = $channelsId;

        $mpPostLink = empty(get_post_meta($post_id, '_mpPostLink', true)) ? "" : get_post_meta($post_id, '_mpPostLink', true);
        $_data['mpPostLink'] = $mpPostLink;
        $unassociated = empty(get_post_meta($post_id, '_unassociated', true)) ? "" : get_post_meta($post_id, '_unassociated', true);
        $_data['unassociated'] = $unassociated;

        
        if ($_data['format'] == "postformatlink") {
            $_data['format'] = "link";
        }

        if (isset($params['id'])) {

            $readedFlag = false;

            $post_year =date('Y',strtotime($post_date));
            $post_month =date('m',strtotime($post_date));
            $post_day =date('d',strtotime($post_date));
            $history_post_single = self::get_history_post_list($post_year,$post_month,$post_day);
            $_data['history_post_single']=$history_post_single;
        

            $wpdb->minapper_logs = $wpdb->prefix . 'minapper_logs';
            if (!empty($userId) && !empty($sessionId)) {
                $sql = $wpdb->prepare("SELECT count(1)  FROM " . $wpdb->minapper_logs . " WHERE logtype='readpostPntegral' and userid = %d and objectid=%s", $userId, $post_id);
                $readCount = (int)$wpdb->get_var($sql);
                if ($readCount > 0) {
                    $readedFlag = true;
                }
            }
            $_data['readedFlag'] = $readedFlag;


            // 是否启用视频前贴广告
            $enablePreMovieAd = empty(get_post_meta($post_id, '_raw-enable-post-pre-movie-ad', true)) ? 0 : (int)get_post_meta($post_id, '_raw-enable-post-pre-movie-ad', true);
            $preMovieAdId = empty(get_option('raw_pre_movie_ad_id')) ? '' : get_option('raw_pre_movie_ad_id');
            if ($enablePreMovieAd == 1 && !empty($preMovieAdId)) {
                $content = RAW_Util::set_video_pre_ad_id($content, $preMovieAdId);
            }

            // 是否启插屏广告
            $enablePostInterstitialAd = empty(get_post_meta($post_id, '_raw-enable-post-interstitial-ad', true)) ? 0 : (int)get_post_meta($post_id, '_raw-enable-post-interstitial-ad', true);
            $_data['enablePostInterstitialAd'] = $enablePostInterstitialAd;


            //获取百度网盘信息
            $baiduPanlink = empty(get_post_meta($post_id, '_baiduPanlink', true)) ? '' : get_post_meta($post_id, '_baiduPanlink', true);
            $baiduPankey = empty(get_post_meta($post_id, '_baiduPankey', true)) ? '' : get_post_meta($post_id, '_baiduPankey', true);
            $baiduPancode = '';
            if (!empty($baiduPanlink)) {
                $_baiduPanlink = explode('/', $baiduPanlink);
                $baiduPancode = is_array($_baiduPanlink) ? $_baiduPanlink[count($_baiduPanlink) - 1] : '';
            }


            //获取推荐商品（微信小商店和小程序商品）
            $_data['recommendGoods'] = self::getRecommendGoods($post_id);

            //获取推荐商品（微信小店商品）
            $_data['recommendWechatShopGoods'] = self::getRecommendWechatShopGoods($post_id);



            //获取文章的标签      
            $_data['tag_name'] = wp_get_post_tags($post_id);

            //获取上传视频
            $videoUrl = empty(get_post_meta($post_id, '_videoUrl', true)) ? "" : get_post_meta($post_id, '_videoUrl', true);



            $video_poster = empty(get_post_meta($post_id, '_video_poster', true)) ? get_option('raw_default_videoposter_image') : get_post_meta($post_id, '_video_poster', true);
            $_data['video_poster'] = $video_poster;

            //获取上一篇下一篇    
            $next_post = get_next_post();
            $previous_post = get_previous_post();
            $nextPostId = !empty($next_post->ID) ? $next_post->ID : null;
            $previousPostId = !empty($previous_post->ID) ? $previous_post->ID : null;
            $_data['next_post_id'] = $nextPostId;
            $_data['next_post_title'] = !empty($next_post->post_title) ? $next_post->post_title : null;
            $_data['next_post_thumbnail_image'] = "";
            if (!empty($nextPostId)) {
                $nextPost = get_post($nextPostId);
                $nextPostContent = $nextPost->post_content;
                $nextPostImages = RAW_Util::getPostImages($nextPostContent, $nextPostId);
                $_data['next_post_thumbnail_image'] = $nextPostImages['post_thumbnail_image'];
            }

            $_data['previous_post_thumbnail_image'] = "";
            $_data['previous_post_id'] = $previousPostId;
            $_data['previous_post_title'] = !empty($previous_post->post_title) ? $previous_post->post_title : null;

            if (!empty($previousPostId)) {
                $previousPost = get_post($previousPostId);
                $previousPostContent = $previousPost->post_content;
                $previousPostImages = RAW_Util::getPostImages($previousPostContent, $previousPostId);
                $_data['previous_post_thumbnail_image'] = $previousPostImages['post_thumbnail_image'];
            }

            //获取可以评论的最低会员星级
            $min_comment_user_member = empty(get_option("raw_min_comment_user_member")) ? 11 : (int)get_option("raw_min_comment_user_member");
            $_data['min_comment_user_member'] = $min_comment_user_member;
            $min_comment_user_memberName = RAW_Util::getMemberName($min_comment_user_member);
            $_data['min_comment_user_memberName'] = $min_comment_user_memberName;

            $fields = RAW_Util::getCustomFields(null, "post", 'goods');
            if (!empty($fields)) {
                foreach ($fields as $field) {

                    $fieldkey = $field['fieldkey'];
                    $_data[$fieldkey] = get_post_meta($post_id, '_' . $fieldkey, true);
                }
            }




            //解析音频
            $audios =  RAW_Util::get_post_content_audio($content);

            $vcontent = RAW_Util::get_post_qq_video($content);
            if (!empty($vcontent)) {
                $content = $vcontent;
            }

            //显示自定义腾讯视频
            // $fields = RAW_Util::getCustomFields(null, "post", 'video');
            $videoList  = array();         

            $vid = get_post_meta($post_id, '_raw_video_vid1', true);
            if (!empty($vid)) {
                $videodata['videokey'] = 'raw_video_vid1';
                $videodata['vid'] = $vid;
                $videoList[] = $videodata;
                $qqIframe = '<iframe src="https://v.qq.com/txp/iframe/player.html?vid=' . $vid . '" width="100%" height="500px" frameborder="0" allowfullscreen="allowfullscreen"></iframe>';
                $content .= $qqIframe;
            }

            //点赞头像
            $_data['avatarurls'] = RAW_Util::getLikeAvatarurl($post_id);

            //获取评论回复订阅消息id
            $new_replay_message_id = empty(get_option('raw_new_replay_message_id')) ? '' : get_option('raw_new_replay_message_id');
            $_data['new_replay_message_id'] = $new_replay_message_id;
            //获取广告参数


            $post_detail_ad = $expand['post_detail_ad'];
            $_data['preMovieAdId'] = $preMovieAdId;
            $_data['enablePreMovieAd'] = $enablePreMovieAd;


            $detailAd = empty($post_detail_ad['enable']) ? '0' : ($post_detail_ad['enable'] == 'yes' ? '1' : '0');
            $_data['detailAd'] = $detailAd;
            $detailAdtype = $post_detail_ad['type'];
            $_data['detailAdtype'] = $detailAdtype;
            $excitationAdId = empty(get_option('raw_excitation_ad_id')) ? '' : get_option('raw_excitation_ad_id');
            $_data['excitationAdId'] = $excitationAdId;


            //获取自定义扩展字段
            $expandField = empty(get_post_meta($post_id, '_expandField', true)) ? '' : get_post_meta($post_id, '_expandField', true);
            $expandField = self::removeEmptyExfeild($expandField);
            //浏览数
            $pageviews = $pageviews + 1;
            RAW_Util::updatePageviews($pageviews, $post_id);



            //猜你喜欢
            $_data['related_posts'] = RAW_Util::getRelatedPosts($_data["tags"], $post_id, $category_id, $_data['format']);

            //版权声明
            $copyright_state = get_option('raw_copyright_state');
            $_data['copyright_state']  = $copyright_state;

            //是否允许赞赏
            $raw_zan_display  = get_option('raw_zan_display');
            $_data['zan_display'] = empty($raw_zan_display) ? "0" : "1";

            //是否允许评论
            $raw_enable_comment_option  = get_option('raw_enable_comment_option');
            $_data['enableComment'] = empty($raw_enable_comment_option) ? "0" : "1";

            //是否是企业主体的小程序
            $raw_enterprise_minapp  = get_option('raw_enterprise_minapp');
            $_data['enterpriseMinapp'] = empty($raw_enterprise_minapp) ? "0" : "1";

            //赞赏的图
            $praiseimgurl = empty(get_option('raw_praiseimgurl')) ? '' : get_option('raw_praiseimgurl');
            $_data['praiseimgurl'] = $praiseimgurl;
            //获取我的点赞
            if (!empty($userId) && !empty($sessionId)) {
                $_data['mylike'] = RAW_Util::getMylike($userId, $post_id);
            } else {
                $_data['mylike'] = "0";
            }


            //获取文章的付费信息
            if ($postType == 'post') {

                $memberUser = RAW_Util::getMemberUserbySessionId($sessionId, $userId);
                $excitation = 0;
                if (!empty($memberUser)) {
                    $member = $memberUser->member;
                    if ($member != "00" &&  $member != "01") {
                        $excitation = empty(get_post_meta($post_id, '_excitation', true)) ? 0 : (int)get_post_meta($post_id, '_excitation', true);
                    }
                } else {

                    $excitation = empty(get_post_meta($post_id, '_excitation', true)) ? 0 : (int)get_post_meta($post_id, '_excitation', true);
                }
                $_data['excitationAd'] = $excitation;


                $wpdb->minapper_product = $wpdb->prefix . 'minapper_product';
                $catYearPrice = "";
                $catYearIntegral = "";
                $postPrice = "";
                $postIntegral = "";
                if (!empty($category_id)) {

                    $sql = "select price,originalprice from " . $wpdb->minapper_product . " where productid=" . $category_id . " and productype='catsubscribe'";
                    $catYearPriceIntegral = $wpdb->get_row($sql);
                    $catYearPrice = !empty($catYearPriceIntegral) ? $catYearPriceIntegral->price : "";
                    $catYearIntegral = !empty($catYearPriceIntegral) ? $catYearPriceIntegral->originalprice : "";


                    $sql = "select price,originalprice from " . $wpdb->minapper_product . " where productid=" . $category_id . " and productype='postsubscribe'";
                    $postPriceIntegral = $wpdb->get_row($sql);
                    $postPrice = !empty($postPriceIntegral) ? $postPriceIntegral->price : "";
                    $postIntegral = !empty($postPriceIntegral) ? $postPriceIntegral->originalprice : "";
                }
                $_postPrice = "";
                $_postIntegral = "";

                if (!empty($post_id)) {
                    $sql = "select price,originalprice from " . $wpdb->minapper_product . " where productid=" . $post_id . " and productype='postpayment'";
                    $postPriceIntegral = $wpdb->get_row($sql);
                    $_postPrice = !empty($postPriceIntegral) ? $postPriceIntegral->price : "";
                    $_postIntegral = !empty($postPriceIntegral) ? $postPriceIntegral->originalprice : "";
                }


                if (!empty($_postPrice)) {
                    $postPrice = $_postPrice;
                }

                if (!empty($_postIntegral)) {
                    $postIntegral = $_postIntegral;
                }

                $_data['catyearprice'] = $catYearPrice;
                $_data['catYearIntegral'] = $catYearIntegral;
                $_data['postprice'] = $postPrice;
                $_data['originalprice'] = empty($postIntegral) ? '' : $postIntegral;
                $excerpt = html_entity_decode($excerpt);
                if (strlen($excerpt) < 1) {
                    $excerpt = RAW_Util::rewrite_content($content, 200);
                }
                $_excerpt['rendered'] = $excerpt;
                $_excerpt['protected'] = $excerpt_protected; 

                $payexcerpt = empty($payexcerpt) ? $excerpt : $payexcerpt;
                $payexcerpt =do_shortcode($payexcerpt); //解析短代码
                //$_data['payexcerpt'] = $payexcerpt;
                $_data['payvideolink'] = empty($payvideolink) ? '' : $payvideolink;

                $pay_content = get_post_meta($post_id, '_pay_content', true);
                $pay_content =do_shortcode($pay_content); //解析短代码
                if (!empty($catYearPrice) || !empty($postPrice) || !empty($postIntegral) || !empty($catYearIntegral) ||  $excitation == 1) {

                    if ($excitation == 1) {
                        $_data['paypequired'] = '0';
                    } else {
                        $_data['paypequired'] = '1';
                    }
                    //未登录的用户无法看文章详情    
                    if (empty($sessionId) || empty($userId) || empty($memberUser)) {

                        $_content['rendered'] = $payexcerpt;
                        $_data['videoList'] = array();
                        $_data['audios'] = array();
                        $_data['videoUrl'] = array();
                        $_data['expandField'] = "";
                        $_data['baiduPanlink'] = "";
                        $_data['baiduPancode'] = "";
                        $_data['baiduPankey'] = "";
                    } else {
                        //付费会员和普通会员需要检测是否付费
                        if ($_data['paypequired'] == '1' && ($member == "10" || $member == "11" || $member == "12" || $member == "13" || $member == "14" || $member == "15")) {
                            //专题订阅或单篇付费记录
                            $wpdb->minapper_order = $wpdb->prefix . 'minapper_order';
                            $sql = $wpdb->prepare("select count(1) from " . $wpdb->minapper_order . "  where userid=%d and status=1 and  ((extid=%d  and  ordertype='postsubscribe') or (extid=%d  and  ordertype='catsubscribe') or (extid=%d  and  ordertype='postpayment') or (extid=%d  and  ordertype='postIntegral') or (extid=%d  and  ordertype='catsubscribeIntegral'))", $userId, $post_id, $category_id, $post_id, $post_id, $category_id);
                            $count = (int)$wpdb->get_var($sql);
                            if ($count < 1) //没有购买的记录的付费会员和普通会员显示需要支付
                            {
                                $_content['rendered'] = $payexcerpt;
                                $_data['videoList'] = array();
                                $_data['audios'] = array();
                                $_data['videoUrl'] = array();
                                $_data['expandField'] = "";
                                $_data['baiduPanlink'] = "";
                                $_data['baiduPancode'] = "";
                                $_data['baiduPankey'] = "";
                            } else {
                                $_data['paypequired'] = '0';
                                $_content['rendered'] = $content.$pay_content;
                                $_data['videoList'] = $videoList;
                                $_data['audios'] = $audios;
                                $_data['videoUrl'] = $videoUrl;
                                $_data['expandField'] = $expandField;
                                $_data['baiduPanlink'] = $baiduPanlink;
                                $_data['baiduPancode'] = $baiduPancode;
                                $_data['baiduPankey'] = $baiduPankey;
                            }
                        }
                        //管理员和vip不需要
                        elseif (($_data['paypequired'] == '1' || $excitation == 1) && ($member == "00" || $member == "01")) {
                            $_data['paypequired'] = '0';
                            $_content['rendered'] = $content.$pay_content;
                            $_data['videoList'] = $videoList;
                            $_data['audios'] = $audios;
                            $_data['videoUrl'] = $videoUrl;
                            $_data['expandField'] = $expandField;
                            $_data['baiduPanlink'] = $baiduPanlink;
                            $_data['baiduPancode'] = $baiduPancode;
                            $_data['baiduPankey'] = $baiduPankey;
                        }
                        //非vip和管理员用户无法查看包含激励视频的文章详情
                        elseif ($excitation == 1 && ($member == "10" || $member == "11" || $member == "12" || $member == "13" || $member == "14" || $member == "15")) {
                            if ($excitationOpened == 1) {
                                $_data['paypequired'] = '0';
                                $_content['rendered'] = $content.$pay_content;
                                $_data['videoList'] = $videoList;
                                $_data['audios'] = $audios;
                                $_data['videoUrl'] = $videoUrl;
                                $_data['expandField'] = $expandField;
                                $_data['baiduPanlink'] = $baiduPanlink;
                                $_data['baiduPancode'] = $baiduPancode;
                                $_data['baiduPankey'] = $baiduPankey;
                            } else {
                                $_data['paypequired'] = '0';
                                $_content['rendered'] = $payexcerpt;
                                $_data['videoList'] = array();
                                $_data['audios'] = array();
                                $_data['videoUrl'] = array();
                                $_data['expandField'] = "";
                                $_data['baiduPanlink'] = "";
                                $_data['baiduPancode'] = "";
                                $_data['baiduPankey'] = "";
                            }
                        }
                    }
                } else {
                    $_data['videoList'] = $videoList;
                    $_data['audios'] = $audios;
                    $_data['paypequired'] = '0';
                    $_content['rendered'] = $content;
                    $_data['videoUrl'] = $videoUrl;
                    $_data['expandField'] = $expandField;
                    $_data['baiduPanlink'] = $baiduPanlink;
                    $_data['baiduPancode'] = $baiduPancode;
                    $_data['baiduPankey'] = $baiduPankey;
                }


                $_content['raw'] = $raw; //古腾堡编辑器需要该属性，否则报错
                $_content['protected'] = $content_protected;
                $_data['content'] = $_content;
                $_data['excerpt'] =  $_excerpt;
               
            }
            //获取页面的自定义字段
            elseif ($postType == 'page') {

                $_data['videoList'] = $videoList;
                $_data['audios'] = $audios;

                $fields = RAW_Util::getCustomFields(null, 'page', 'business');
                foreach ($fields as $field) {
                    $fieldkey = $field['fieldkey'];
                    $_data[$fieldkey] = get_post_meta($post_id, '_' . $fieldkey, true);
                }
            }
        } else {
            unset($_data['content']);
            unset($_data['meta']);
            unset($_data['excerpt']);
            unset($_data['link']);
            unset($_data['guid']);
            unset($_data['date']);
            unset($_data['date_gmt']);
            unset($_data['modified']);
            unset($_data['modified_gmt']);
            unset($_data['status']);
            unset($_data['comment_status']);
            unset($_data['ping_status']);

            unset($_data['type']);
            //unset($_data['author'] );
            unset($_data['featured_media']);
            // unset($_data['tags'] );

        }
        $_data['pageviews'] = $pageviews;

        //获取作者头像和名称
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = $wpdb->prepare("SELECT avatarurl,`member`  FROM " . $wpdb->minapper_weixin_users . " WHERE userid = %d", (int)$_data['author']);
        $member = $wpdb->get_row($sql);
        if (empty($member)) {

            $minapper_avatar = get_user_meta((int)$_data['author'], 'minapper_avatar', true);
            if (!empty($minapper_avatar)) {
                $author_avatar = $minapper_avatar;
            } else {
                $author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
            }

            $member = "10";
        } else {
            $author_avatar = $member->avatarurl;
            $member = $member->member;
        }
        $_data['member'] = $member;
        $_data["membername"] = RAW_Util::getMemberName($member);
        $_data['author_avatar'] = $author_avatar;
        $_data['author_name'] = get_the_author_meta('display_name', $_data['author']);
        
        $zanImageUrl = get_user_meta((int)$_data['author'], 'zanimage', true);
        if (empty($zanImageUrl)) {
            $_data['author_zan_image'] = '';
        } else {
            $_data['author_zan_image'] = $zanImageUrl;
        }

        $follow = RAW_Util::getUserFollow($sessionId, $userId, $_data['author']);
        $_data['author_id'] = $_data['author'];
        $_data['follow'] = $follow;



        return $_data;
    }

    public static function removeEmptyExfeild($_expandField)
    {
        $expandField = array();
        if (!empty($_expandField)) {
            foreach ($_expandField as $field) {
                if (!empty($field['name']) && !empty($field['value'])) {
                    $expandField[] = $field;
                }
            }
        }

        return $expandField;
    }


    public static function  checkCardcode($cardcode)
    {
        global $wpdb;
        $wpdb->minapper_cardcode = $wpdb->prefix . 'minapper_cardcode';
        $sql = "select count(1) from " . $wpdb->minapper_cardcode . " t  where t.status='0' and t.code='" . $cardcode . "' and ( (t.enddate is  null) or (t.enddate is not null and  current_timestamp <t.enddate))";
        $count = $wpdb->get_var($sql);
        if ($count == 1) {
            return true;
        } else {
            return false;
        }
    }

    public static function  getCardcodeInfo($cardcode)
    {
        global $wpdb;
        $wpdb->minapper_cardcode = $wpdb->prefix . 'minapper_cardcode';
        $sql = $wpdb->prepare("select * from " . $wpdb->minapper_cardcode . " t  where t.status='0' and t.code=%s", $cardcode);
        $cardcode = $wpdb->get_row($sql);
        return $cardcode;
    }

    public static function getInvite($key, $flag)
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $sql = "SELECT
        u1.nickname,
        u1.userid,
        u1.phone,
        e.updatedate,  
       (select u2.nickname from  " . $wpdb->minapper_weixin_users . " u2 where u2.openid=e.extvalue) as inviteusername ,
       (select u3.userid from  " . $wpdb->minapper_weixin_users . " u3 where u3.openid=e.extvalue) as inviteuserid   
    FROM
        " . $wpdb->minapper_ext . " e
    LEFT JOIN " . $wpdb->minapper_weixin_users . " u1 ON
        u1.userid = e.userid
    WHERE
        e.`extype` = 'invite' AND e.extid = 1";

        if (!empty($key) && $flag == "nickname") {
            $sql .= " and u1.nickname like '%" . $key . "%'";
        }

        if (!empty($key) && $flag == "userid") {
            $sql .= " and u1.userid =" . $key;
        }



        $sql .= "  order by e.updatedate desc";

        $invitelist = $wpdb->get_results($sql, ARRAY_A);
        if (!empty($invitelist)) {
            return $invitelist;
        } else {
            return null;
        }
    }

    public static function getUserDespending($page, $per_page)
    {
        global $wpdb;
        $sql = "select *, '待审核' as despendingname from " . $wpdb->usermeta . " t where t.meta_key='despending' and t.meta_value='2' limit " . $page . "," . $per_page;
        $_userpending = $wpdb->get_results($sql, ARRAY_A);
        $userpending = array();
        if (!empty($_userpending)) {
            foreach ($_userpending as $u) {
                $userId = (int)$u['user_id'];
                $user = RAW_Util::getMemberUser($userId);
                $u['username'] = $user->nickname;
                $u['description'] = $user->description;
                $userpending[] = $u;
            }
            return $userpending;
        } else {
            return null;
        }
    }

    public static function getUserDespendingCount()
    {
        global $wpdb;
        $sql = "select count(1) from " . $wpdb->usermeta . " t where t.meta_key='despending' and t.meta_value='2' ";
        $count = $wpdb->get_var($sql);

        return $count;
    }

    public static function  getFormatShortLink($ShortLink, $pagetitle)
    {
        $raw_meta_name = get_option('raw_meta_name');
        $raw_webname = get_option('raw_webname');
        $minappname = !empty($raw_meta_name) ? $raw_meta_name : $raw_webname;
        if (!empty($ShortLink) && !empty($minappname) && !empty($pagetitle)) {
            $pagetitle = self::match_chinese($pagetitle);
            $ShortLinkcode = substr($ShortLink, strripos($ShortLink, '/') + 1);
            $ShortLink = "#小程序://" . $minappname . "/" . $pagetitle . '/' . $ShortLinkcode;
        }
        return $ShortLink;
    }

    public  static function match_chinese($chars, $encoding = 'utf8')
    {
        $pattern = ($encoding == 'utf8') ? '/[\x{4e00}-\x{9fa5}a-zA-Z0-9]/u' : '/[\x80-\xFF]/';
        preg_match_all($pattern, $chars, $result);
        $str = join('', $result[0]);
        return $str;
    }

    public static function  errorUserMessage()
    {
        return new WP_Error('user_parameter_error', "用户参数错误,重新登录", array('status' => 200));
    }


    public static function getRecommendGoods($post_id)
    {
        $minishopGoods = empty(get_post_meta($post_id, '_minishopGoods', true)) ? '' : get_post_meta($post_id, '_minishopGoods', true);
        $miniappGoods = empty(get_post_meta($post_id, '_miniappGoods', true)) ? '' : get_post_meta($post_id, '_miniappGoods', true);
       

        $recommendGoods = array();
        if (!empty($minishopGoods)) {

            foreach ($minishopGoods as $id) {
                $goods = array();
                $goods["type"] = "minishopGoods";
                $goods['redirecttype'] = "apppage";
                $goods["appid"] = "";
                if (!empty($id)) {

                    $data = array(
                        "product_id" => $id

                    );
                    $product = RAW()->wxapi->get_product($data);
                    if ($product['errcode'] == 0) {

                        $data = $product['data'];
                        $spu = $data['spu'];
                        $goods["title"] = $spu['title'];
                        $goods["path"] = "plugin-private://wx34345ae5855f892d/pages/productDetail/productDetail?productId=" . $id;
                        $goods["img"] = $spu['head_img'][0];
                        $goods["price"] = $spu['min_price'] / 100;
                    }
                }
                $recommendGoods[] = $goods;
            }
        }

        if (!empty($miniappGoods)) {
            $cnt = count($miniappGoods['appid']);
            for ($i = 0; $i < $cnt; $i++) {
                $goods = array();
                $goods["type"] = "miniappGoods";
                $goods['redirecttype'] = "miniapp";
                $goods["title"] = $miniappGoods['title'][$i];
                $goods["path"] = $miniappGoods['path'][$i];
                $goods["img"] = $miniappGoods['img'][$i];
                $goods["appid"] = $miniappGoods['appid'][$i];
                $goods["jumptype"] = $miniappGoods['jumptype'][$i];
                $goods["price"] = $miniappGoods['price'][$i];
                $recommendGoods[] = $goods;
            }
        }

        
        return $recommendGoods;
    }

    public static function getRecommendWechatShopGoods($post_id)
    {
       
        $wechatshopGoods = empty(get_post_meta($post_id, '_wechatshopGoods', true)) ? '' : get_post_meta($post_id, '_wechatshopGoods', true);

        $recommendGoods = array();
        if (!empty($wechatshopGoods)) {
            $cnt = count($wechatshopGoods['appid']);
            for ($i = 0; $i < $cnt; $i++) {
                $goods = array();
                $goods["type"] = "wechatshopGoods";
                $goods['redirecttype'] = "wechatshop";
                $goods["title"] = $wechatshopGoods['title'][$i];
                $goods["storeappid"] = $wechatshopGoods['appid'][$i];
                $goods["productid"] = $wechatshopGoods['productid'][$i];
                $goods["productpromotionlink"] = $wechatshopGoods['productpromotionlink'][$i];
                $recommendGoods[] = $goods;
            }
        }
        return $recommendGoods;
    }

    public static function insertMpsubscribeUser($data)
    {
        global $wpdb;
        $wpdb->minapper_mpsubscribe_user = $wpdb->prefix . 'minapper_mpsubscribe_user';
        $result = $wpdb->insert($wpdb->minapper_mpsubscribe_user, $data);
    }

    public static function insertMpUserTag($data)
    {
        global $wpdb;
        $wpdb->minapper_mpuser_tag = $wpdb->prefix . 'minapper_mpuser_tag';
        $result = $wpdb->insert($wpdb->minapper_mpuser_tag, $data);
    }

    public static function updateMpsubscribeUser($data, $openid)
    {
        global $wpdb;
        $wpdb->minapper_mpsubscribe_user = $wpdb->prefix . 'minapper_mpsubscribe_user';
        $where  = array('openid' => $openid);
        $result = $wpdb->update($wpdb->minapper_mpsubscribe_user, $data, $where);
        return $result;
    }



    public static function getMpFollowUsers($tagid)
    {
        global $wpdb;
        $wpdb->minapper_mpsubscribe_user = $wpdb->prefix . 'minapper_mpsubscribe_user';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_mpuser_tag = $wpdb->prefix . 'minapper_mpuser_tag';
        $sql = "select t.*,u.nickname as minappername, u.userid as userid,u.avatarurl from " . $wpdb->minapper_mpsubscribe_user . " t   LEFT JOIN " . $wpdb->minapper_weixin_users . " u ON u.unionid=t.unionid  LEFT JOIN " . $wpdb->minapper_mpuser_tag . " tg ON tg.openid=t.openid ";
        if (!empty($tagid)) {
            $sql .= " where  tg.tagid=" . $tagid;
        }
        $sql .= " group by t.openid  order by t.subscribe_time desc ";
        $mpsubscribe_user = $wpdb->get_results($sql, ARRAY_A);
        if (!empty($mpsubscribe_user)) {
            return $mpsubscribe_user;
        } else {
            return null;
        }
    }

    public static function getMpSubscribeUsers($tagid)
    {
        global $wpdb;
        $wpdb->minapper_mpsubscribe_user = $wpdb->prefix . 'minapper_mpsubscribe_user';
       
        if ($tagid != 0) {
            $wpdb->minapper_mpuser_tag = $wpdb->prefix . 'minapper_mpuser_tag';
            $sql = "select t.* from " . $wpdb->minapper_mpsubscribe_user . " t  LEFT JOIN " . $wpdb->minapper_mpuser_tag . " tg ON tg.openid=t.openid  where t.subscribe='1' ";
            $sql .= " and  tg.tagid=" . $tagid;
        }
        else {
            $sql = "select t.* from ". $wpdb->minapper_mpsubscribe_user. " t  where t.subscribe='1' ";
        }
        $sql .= "  GROUP by t.openid";
        $mpsubscribe_user = $wpdb->get_results($sql, ARRAY_A);
        return   $mpsubscribe_user;
    }




    public static function deleteMpFollowUsers()
    {
        global $wpdb;
        $wpdb->minapper_mpsubscribe_user = $wpdb->prefix . 'minapper_mpsubscribe_user';
        $sql = "select  count(1) from " . $wpdb->minapper_mpsubscribe_user;
        $count = (int)$wpdb->get_var($sql);
        if ($count == 0) {
            return  true;
        }
        $sql = "delete from " . $wpdb->minapper_mpsubscribe_user;
        $result = $wpdb->query($sql);
        return  $result;
    }

    public static function deleteMpUsersTag()
    {
        global $wpdb;
        $wpdb->minapper_mpuser_tag = $wpdb->prefix . 'minapper_mpuser_tag';
        $sql = "select  count(1) from " . $wpdb->minapper_mpuser_tag;
        $count = (int)$wpdb->get_var($sql);
        if ($count == 0) {
            return  true;
        }
        $sql = "delete from " . $wpdb->minapper_mpuser_tag;
        $result = $wpdb->query($sql);
        return  $result;
    }


    public static function isFollowUser($openid)
    {
        global $wpdb;
        $result = false;
        $wpdb->minapper_mpsubscribe_user = $wpdb->prefix . 'minapper_mpsubscribe_user';
        $sql = "select  count(1) from " . $wpdb->minapper_mpsubscribe_user . " where openid='" . $openid . "'";
        $count = (int)$wpdb->get_var($sql);
        if ($count == 1) {
            return  true;
        }
        return  $result;
    }

    public static function getFollowMeUserMPOpenid($userid)
    {
        global $wpdb;
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $wpdb->minapper_mpsubscribe_user = $wpdb->prefix . 'minapper_mpsubscribe_user';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "SELECT m.openid as openid,w.userid,w.nickname from " . $wpdb->minapper_ext . " t LEFT JOIN " . $wpdb->minapper_weixin_users . " w ON w.userid=t.userid LEFT JOIN " . $wpdb->minapper_mpsubscribe_user . " m on m.unionid=w.unionid where t.extype='follow' and m.subscribe='1' and t.extid =" . $userid;
        $openids = $wpdb->get_results($sql, ARRAY_A);
        return $openids;
    }

    public static function getMPOpenidByUserid($userid)
    {
        global $wpdb;
        $wpdb->minapper_mpsubscribe_user = $wpdb->prefix . 'minapper_mpsubscribe_user';
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "SELECT m.openid as openid from " . $wpdb->minapper_weixin_users . " w  LEFT JOIN " . $wpdb->minapper_mpsubscribe_user . " m on m.unionid=w.unionid where w.userid=" . $userid;
        $openid = $wpdb->get_results($sql, ARRAY_A);
        return $openid;
    }

    public static function getMedia($mediatype)
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "SELECT
        t.*,
        t2.id AS post_parent_id,
        t2.post_type AS post_parent_postype,
        t2.post_title AS post_parent_title,
        t2.post_content AS post_parent_content,  
        t2.post_date  as  post_parent_date,
        t2.post_author  as  post_parent_author,
        (select u.nickname from " . $wpdb->minapper_weixin_users . " u where u.userid=t2.post_author) as post_author_name       
        FROM
            " . $wpdb->posts . " t left join
            " . $wpdb->posts . " t2
        on
            t.post_parent = t2.id
        where  t2.post_status = 'publish'
        AND t.post_type = 'attachment'
        AND t.post_parent != 0
        AND t.post_status = 'inherit'
        AND (t2.post_type ='post' || t2.post_type ='topic' )";

        if ($mediatype == '') {
            $sql .= " AND ((
                t.post_mime_type = 'image/jpeg' || t.post_mime_type = 'image/gif' || t.post_mime_type = 'image/png' || t.post_mime_type = 'image/bmp'
            ) or t.post_mime_type='video/mp4' )  GROUP BY  t.post_parent,t.post_mime_type";
        } else if ($mediatype == 'video') {

            $sql .= " AND  t.post_mime_type='video/mp4' ";
        } else if ($mediatype == 'image') {

            $sql .= " AND (
                t.post_mime_type = 'image/jpeg' || t.post_mime_type = 'image/gif' || t.post_mime_type = 'image/png' || t.post_mime_type = 'image/bmp'
            )  GROUP BY  t.post_parent";
        }

        $order = " ORDER BY t2.post_date desc";
        $sql .= $order;
        $result = $wpdb->get_results($sql, ARRAY_A);
        return $result;
    }

    //清理输入的参数
    public static function sanitize_param($param)
    {
        if (isset($param)) {
            return sanitize_text_field(wp_unslash($param));
        }
        return null;
    }

    public static function updateShopGoodStock($postid, $goodscode)
    {
        $options = get_post_meta($postid, 'minapper_shop_fields', true);
        $_specifications = empty($options) ? '' : $options['specifications'];
        $allstock = 0;
        $specifications = array();
        if (!empty($_specifications)) { {
                foreach ($_specifications as $s) {
                    if ($s['goodscode'] == $goodscode) {
                        $stock = empty($s['stock']) ? 0 : (int)$s['stock'];
                        if ($stock != 0) {
                            $stock--;  //当前规格库存减少一个
                            $s['stock'] = $stock;
                        }
                    }
                    $allstock += empty($s['stock']) ? 0 : (int)$s['stock'];
                    $specifications[] = $s;
                }
            }
        }
        $options['specifications'] = $specifications;
        update_post_meta($postid, 'minapper_shop_fields', $options);
        update_post_meta($postid, '_minapper_shop_allstock', $allstock);

        return $stock;
    }

    public static function get_users_recent_comment()
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "SELECT user_id as userid ,nickname as username, avatarurl,member  from 
(SELECT DISTINCT user_id from  (SELECT user_id ,`comment_date`   as  postdate FROM $wpdb->comments UNION SELECT post_author ,post_date as postdate FROM $wpdb->posts WHERE `post_type`='reply' and post_status='publish'  ORDER BY  postdate DESC) as t  LIMIT 0,20) as tuser  left JOIN  " . $wpdb->minapper_weixin_users . "  on  tuser.user_id = wp_minapper_weixin_users.userid";
        $_users = $wpdb->get_results($sql);
        $users = array();
        foreach ($_users as $user) {
            if (empty($user->username)) {
                $wpuser = get_user_by("id", (int)$user->userid);
                $user->username = $wpuser->display_name;
                $minapper_avatar = get_user_meta((int)$wpuser->id, 'minapper_avatar', true);
                if (!empty($minapper_avatar)) {
                    $author_avatar = $minapper_avatar;
                } else {
                    $author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
                }
                $user->avatarurl = $author_avatar;
                $user->memberName = self::getMemberName($user->member);
            }
            $users[] = $user;
        }

        return $users;
    }


    public static function get_users_recent_login()
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "select t.userid , t.nickname as username,t.avatarurl,t.member from " . $wpdb->minapper_weixin_users . " t  order by  t.updatedate desc limit 0,20";
        $_users = $wpdb->get_results($sql);
        $users = array();
        foreach ($_users as $user) {
            $user->memberName = self::getMemberName($user->member);
            $users[] = $user;
        }

        return $users;
    }

    public static function get_users_rand()
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "select t.userid, t.nickname as username,t.avatarurl,t.member from " . $wpdb->minapper_weixin_users . " t  order by    RAND()   limit 0,20";
        $_users = $wpdb->get_results($sql);
        $users = array();
        foreach ($_users as $user) {
            $user->memberName = self::getMemberName($user->member);
            $users[] = $user;
        }

        return $users;
    }

    public static function get_followRanking()
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $sql = "SELECT
				count(t.userid) AS count,
				t.extid   as userid,
			(SELECT w.avatarurl from " . $wpdb->minapper_weixin_users . " w where w.userid=t.extid) as avatarurl,		
            (SELECT w1.member from " . $wpdb->minapper_weixin_users . " w1 where w1.userid=t.extid) as member,
			u.display_name as username
			FROM
				" . $wpdb->minapper_ext . " t,
				" . $wpdb->users . " u
			WHERE
				t.extype = 'follow'
			AND t.extid = u.ID
			GROUP BY
				t.extid
			ORDER BY
				count DESC  LIMIT 0,100";
        $_follows = $wpdb->get_results($sql);
        $follows = array();
        foreach ($_follows as $_follow) {
            $follow['count'] = $_follow->count;
            $follow['username'] = $_follow->username;
            $follow['userid'] = $_follow->userid;
            if (empty($_follow->avatarurl)) {
                $minapper_avatar = get_user_meta((int)$_follow->userid, 'minapper_avatar', true);
                if (!empty($minapper_avatar)) {
                    $author_avatar = $minapper_avatar;
                } else {
                    $author_avatar = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/gravatar.png';
                }
                $follow['avatarurl'] = $author_avatar;
            } else {
                $follow['avatarurl'] = $_follow->avatarurl;
            }

            $follow['count'] = $_follow->count;
            $follow['memberName'] = self::getMemberName($_follow->member);
            $follows[] = $follow;
        }
        return $follows;
    }


    public static function get_users_byids($ids, $userid)
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $wpdb->minapper_ext = $wpdb->prefix . 'minapper_ext';
        $sql = "select t.userid, t.nickname as username,t.avatarurl,t.integral as count,t.member, 
        (select if(count(1)=1,'true','false')  from " . $wpdb->minapper_ext . " f where f.extid=t.userid and f.userid=" . $userid . " and f.extype='follow') as follow  
        from " . $wpdb->minapper_weixin_users . " t  where t.userid IN ($ids)";
        $_users = $wpdb->get_results($sql);
        $users = array();
        foreach ($_users as $user) {
            $user->memberName = self::getMemberName($user->member);
            $users[] = $user;
        }
        return $users;
    }

    public static function get_integralRanking()
    {
        global $wpdb;
        $wpdb->minapper_weixin_users = $wpdb->prefix . 'minapper_weixin_users';
        $sql = "select t.userid, t.nickname as username,t.avatarurl,t.integral as count,t.member from " . $wpdb->minapper_weixin_users . " t  order by  t.integral  desc limit 0,100";
        $_users = $wpdb->get_results($sql);
        $users = array();
        foreach ($_users as $user) {
            $user->memberName = self::getMemberName($user->member);
            $users[] = $user;
        }
        return $users;
    }


    public  static function  get_module_post_categories($categories)
    {
        $categoriesId = '';
        if (!empty($categories) && is_array($categories) && count($categories) > 0) {
            $categoriesId = implode(",", $categories);
        }
        $args = array();
        if (empty($categoriesId)) {
            $args = array(
                'orderby' => 'id',
                'order' => 'ASC',
                //'parent' => 0,
                'hide_empty' => false,
                'pad_counts' => false
            );
        } else {
            $args = array(
                'orderby' => 'id',
                'order' => 'ASC',
                'pad_counts' => false,
                'hide_empty' => false,
                'include' => $categoriesId
            );
        }

        $_categories = get_categories($args);
        $categories = array();
        foreach ($_categories as $category) {
            $category_thumbnail_image = '';
            $temp = '';
            if ($temp = get_term_meta($category->term_id, 'raw_catcover', true)) {
                $category_thumbnail_image = $temp;
            }



            $data['name'] = $category->name;
            $data['id'] = $category->term_id;
            $data['cateimg'] = $category_thumbnail_image;
            $data['count'] = $category->count;
            $categories[] = $data;
        }
        return $categories;
    }


    public  static function  get_module_shop_categories($categories)
    {
        $_productcat = RAW()->wxapi->get_productcat();
        $shopcatList = array();
        $shopcovers = get_option('raw-shop-cover');
        if ($_productcat['errcode'] == 0) {
            $shopcats = $_productcat['shopcat_list'];
            if ($categories == '') {

                foreach ($shopcats as $shopcat) {
                    $_shopcat = array();
                    $shopcover = '';
                    if (!empty($shopcovers)) {
                        foreach ($shopcovers as $cover) {
                            if ($cover['id'] == $shopcat['shopcat_id']) {
                                $shopcover = $cover['category_thumbnail_image'];
                                break;
                            }
                        }
                    }
                    $_shopcat['name'] = $shopcat['shopcat_name'];
                    $_shopcat['id'] = $shopcat['shopcat_id'];
                    $_shopcat['cateimg'] = $shopcover;
                    $_shopcat['count'] = '';
                    $shopcatList[] = $_shopcat;
                }
            } else {
                foreach ($categories as $categoryid) {
                    foreach ($shopcats as $shopcat) {
                        if ($shopcat['shopcat_id'] == $categoryid) {
                            $_shopcat = array();
                            $shopcover = '';
                            if (!empty($shopcovers)) {
                                foreach ($shopcovers as $cover) {
                                    if ($cover['id'] == $shopcat['shopcat_id']) {
                                        $shopcover = $cover['category_thumbnail_image'];
                                        break;
                                    }
                                }
                            }
                            $_shopcat['name'] = $shopcat['shopcat_name'];
                            $_shopcat['id'] = $shopcat['shopcat_id'];
                            $_shopcat['cateimg'] = $shopcover;
                            $_shopcat['count'] = '';
                            $shopcatList[] = $_shopcat;
                        }
                    }
                }
            }
        }

        return   $shopcatList;
    }

    public  static function  get_module_topic_categories($categories)
    {

        $categoriesId = '';
        if (!empty($categories) && is_array($categories) && count($categories) > 0) {
            $categoriesId = implode(",", $categories);
        }

        if (empty($categoriesId)) {
            $args = array(
                'numberposts' => 100,
                'post_type'   => 'forum',
                'post_status' => 'publish',
                //'post_parent' => 0,                   
                'orderby'     => 'menu_order',
                'order'       => 'DESC'
            );
        } else {
            $args = array(
                'numberposts' => 100,
                'post_type'   => 'forum',
                'post_status' => 'publish',
                'include' => $categoriesId,
                'orderby'     => 'menu_order',
                'order'       => 'DESC'
            );
        }

        $_forums = get_posts($args);
        $forums = array();
        foreach ($_forums as $_forum) {
            $forum = array();
            $forum['name'] = $_forum->post_title;
            $forum['id'] = $_forum->ID;
            $forum['count'] = bbp_get_forum_topic_count($_forum->ID);
            $forum['cateimg'] = self::get_post_content_first_image($_forum->post_content);
            $forums[] = $forum;
        }
        return $forums;
    }

    public static function randString()
    {
        $code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $rand = $code[rand(0, 25)]
            . strtoupper(dechex(date('m')))
            . date('d') . substr(time(), -5)
            . substr(microtime(), 2, 5)
            . sprintf('%02d', rand(0, 99));
        for (
            $a = md5($rand, true),
            $s = '0123456789abcdefjhijklmnopqrstuv',
            $d = '',
            $f = 0;
            $f < 8;
            $g = ord($a[$f]),
            $d .= $s[($g ^ ord($a[$f + 8])) - $g & 0x1F],
            $f++
        );
        return  $d;
    }
    public static function strLength($str, $charset = 'utf-8')
    {
        if ($charset == 'utf-8') $str = iconv('utf-8', 'gb2312', $str);
        $num = strlen($str);
        $cnNum = 0;
        for ($i = 0; $i < $num; $i++) {
            if (ord(substr($str, $i + 1, 1)) > 127) {
                $cnNum++;
                $i++;
            }
        }
        $enNum = $num - ($cnNum * 2);
        $number = ($enNum / 2) + $cnNum;
        return ceil($number);
    }

    public static function security_msgSecCheck($data)
    {

        $msgSecCheckResult = RAW()->wxapi->msgSecCheck($data);
        $errcode = $msgSecCheckResult['errcode'];
        $errmsg = $msgSecCheckResult['errmsg'];

        $result = array();
        if ($errcode != 0) {
            $result['errcode'] = $errcode;
            $result['errmsg'] = $errmsg;
        }
        else
        {
            $checkResult = $msgSecCheckResult['result'];
            $label = $checkResult['label'];
            if ($label != '100') {
                $result['errcode'] = (int)$label;
                $result['errmsg'] = "昵称无法通过审核";
            } else {
                $result['errcode'] = 0;
                $result['errmsg'] = "昵称合规合法";
            }
        }

        

        return  $result;
    }


    public static function security_mediaCheckAsync($data)
    {

        $msgSecCheckResult = RAW()->wxapi->mediaCheckAsync($data);
        $errcode = $msgSecCheckResult['errcode'];
        $errmsg = $msgSecCheckResult['errmsg'];

        $result = array();
        if ($errcode != 0) {
            $result['errcode'] = $errcode;
            $result['errmsg'] = $errmsg;
        }

        $checkResult = $msgSecCheckResult['result'];
        $label = $checkResult['label'];
        if ($label != '100') {
            $result['errcode'] = (int)$label;
            $result['errmsg'] = "昵称中含有违法违规内容";
        } else {
            $result['errcode'] = 0;
            $result['errmsg'] = "昵称合规合法";
        }

        return  $result;
    }

    //获取可修改头像的次数
    public static  function getEnableUpdateAvatarCount($userId)
    {
        $year = date('Y', time());
        $updateAvatarCount = $year . "-" . "updateAvatarCount";
        $updateCount = empty(get_user_meta($userId, $updateAvatarCount)) ? 0 : (int)get_user_meta($userId, $updateAvatarCount, true);
        $configCount = (int)get_option('raw_updateAvatar_count');
        $count = $configCount - $updateCount;
        if ($count < 0) {
            $count;
        }
        return $count;
    }

    //获取修改头像的次数
    public static  function getUpdateAvatarCount($userId)
    {
        $year = date('Y', time());
        $updateAvatarCount = $year . "-" . "updateAvatarCount";
        $updateCount = empty(get_user_meta($userId, $updateAvatarCount)) ? 0 : (int)get_user_meta($userId, $updateAvatarCount, true);
        return  $updateCount;
    }

    //设置修改头像的次数
    public static  function setUpdateAvatarCount($userId, $count)
    {
        $year = date('Y', time());
        $updateAvatarCount = $year . "-" . "updateAvatarCount";
        update_user_meta($userId, $updateAvatarCount, $count);
    }

    public static function get_special_article_property($post_id, $key, $default = '')
    {
        $options = get_post_meta($post_id, 'minapper_special_fields', true);
        if (isset($options[$key]) && !empty($options[$key])) {
            return $options[$key];
        }
        return $default;
    }

    public static function get_special($id, $userId)
    {
        $args = array(
            'taxonomy' => 'minapperspecialcategory',
            'orderby' => 'id',
            'order' => 'DESC',
            // 'parent' => 0,
            //'child_of' =>322,
            'hide_empty' => false,
            'pad_counts' => false
        );

        $term_query = new WP_Term_Query($args);
        $terms = $term_query->terms;
        $categories = array();
        foreach ($terms as $category) {

            if ($id == 0) {
                if ($category->parent == 0) {
                    $category = self::get_special_fields($category, $terms, $userId);
                    $categories[] = $category;
                }
            } else {
                if ($category->term_id == $id) {
                    $category = self::get_special_fields($category, $terms, $userId);
                    $categories[] = $category;
                }
            }
        }

        return $categories;
    }

    public static function get_child_special($term_id, $terms, $userId)
    {
        $childCategories = array();
        foreach ($terms as $category) {
            if ($category->parent == $term_id) {
                $category = self::get_special_fields($category, $terms, $userId);
                $childCategories[] = $category;
            }
        }

        return $childCategories;
    }

    public  static function get_special_fields($category, $terms, $userId)
    {

        $cover   = get_term_meta($category->term_id, 'minapper_special_category_cover', true);
        $des   = get_term_meta($category->term_id, 'minapper_special_category_des', true);
        $category_type   = get_term_meta($category->term_id, 'minapper_special_category_type', true);
        $pay_type   = get_term_meta($category->term_id, 'minapper_special_pay_type', true);
        $author_id = get_term_meta($category->term_id, 'minapper_special_author_id', true);
        $wpuser = get_user_by("id", $author_id);
        $author = $wpuser->display_name;

        $saleprice = get_term_meta($category->term_id, 'minapper_special_category_saleprice', true);
        $marketprice = get_term_meta($category->term_id, 'minapper_special_category_marketprice', true);

        $saleintegral   = get_term_meta($category->term_id, 'minapper_special_category_saleintegral', true);
        $marketintegral   = get_term_meta($category->term_id, 'minapper_special_category_marketintegral', true);

        $profitsharing_flag   = get_term_meta($category->term_id, 'minapper_special_profitsharing_flag', true);
        $profitsharing_scale   = get_term_meta($category->term_id, 'minapper_special_profitsharing_scale', true);

        $vipflag = get_term_meta($category->term_id, 'minapper_special_vip_flag', true);

        $category_type_name = '图文';
        switch ($category_type) {
            case 'article':
                $category_type_name = '图文';
                break;
            case 'video':
                $category_type_name = '视频';
                break;
            case 'audio':
                $category_type_name = '音频';
                break;
            case 'download':
                $category_type_name = '下载';
                break;
        }
        $payflag = '0';
        if ($userId != 0) {
            // $memberUser =self::getMemberUserbyUserId($userId);
            // if(!empty($memberUser))
            // {
            //     $member =$memberUser->member;
            //     //$member !="00" &&  
            //     if(vipflag=='1')
            //     {
            //         $vipcansee='1';
            //     }
            // }
            $payflag = self::get_special_payflag($userId, $category->term_id);
        }
        $category->vipflag = $vipflag; //vip免费阅读标识
        $category->payflag = $payflag; //非vip用户付费标识
        $category->des = $des; //专栏介绍

        $category->categorytype = $category_type;
        $category->categorytypename = $category_type_name;
        $category->profitsharing_flag = $profitsharing_flag;
        $category->profitsharing_scale = $profitsharing_scale;

        $category->paytype = $pay_type; //是否为付费
        $category->author_id = $author_id;
        $category->author_name = $author;
        $category->saleprice = $saleprice;
        $category->marketprice = $marketprice;


        $category->saleintegral = $saleintegral;
        $category->marketintegral = $marketintegral;


        $category->cover = $cover;

        $childCategories = self::get_child_special($category->term_id, $terms, $userId);
        $category->child = $childCategories;
        return $category;
    }

    public static function get_pecialpost_byid($id, $userId)
    {
        global $wpdb;
        $sql = "SELECT *,
        (select $wpdb->postmeta.meta_value from $wpdb->postmeta where $wpdb->postmeta.post_id=$wpdb->posts.ID and $wpdb->postmeta.meta_key='_specialpost_trySee') as trySee FROM $wpdb->posts ";
        $sql .= "where  $wpdb->posts.post_type = 'minapperspecial' AND $wpdb->posts.post_status = 'publish' and  $wpdb->posts.ID=" . $id;
        $post = $wpdb->get_row($sql);
        $post = self::get_specialpost_fields($post, $userId);
        return $post;
    }


    //获取专栏文章的字段属性
    public static function get_specialpost_fields($post, $userId)
    {
        global $wpdb;
        $post_id = $post->ID;
        $categories = get_the_terms($post_id, 'minapperspecialcategory');
        if (empty($categories)) {
            $post->specialcategory_name = "文章没有设置专栏分类";
            $post->post_content = '';
            return $post;
        }
        $specialcategory_id = empty($categories) ? 0 : $categories[0]->term_id;
        $minapper_special_vip_flag = get_term_meta($specialcategory_id, 'minapper_special_vip_flag', true);

        $author_id = get_term_meta($specialcategory_id, 'minapper_special_author_id', true);
        $wpuser = get_user_by("id", $author_id);
        $author_name = $wpuser->display_name;

        $post->author_id = $author_id;
        $post->author_name = $author_name;
        $specialcategory_name = $categories[0]->name;
        $post->specialcategory_id = $specialcategory_id;
        $post->specialcategory_name = $specialcategory_name;
        $paytype = get_term_meta($specialcategory_id, 'minapper_special_pay_type', true);
        $sort = get_post_meta($post_id, '_specialpost_order', true);
        $post->sort = $sort;
        $post->payflag = '0';
        $post->paytype = $paytype;
        $vipflag = get_term_meta($specialcategory_id, 'minapper_special_vip_flag', true);
        $post->vipflag = $vipflag;
        if ($paytype == '1' && $post->trySee == 'disenable') {
            // $wpdb->minapper_order = $wpdb->prefix . 'minapper_order';
            // $sql = $wpdb->prepare("select count(1) from " . $wpdb->minapper_order . "  where userid=%d and status=1 and  (extid=%d  and  ordertype='minapperspecial')", $userId,$specialcategory_id);
            // $count = (int)$wpdb->get_var($sql);
            $flag = self::get_special_payflag($userId, $specialcategory_id);
            $memberUser = self::getMemberUserbyUserId($userId);
            $vipcansee = '0';
            $member = '';
            if (!empty($memberUser)) {
                $member = $memberUser->member;
                //$member !="00" &&  
                if ($member == "00" || ($member == "01" && $minapper_special_vip_flag == '1')) {
                    $vipcansee = '1';
                }
            }
            if ($flag == '1' || $vipcansee == '1') {
                $keys = array('thumbnail', 'cover', 'videoResources', 'audioResources', 'downloadResources');
                $options = get_post_meta($post_id, 'minapper_special_fields', true);
                foreach ($keys as $key) {
                    if (isset($options[$key])) {
                        $post->$key = $options[$key];
                    }
                }

                $post->payflag = '1';
            } else {
                $keys = array('thumbnail', 'cover');
                $options = get_post_meta($post_id, 'minapper_special_fields', true);
                foreach ($keys as $key) {
                    if (isset($options[$key])) {
                        $post->$key = $options[$key];
                    }
                }
                $post->post_content = '';
            }
        } else if ($paytype == '0' || ($paytype == '1' && $post->trySee == 'enable')) {
            $keys = array('thumbnail', 'cover', 'videoResources', 'audioResources', 'downloadResources');
            $options = get_post_meta($post_id, 'minapper_special_fields', true);
            foreach ($keys as $key) {
                if (isset($options[$key])) {
                    $post->$key = $options[$key];
                }
            }
        }

        return $post;
    }

    public static function get_special_payflag($userId, $specialcategory_id)
    {
        global $wpdb;
        $wpdb->minapper_order = $wpdb->prefix . 'minapper_order';
        $payflag = "0";
        $sql = $wpdb->prepare("select count(1) from " . $wpdb->minapper_order . "  where userid=%d and status=1 and  (extid=%d  and  ordertype='minapperspecial')", $userId, $specialcategory_id);
        $count = (int)$wpdb->get_var($sql);
        if ($count == 1) {
            $payflag = '1';
        }
        return $payflag;
    }

    public static function creat_qrcode($pagetype, $postId, $_path, $invitecode = '0', $checkexit = false)
    {
        $result = array();
        $qrcodeName = '';
        $postype = '';
        $path = '';
        $mpPostLink = '';
        $postformat = '';
        if (!empty($postId) && ($pagetype == 'detail' || $pagetype == 'socialdetail')) {
            $mpPostLink = get_post_meta($postId, '_mpPostLink', true);
            $postformat = get_post_format((int)$postId)?: 'standard';
            if ($postformat == 'postformatlink') {
                $postformat = 'link';
            }
            $_postId = (int)$postId;
            $postype = get_post_type($_postId) ? get_post_type($_postId) : "";
            if (!($postype == "post" || $postype == "topic")) {
                $result['errcode'] = "2";
                $result['errmsg'] = "参数错误";
                return  $result;
            }

            $qrcodeName = 'qrcode-' . $postId . '.png'; //文章小程序二维码文件名   
        } else if (!empty($postId) && $pagetype == 'shopproduct') {
            $qrcodeName = 'qrcode-' . $pagetype . '-' . $postId . '.png';
        } else {
            $qrcodeName = 'qrcode-' . $pagetype . '.png';
        }


        $qrcodePath = REST_API_TO_WECHAT_PLUGIN_DIR . 'images/qrcode/';
        if (!is_dir($qrcodePath)) {
            mkdir($qrcodePath, 0777);
        }
        //$qrcodePath = $qrcodePath.$qrcodeName;//文章小程序二维码路径
        //$qrcodeUrl = plugins_url().'/'.REST_API_TO_WECHAT_PLUGIN_NAME.'/images/qrcode/'.$qrcodeName;


        $filedate = '';
        if ($invitecode == '0') {
            $qrcodePath = $qrcodePath . $qrcodeName; //文章小程序二维码路径
            $qrcodeUrl = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/qrcode/' . $qrcodeName;
        } else {
            $filedate = date('YmdHis') . rand(100, 999); //为了避免时间重复，再加一段2位的随机数
            $invitecodeQrcodeName = 'qrcode-' . $invitecode . '-' . $filedate . '.png'; //文章小程序二维码文件名
            $qrcodePath = $qrcodePath . $invitecodeQrcodeName;
            $qrcodeUrl = plugins_url() . '/' . REST_API_TO_WECHAT_PLUGIN_NAME . '/images/qrcode/' . $invitecodeQrcodeName;
        }



        if ($pagetype == 'detail' && !empty($postId) && $postformat != 'link') {
            $path = "pages/detail/detail?id=" . $postId;
            if ($postformat == "video") {
                $path .= '&format=video';
            } else {
                $path .= '&format=standard';
            }

            if ($invitecode != '0') {
                $path .= '&invitecode=' . $invitecode;
            }
        } else if ($pagetype == 'socialdetail' && !empty($postId) && $postformat != 'link') {
            $path = "pages/socialdetail/socialdetail?id=" . $postId;
            if ($invitecode != '0') {
                $path .= '&invitecode=' . $invitecode;
            }
        } else if (!empty($postId) && $postformat == 'link' && !empty($mpPostLink)) {
            $path = "pages/webview/webview?url=" . $mpPostLink;
        } else {
            $path = $_path;
        }

        //判断文章小程序二维码是否存在
        //$checkexit 是否检查
        if ($checkexit && is_file($qrcodePath)) {

            $result['qrcodeUrl'] = $qrcodeUrl;
            $result['qrcodePath'] = $qrcodePath;
            $result['path'] = $path;
            $result['filedate'] = $filedate;
            return $result;
        }



        $color = array(
            "r" => "0",
            "g" => "0",
            "b" => "0",
        );
        $data = array(

            'path' => $path, //前端传过来的页面path
            'width' => 430, //设置二维码尺寸
            'auto_color' => false,
            'line_color' => $color,
        );
        $qrcodeesult = RAW()->wxapi->get_qrcode($data);
        $errcode = (int)$qrcodeesult['errcode'];
        if ($errcode == 0) {
            $qrcode = $qrcodeesult['buffer'];
            file_put_contents($qrcodePath, $qrcode);
            $result['qrcodeUrl'] = $qrcodeUrl;
            $result['qrcodePath'] = $qrcodePath;
            $result['path'] = $path;
            $result['filedate'] = $filedate;
        } else {

            $result['errcode'] = "1";
            $result['errmsg'] = "生成二维码错误";
        }


        return $result;
    }

    public static function get_history_post_list($post_year, $post_month, $post_day){
        global $wpdb;
        $limit = 10;
        $order = "latest";
        if($order == "latest"){ $order = "DESC";} else { $order = '';}
        $sql = "select ID, year(post_date_gmt) as post_year,date(post_date_gmt) as post_date, post_title, comment_count,post_content,ID FROM 
        $wpdb->posts WHERE post_password = '' AND post_type = 'post' AND post_status = 'publish'
        AND year(post_date_gmt)!='".$post_year."' AND month(post_date_gmt)='".$post_month."' AND day(post_date_gmt)='".$post_day."'
        order by post_date_gmt ".$order." limit ".$limit;
        $_histtory_post = $wpdb->get_results($sql);
        $histtory_post = array();
        foreach ($_histtory_post as $post) {
            $postsImage = self::getPostImages($post->post_content, $post->ID);
            $post_id = $post->ID;
            $rpData['ID'] = $post_id;
            $postformat = get_post_format($post_id)?: 'standard';
            if ($postformat == 'postformatlink') {
                $postformat = "link";
            }
            $rpData['format'] = $postformat;
            $channelsFeedId = empty(get_post_meta($post_id, '_channelsFeedId', true)) ? "" : get_post_meta($post_id, '_channelsFeedId', true);
            $rpData['channelsFeedId'] = $channelsFeedId;

            $channelsId = empty(get_post_meta($post_id, '_channelsId', true)) ? "" : get_post_meta($post_id, '_channelsId', true);
            $rpData['channelsId'] = $channelsId;

            $mpPostLink = empty(get_post_meta($post_id, '_mpPostLink', true)) ? "" : get_post_meta($post_id, '_mpPostLink', true);
            $rpData['mpPostLink'] = $mpPostLink;

            $unassociated = empty(get_post_meta($post_id, '_unassociated', true)) ? "" : get_post_meta($post_id, '_unassociated', true);
            $rpData['unassociated'] = $unassociated;

            $rpData['post_title'] = $post->post_title;
            $rpData['post_frist_image'] = $postsImage['post_frist_image'];
            $rpData['post_thumbnail_image'] = $postsImage['post_thumbnail_image'];
            $rpData['post_medium_image'] = $postsImage['post_medium_image'];
            $rpData['post_large_image'] = $postsImage['post_large_image'];
            $rpData['post_full_image'] = $postsImage['post_full_image'];
            $histtory_post[]=$rpData;
        }


        return $histtory_post;
    }

    public static  function isMobileBrowser() {
        $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
       
        
        // 检查User-Agent中是否包含手机浏览器的特征字符串
        $mobileUA_Feature = array("android", "iphone", "ipod", "blackberry", "iemobile", "opera mini");
        foreach ($mobileUA_Feature as $uaFeature) {
            if (strpos($userAgent, $uaFeature) !== false) {
                return true;
            }
        }
        
        // 检查HTTP_ACCEPT是否包含特定于手机浏览器的MIME类型
        if(isset($_SERVER['HTTP_ACCEPT']))
        {
            $accept = strtolower($_SERVER['HTTP_ACCEPT']);
            if ((strpos($accept, 'text/vnd.wap.wml') !== false) || (strpos($accept, 'application/vnd.wap.xhtml+xml') !== false)) {
                return true;
            }
        }       
        
        // 检查屏幕宽度是否小于特定值，通常用于手机设备
        if (isset($_SERVER['HTTP_X_WAP_PROFILE']) || isset($_SERVER['HTTP_PROFILE'])) {
            return true;
        } 
        
        return false;
    }

    public static function  launchMiniProgram($pcpath,$postId,$buttontext,$type,$webappid,$ghid)
    {
        $launchButton='';
        if(!empty(get_option('minapper_webapp_appid'))&& !RAW_Util::isMobileBrowser())
        {
            if($pcpath=='' && $type=='content' && $postId !='' )
            {
                $pcpath="'"."pages/detail/detail?id=".$postId."'"; 
            }
            else
            {
                $pcpath="'".$pcpath."'";
            }
            if(empty($ghid))
            {
                $ghid="'".get_option('raw_gh_id')."'"; 
            }
            else
            {
                $ghid="'".$ghid."'"; 
            }
            if(empty($webappid))
            {
                $webappid="'".get_option('minapper_webapp_appid')."'";
            }
            else
            {
                $webappid="'".$webappid."'";
            }        
            $result=RAW()->wxapi->get_pcopensdk_ticket();        
            $ticket="'".$result['ticket']."'"; 
            if($type=='content')
            {
                $launchButton ='<br/><br/><button onclick="launchMiniProgram('.$webappid.','.$ghid.','.$pcpath.','.$ticket.')">'.$buttontext.'</button>';
            }
            elseif($type=='shortcode')
            {
                $launchButton='<div class="wmcode-btn"><a href="#"  onclick="launchMiniProgram('.$webappid.','.$ghid.','.$pcpath.','.$ticket.')">'.$buttontext.'</a></div>&emsp;&emsp;';
            }
        }
        
       
        return $launchButton;
    }

    public static function shareMiniProgram($pcpath,$postId,$title,$imgUrl,$buttontext,$type,$webappid,$ghid)
    {
        $shareButton='';
        if(!empty(get_option('minapper_webapp_appid'))&& !RAW_Util::isMobileBrowser())
        {
            if($pcpath=='' && $type=='content' && $postId !='' )
            {
                $pcpath="'"."pages/detail/detail?id=".$postId."'"; 
            }
            else
            {
                $pcpath="'".$pcpath."'";
            }
    
            if(empty($ghid))
            {
                $ghid="'".get_option('raw_gh_id')."'"; 
            }
            else
            {
                $ghid="'".$ghid."'"; 
            }
            if(empty($webappid))
            {
                $webappid="'".get_option('minapper_webapp_appid')."'";
            }
            else
            {
                $webappid="'".$webappid."'";
            }                          
            $result=RAW()->wxapi->get_pcopensdk_ticket();  
            $shareTicket="'".$result['ticket']."'";
            $shareTitle="'".$title."'";
            $shareImgUrl="'".$imgUrl."'";
            if($type=='content')
            {
                $shareButton ='<button onclick="shareMiniProgram('.$webappid.','.$ghid.','.$shareTitle.','.$pcpath.','.$shareImgUrl.','.$shareTicket.')">'.$buttontext.'</button><br/><br/>';
            }
            elseif($type=='shortcode')
            {
                $shareButton='<div class="wmcode-btn"><a href="#"  onclick="shareMiniProgram('.$webappid.','.$ghid.','.$shareTitle.','.$pcpath.','.$shareImgUrl.','.$shareTicket.')">'.$buttontext.'</a></div>';
            }
            
        }
        
        return $shareButton;

    }
}
