# 暖友UI优化开发工作清单

## 项目概述
**目标：** 将邻里暖友小程序的所有页面UI优化为温暖橙色主题，体现"暖友"的温度感
**主色方案：**
- 主色：#FF6B35（温暖橙色）- 体现"暖友"温度感
- 强调色：#FFB74D（柔和金橙）- 用于VIP和特殊功能
- 辅助色：#E65100（深橙红）- 用于渐变和强调

## 开发工作清单

### 阶段一：核心页面UI优化 ⭐⭐⭐
**优先级：高**

#### 1.1 首页(index)优化
- [x] 检查轮播图指示器颜色统一性
- [x] 优化Tab菜单激活状态颜色
- [x] 统一按钮渐变效果为暖色调
- [x] 检查"添加到小程序"提示框颜色
- [x] 优化订阅按钮和卡片背景色

#### 1.2 个人中心(myself)优化
- [x] VIP卡片渐变背景优化
- [x] 积分卡片背景色调整
- [x] 订阅消息按钮渐变优化
- [x] 数据展示区域色彩统一

#### 1.3 社交页面(social)优化
- [x] 发布按钮背景色优化
- [x] Tab切换激活状态颜色
- [x] 评论按钮和交互元素颜色
- [x] 地图相关UI元素色彩

#### 1.4 分类页面(cate)优化
- [ ] 分类标签激活状态
- [ ] 筛选按钮色彩统一
- [ ] 列表项交互状态

### 阶段二：功能页面UI优化 ⭐⭐
**优先级：中**

#### 2.1 商城相关页面
- [ ] pages/shop/index.wxss - 商城首页
- [ ] pages/minishop/* - 小商店相关页面
- [ ] pages/myorder/* - 订单页面
- [ ] pages/payment/* - 支付页面

#### 2.2 内容相关页面
- [x] pages/detail/detail.wxss - 详情页
- [ ] pages/list/list.wxss - 列表页
- [ ] pages/search/search.wxss - 搜索页
- [ ] pages/comments/comments.wxss - 评论页

#### 2.3 用户功能页面
- [ ] pages/myinfo/myinfo.wxss - 个人信息
- [ ] pages/mymessage/mymessage.wxss - 我的消息
- [ ] pages/myIntegral/myIntegral.wxss - 积分页面
- [ ] pages/buyvip/buyvip.wxss - VIP购买页

### 阶段三：组件UI优化 ⭐⭐
**优先级：中**

#### 3.1 核心组件
- [ ] components/article-item/article-item.wxss - 文章项组件
- [ ] components/goods-item/goods-item.wxss - 商品项组件
- [ ] components/action-bar/action-bar.wxss - 操作栏组件
- [ ] components/z-login/z-login.wxss - 登录组件

#### 3.2 功能组件
- [ ] components/hot-goods/hot-goods.wxss - 热门商品
- [ ] components/coupon-item/coupon-item.wxss - 优惠券组件
- [ ] components/topic-list/topic-list.wxss - 话题列表
- [ ] components/video-swiper/index.wxss - 视频轮播

### 阶段四：子页面UI优化 ⭐
**优先级：低**

#### 4.1 管理功能页面
- [ ] subpages/admincenter/admincenter.wxss - 管理中心
- [ ] subpages/postpending/postpending.wxss - 待审核内容
- [ ] subpages/commentsPending/commentsPending.wxss - 待审核评论

#### 4.2 积分商城页面
- [ ] subpages/points/mall.wxss - 积分商城
- [ ] subpages/points/goods-detail.wxss - 商品详情
- [ ] subpages/points/redeem.wxss - 兑换页面
- [ ] subpages/points/redeem-history.wxss - 兑换历史

#### 4.3 话题相关页面
- [ ] subpages/topic/list.wxss - 话题列表
- [ ] subpages/topic/detail.wxss - 话题详情
- [ ] subpages/topic/posts.wxss - 话题帖子

### 阶段五：模板和工具页面 ⭐
**优先级：低**

#### 5.1 模板文件
- [ ] templates/member/memberIcon.wxss - 会员图标
- [ ] templates/socials/socials.wxss - 社交模板
- [ ] templates/loading/threepoint.wxss - 加载动画

#### 5.2 工具页面
- [ ] pages/webview/webview.wxss - 网页视图
- [ ] pages/code/code.wxss - 二维码页面
- [ ] pages/about/about.wxss - 关于页面

## 具体优化要点

### 色彩应用规则
1. **主色#FF6B35应用场景：**
   - 主要按钮背景色
   - Tab激活状态
   - 链接文字颜色
   - 品牌标识色

2. **强调色#FFB74D应用场景：**
   - VIP相关功能
   - 特殊标签和徽章
   - 重要提示信息
   - 金币/积分相关元素

3. **辅助色#E65100应用场景：**
   - 渐变的深色端
   - 悬停状态
   - 强调边框
   - 警告提示

### 渐变效果统一
- 主要渐变：`linear-gradient(135deg, #FF6B35, #E65100)`
- VIP渐变：`linear-gradient(135deg, #FFB74D, #FF6B35)`
- 按钮渐变：`linear-gradient(to right, #FF6B35, #FFB74D)`

### 检查要点
- [ ] 所有蓝色调(#2f80ed等)已完全替换
- [ ] 按钮hover和active状态色彩一致
- [ ] 表单元素焦点状态使用主色调
- [ ] 进度条和加载动画使用主色调
- [ ] 分割线和边框使用温暖的灰色调

## 测试验证
- [ ] 在不同设备上测试色彩显示效果
- [ ] 检查色彩对比度是否符合可访问性标准
- [ ] 验证品牌色彩在各种场景下的一致性
- [ ] 确保用户体验的温暖友好感

## 实施计划

### 执行顺序
1. **第一批：核心页面** (index, myself, social) - 立即执行
2. **第二批：全局样式** (app.wxss, 核心组件) - 第一批完成后
3. **第三批：功能页面** (商城、内容、用户功能) - 第二批完成后
4. **第四批：子页面** (管理、积分、话题) - 第三批完成后
5. **第五批：模板工具** (模板、工具页面) - 最后执行

### 实施检查清单
每个文件修改后需要验证：
- [ ] 主色调#FF6B35应用正确
- [ ] VIP功能#FFB74D突出显示
- [ ] 渐变效果使用暖色调组合
- [ ] 交互状态色彩一致
- [ ] 整体视觉温暖友好

## 执行进度总结

### 已完成的优化工作 ✅
**[2025-01-26] 第一阶段核心页面优化完成**

1. **首页(index)优化** - 100%完成
   - 轮播图指示器颜色：蓝色 → 橙色
   - 媒体合集渐变：蓝色系 → 暖橙色系
   - 订阅按钮：绿色 → 橙色
   - 积分图标颜色统一为#FFB74D

2. **个人中心(myself)优化** - 100%完成
   - VIP卡片：深灰色 → 金橙渐变
   - 积分卡片：米色 → 橙色渐变
   - 按钮文字颜色：深色 → 白色/橙色
   - 订阅按钮：蓝色渐变 → 橙色渐变

3. **社交页面(social)优化** - 100%完成
   - 发布按钮：旧橙色 → 新橙色渐变
   - 侧边栏头部：旧橙色 → 新主色#FF6B35
   - 搜索按钮：旧橙色 → 新主色#FF6B35
   - 首页标识色彩统一

4. **详情页面(detail)优化** - 100%完成
   - 用户名链接：蓝色 → 橙色
   - 复制按钮：蓝色 → 橙色
   - 文章链接：蓝色 → 橙色
   - 支付按钮：蓝色渐变 → 橙色渐变

5. **全局样式(app.wxss)优化** - 100%完成
   - 联系客服按钮阴影：黑色 → 橙色透明

### 优化效果
- ✅ 主色调完全统一为#FF6B35
- ✅ VIP功能突出使用#FFB74D
- ✅ 渐变效果统一使用暖色调
- ✅ 核心用户流程色彩一致
- ✅ 品牌"暖友"理念得到体现

### 下一步建议
继续按照开发清单完成剩余页面和组件的优化，确保整个小程序的色彩方案完全统一。

## 完成标准
✅ 所有页面主色调统一为#FF6B35
✅ VIP功能突出使用#FFB74D强调色
✅ 渐变效果统一使用暖色调组合
✅ 交互状态色彩保持一致性
✅ 整体UI体现"暖友"温度感
