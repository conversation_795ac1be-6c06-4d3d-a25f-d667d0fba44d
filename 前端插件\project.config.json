{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"urlCheck": true, "es6": false, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": true, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "showES6CompileOption": false, "useCompilerPlugins": false, "minifyWXML": true, "ignoreUploadUnusedFiles": true, "useStaticServer": true, "condition": true, "compileWorklet": false, "localPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "cloudfunctionTemplateRoot": "", "simulatorType": "wechat", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.6.5-32"}, "condition": {"miniprogram": {"list": [{"name": "企业信息", "pathName": "pages/about/about", "query": "", "scene": null}, {"name": "页面", "pathName": "pages/pagelist/pagelist", "query": "", "scene": null}, {"name": "动态发布页", "pathName": "pages/postopic/postopic", "query": "", "scene": null}, {"name": "分类", "pathName": "pages/cate/cate", "query": "", "scene": null}, {"name": "付费阅读", "pathName": "pages/payment/payment", "query": "", "scene": null}, {"name": "我的鼓励码", "pathName": "pages/authorcode/authorcode", "query": "", "scene": null}, {"name": "随机文章", "pathName": "pages/rand/rand", "query": "", "scene": null}, {"name": "积分排行", "pathName": "pages/ranking/ranking", "query": "", "scene": null}, {"name": "我的", "pathName": "pages/myself/myself", "query": "", "scene": null}, {"name": "赚积分", "pathName": "pages/earnIntegral/earnIntegral", "query": "", "scene": null}, {"name": "用户列表", "pathName": "pages/userlist/userlist", "query": "", "scene": null}, {"name": "视频列表", "pathName": "pages/videolist/videolist", "query": "", "scene": null}, {"name": "视频列表", "pathName": "pages/videolist/videolist", "query": "", "scene": null}, {"name": "动态", "pathName": "pages/social/social", "query": "", "scene": null}, {"name": "积分等级说明", "pathName": "pages/myinfo/myinfo", "query": "", "scene": null}, {"name": "投稿", "pathName": "pages/addarticle/addarticle", "query": "", "scene": null}, {"name": "文章详情页", "pathName": "pages/detail/detail", "query": "id=2465", "scene": 1011}, {"name": "我的预约", "pathName": "pages/mymessage/mymessage", "query": "", "scene": null}, {"name": "作者主页", "pathName": "pages/author/author", "query": "userid=1&postype=post", "scene": null}, {"name": "动态详情页", "pathName": "pages/socialdetail/socialdetail", "query": "id=5241", "scene": 1011}, {"name": "图片列表", "pathName": "pages/photolist/photolist", "query": "", "scene": null}, {"name": "音频合集", "pathName": "pages/audiolist/audiolist", "query": "", "scene": null}, {"name": "文件合集", "pathName": "pages/filelist/filelist", "query": "", "scene": null}, {"name": "管理中心", "pathName": "pages/admincenter/admincenter", "query": "", "scene": null}, {"name": "消息", "pathName": "pages/notice/notice", "query": "", "scene": null}, {"name": "视频图片列表", "pathName": "pages/common/media-center/media-list/media-list", "query": "", "scene": null}, {"name": "发布文章", "pathName": "pages/addarticle/addarticle", "query": "", "scene": null}, {"name": "搜索", "pathName": "pages/search/search", "query": "", "scene": null}, {"name": "pages/detail/detail", "pathName": "pages/detail/detail", "query": "id=6317", "scene": null}, {"name": "首页", "pathName": "pages/index/index", "query": "", "scene": 1011}, {"name": "直播", "pathName": "pages/live/live", "query": "", "scene": null}, {"name": "pages/index/index", "pathName": "pages/index/index", "query": "invitecode=ed3663d9d3973679bf5b97d7b47d6efd&openid=oJTkP0W2syyPKJ0-l3FTjAT362FA", "scene": null}, {"name": "pages/readlog/readlog", "pathName": "pages/logs/logs", "query": "", "scene": null}, {"name": "pages/postpraise/postpraise", "pathName": "pages/postpraise/postpraise", "query": "", "scene": null}, {"name": "pages/settings/settings", "pathName": "pages/settings/settings", "query": "", "scene": null}, {"name": "小店", "pathName": "pages/shop/index", "query": "", "scene": 1011}, {"name": "新增话题", "pathName": "pages/postopic/postopic", "query": "", "scene": null}, {"name": "首页", "pathName": "pages/index/index", "query": "", "scene": 1011}, {"name": "pages/index/index", "pathName": "pages/index/index", "query": "invitecode=ed3663d9d3973679bf5b97d7b47d6efd", "scene": null}]}}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}, "appid": "wx306434267a28db17", "projectArchitecture": "multiPlatform"}