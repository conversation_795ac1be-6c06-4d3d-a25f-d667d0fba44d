<?php
/**
 * 登录状态调试工具
 */

// 引入WordPress环境
require_once('wp-config.php');

echo "<h1>登录状态调试工具</h1>";

// 1. 检查用户会话表
echo "<h2>1. 用户会话表检查</h2>";
global $wpdb;
$session_table = $wpdb->prefix . 'minapper_user_session';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$session_table'") == $session_table;

if ($table_exists) {
    echo "<p style='color: green;'>✓ 用户会话表存在: $session_table</p>";
    
    // 查看最近的会话记录
    $recent_sessions = $wpdb->get_results("
        SELECT * FROM $session_table 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
    if ($recent_sessions) {
        echo "<p>最近的会话记录:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>用户ID</th><th>会话ID</th><th>创建时间</th><th>过期时间</th></tr>";
        foreach ($recent_sessions as $session) {
            echo "<tr>";
            echo "<td>{$session->userid}</td>";
            echo "<td>" . substr($session->sessionid, 0, 20) . "...</td>";
            echo "<td>{$session->created_at}</td>";
            echo "<td>{$session->expire_time}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠ 没有找到会话记录</p>";
    }
} else {
    echo "<p style='color: red;'>✗ 用户会话表不存在</p>";
}

// 2. 检查微信用户表
echo "<h2>2. 微信用户表检查</h2>";
$weixin_users_table = $wpdb->prefix . 'minapper_weixin_users';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$weixin_users_table'") == $weixin_users_table;

if ($table_exists) {
    echo "<p style='color: green;'>✓ 微信用户表存在: $weixin_users_table</p>";
    
    // 查看用户数量
    $user_count = $wpdb->get_var("SELECT COUNT(*) FROM $weixin_users_table");
    echo "<p>用户总数: $user_count</p>";
    
    // 查看最近注册的用户
    $recent_users = $wpdb->get_results("
        SELECT userid, nickname, created_at 
        FROM $weixin_users_table 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
    if ($recent_users) {
        echo "<p>最近注册的用户:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>用户ID</th><th>昵称</th><th>注册时间</th></tr>";
        foreach ($recent_users as $user) {
            echo "<tr>";
            echo "<td>{$user->userid}</td>";
            echo "<td>{$user->nickname}</td>";
            echo "<td>{$user->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p style='color: red;'>✗ 微信用户表不存在</p>";
}

// 3. 模拟登录检查
echo "<h2>3. 模拟登录检查</h2>";

// 获取一个有效的用户ID进行测试
$test_user = $wpdb->get_row("SELECT userid FROM $weixin_users_table LIMIT 1");

if ($test_user) {
    $test_userid = $test_user->userid;
    echo "<p>使用测试用户ID: $test_userid</p>";
    
    // 检查是否有有效会话
    $valid_session = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM $session_table 
        WHERE userid = %d AND expire_time > NOW() 
        ORDER BY created_at DESC 
        LIMIT 1
    ", $test_userid));
    
    if ($valid_session) {
        echo "<p style='color: green;'>✓ 找到有效会话</p>";
        echo "<p>会话ID: " . substr($valid_session->sessionid, 0, 20) . "...</p>";
        echo "<p>过期时间: {$valid_session->expire_time}</p>";
        
        // 测试用户检查函数
        if (class_exists('RAW_Util')) {
            $check_result = RAW_Util::checkUser($valid_session->sessionid, $test_userid);
            if ($check_result) {
                echo "<p style='color: green;'>✓ RAW_Util::checkUser 验证通过</p>";
            } else {
                echo "<p style='color: red;'>✗ RAW_Util::checkUser 验证失败</p>";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠ 没有找到有效会话</p>";
    }
} else {
    echo "<p style='color: red;'>✗ 没有找到测试用户</p>";
}

// 4. 前端调试建议
echo "<h2>4. 前端调试建议</h2>";
echo "<ol>";
echo "<li><strong>检查本地存储</strong>: 在小程序开发工具中查看 Storage > userSession</li>";
echo "<li><strong>检查登录流程</strong>: 确保用户已正确登录并获得有效的 userId 和 sessionId</li>";
echo "<li><strong>检查字段名</strong>: 确保使用正确的字段名 userId (不是 userid)</li>";
echo "<li><strong>检查会话有效期</strong>: 确保会话没有过期</li>";
echo "</ol>";

echo "<h2>5. 测试步骤</h2>";
echo "<ol>";
echo "<li>在小程序中先进行登录</li>";
echo "<li>在开发工具中检查 Storage 中的 userSession 数据</li>";
echo "<li>确认 userSession 包含 userId 和 sessionId 字段</li>";
echo "<li>然后尝试进入邻里互助列表页面</li>";
echo "<li>点击发布按钮测试是否还提示登录</li>";
echo "</ol>";

echo "<h2>6. 常见问题解决</h2>";
echo "<ul>";
echo "<li><strong>仍提示请先登录</strong>: 检查 userSession 是否存在且包含正确字段</li>";
echo "<li><strong>会话过期</strong>: 重新登录获取新的会话</li>";
echo "<li><strong>字段名错误</strong>: 确保使用 userId 而不是 userid</li>";
echo "<li><strong>数据格式问题</strong>: 确保 userId 是数字类型</li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>调试完成</strong></p>";
echo "<p><small>调试完成后请删除此文件：test_login_debug.php</small></p>";
?>
