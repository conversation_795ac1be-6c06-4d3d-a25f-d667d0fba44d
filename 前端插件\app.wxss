/*
 * 
 * 微慕小程序
 * author: jian<PERSON>
 * organization:  微慕 www.minapper.com 
 * 技术支持微信号：Jianbo
 * Copyright (c) 2018 https://www.minapper.com All rights reserved.
 */

/* @import "vendor/ZanUI/index.wxss"; */
@import "images/iconfont/iconfont.wxss";
@import "templates/member/memberIcon.wxss";
@import 'vendor/WeUI/style/weui.wxss';
page{
  font-family: "Source Han Sans CN", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.wxParse-p {
  margin-bottom: 30rpx;
}
.vip {
  background-color: #FFB74D !important;
}
/*  more   */

.loadingmore {
  margin-top: 24rpx;
  text-align: center;
  margin-bottom: 24rpx;
}

.more-button {
  font-size: 0.785714286rem;
  font-weight: normal;
  color: #959595;
  background-color: #eee;
  background-repeat: repeat-x;
  margin-top: 30rpx;
  width: 240rpx;
  border-radius: 300rpx;
}

.more-button::after {
  border: none;
}

.no-more {
  color: #c4c4c4;
  font-size: 28rpx;
  line-height: 1.8rem;
  margin-bottom: 10rpx;
  text-align: center;
  margin-top: 10rpx;
}

/*  more  end  */

.common-gap {
  width: 100%;
  height: 24rpx;
  background-color: #f5f7f7;
}

/* 搜索框样式 */

.search-pancel {
  padding: 16rpx 40rpx 0;
  background-color: #fff;
}

.search-pancel>view {
  display: flex;
  flex-direction: row;
  align-items: center;
  background: #f5f6f7;
}

.search-input {
  background: #f5f6f7;
  padding: 36rpx 0 36rpx 32rpx;
  min-height: 1rem;
  font-size: 30rpx;
  height: 48rpx;
  width: 80%;
  text-align: left;
}

.search-button {
  border: none !important;
  margin: 0 !important;
}

.search-button icon {
  vertical-align: middle;
  margin-top: -4rpx;
}

.showerror {
  text-align: center;
  font-weight: normal;
  font-size: 30rpx;
  line-height: 40rpx;
  color: #757575;
  margin-top: 60rpx;
}

.errortext {
  margin-top: 50rpx;
  table-layout: center;
}

/* 登录框 */

.login-popup .zan-popup__container {
  background-color: transparent;
}

.login-popup-wrapper {
  margin: 0 80rpx;
}

.login-popup .login-inner {
  background-color: #fff;
  padding: 60rpx 0 0;
  border-radius: 10rpx;
}

.login-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.login-inner .avatar {
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
}

.login-inner button {
  background-color: transparent;
  padding: 0;
}

.login-inner button::after {
  border: none;
}

.login-inner .close-btn {
  width: 20px;
  height: 20px;
}

.login-inner .username {
  display: flex;
  align-items: center;
}

.login-inner .username text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 32rpx;
  line-height: 3;
  font-weight: 700;
  color: #FF6B35;
}

.login-inner .tips {
  width: 80%;
  font-size: 30rpx;
  text-align: center;
  color: #959595;
  line-height: 1.8;
}

/* 仿新登录组件z-login样式 */
.login-info {
  background: #FFF;
  border-radius: 12rpx;
  overflow: hidden;
  text-align: center;
}

.login-phone {
  padding-top: 48rpx;
  background: #FF6B35;
}

.icon-login-phone {
  font-size: 160rpx !important;
  color: #FFF;
  line-height: 1;
}

.login-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 40rpx 0;
}

.login-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #333;
  line-height: 1.6;
}

.login-tip .iconfont {
  color: #999;
  margin-right: 10rpx;
}

.login-footer {
  padding: 60rpx 80rpx 40rpx;
}

.login-btn-ok {
  background: #FF6B35;
  height: 88rpx;
  border-radius: 44rpx;
  box-shadow: 4rpx 8rpx 24rpx rgba(255, 107, 53, 0.4);
  font-size: 16px;
  color: #fff;
  line-height: 88rpx;
}

.login-btn-ok::after {
  border: none;
}

.login-btn-box {
  display: flex;
  justify-content: center; 
}

.login-btn-cancel {
  font-size: 16px;
  color: #090808;
  margin-top: 24rpx;
}

.login-footer .iconfont {
  margin-right: 6rpx;
  color: #bbb;
}

.login-footer .icon-apple {
  font-size: 14px;
  margin-right: 4rpx;
}

.login-btn-box {
  display: flex;
  padding: 30rpx 0 10rpx;
  font-size: 14px;
  color: #999;
}

.login-btn-box .btn-wechat,
.login-btn-box .btn-apple {
  flex: 1;
  text-align: center;
  position: relative;
}

.login-btn-box .btn-apple::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  height: 30rpx;
  width: 1rpx;
  margin-top: -15rpx;
  background: #EEE;
}



/* 弹窗按钮 */

.popup-btn {
  display: flex;
  width: 100%;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
  text-align: left;
  border: 0;
  border-radius: 0;
  background-color: #fff;
}

.popup-btn .button-main {
  flex-grow: 1;
  overflow: hidden;
}

.popup-btn .go-popup {
  display: flex;
  align-items: center;
}

.popup-btn .go-popup image {
  width: 15px;
  height: 15px;
}

/* 登录弹窗 */

.login-popup .zan-popup__mask {
  z-index: 998;
}

.login-popup .zan-popup__container {
  z-index: 999;
}

.zan-popup .zan-popup__container {
  width: 100%;
  overflow: visible;
}

.zan-popup .popup-wrapper {
  width: 100%;
  padding: 0 15px;
  padding-bottom: 10px;
  overflow: visible;
}

.zan-popup .popup-wrapper .popup-title {
  font-size: 16px;
  padding: 15px 0;
}

.zan-popup .btn-primary.confirm {
  height: 50px;
  line-height: 50px;
  border-radius: 0;
}

.zan-popup {
  visibility: hidden;
}

.zan-popup--show {
  visibility: visible;
}

.zan-popup__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 997;
  background: rgba(0, 0, 0, 0.7);
  display: none;
}

.zan-popup__container {
  position: fixed;
  left: 50%;
  top: 50%;
  background: #fff;
  transform: translate3d(-50%, -50%, 0);
  transform-origin: center;
  transition: all 0.4s ease;
  z-index: 998;
  opacity: 0;
}

.zan-popup--show .zan-popup__container {
  opacity: 1;
}

.zan-popup--show .zan-popup__mask {
  display: block;
}

.zan-popup--left .zan-popup__container {
  left: 0;
  top: auto;
  transform: translate3d(-100%, 0, 0);
}

.zan-popup--show.zan-popup--left .zan-popup__container {
  transform: translate3d(0, 0, 0);
}

.zan-popup--right .zan-popup__container {
  right: 0;
  top: auto;
  left: auto;
  transform: translate3d(100%, 0, 0);
}

.zan-popup--show.zan-popup--right .zan-popup__container {
  transform: translate3d(0, 0, 0);
}

.zan-popup--bottom .zan-popup__container {
  top: auto;
  left: auto;
  bottom: 0;
  transform: translate3d(0, 100%, 0);
}

.zan-popup--show.zan-popup--bottom .zan-popup__container {
  transform: translate3d(0, 0, 0);
}

.zan-popup--top .zan-popup__container {
  top: 0;
  left: auto;
  transform: translate3d(0, -100%, 0);
}

.zan-popup--show.zan-popup--top .zan-popup__container {
  transform: translate3d(0, 0, 0);
}

/* 关注、已关注、相互关注按钮样式 */

.btn-follow {
  height: 54rpx;
  width: 128rpx;
  line-height: 54rpx;
  border-radius: 30rpx;
  font-size: 12px;
  text-align: center;
  color: #FF6B35;
  border: 2rpx solid #FF6B35;
}

.btn-followed {
  height: 54rpx;
  width: 128rpx;
  line-height: 54rpx;
  border-radius: 30rpx;
  font-size: 12px;
  text-align: center;
  color: #959595;
  border: 2rpx solid #c4c4c4;
}

.btn-follow-eachother {
  height: 54rpx;
  width: 128rpx;
  line-height: 54rpx;
  border-radius: 30rpx;
  font-size: 12px;
  text-align: center;
  color: #959595;
  border: 2rpx solid #c4c4c4;
}

/* 全局主色 */
.font-color {
  color: #FF6B35;
}

.bg-color {
  background-color: #FF6B35;
}

.border-color {
  border-color: #FF6B35;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

video {
  width: 100% !important;
}

/* 自定义字段展示（文章和动态详情）*/
.custom-filed-show {
  border: 1rpx solid #CCC;
  border-radius: 16rpx;
  margin-top: 20rpx;
  box-sizing: border-box;
  font-size: 14px;
}

.custom-filed-show .custom-filed-item:not(:last-child) {
  border-bottom: 1rpx solid #EEE;
}

.custom-filed-item {
  display: flex;
  padding: 30rpx 20rpx;
}

.custom-filed-item-label {
  width: 130rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.custom-filed-item-value {
  flex: 1;
  font-weight: 500;
}

/* 评论tab栏 */
.menu-box {
  position: relative;
  height: 80px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.menu-box-hidden {
  display: none !important;
}

.menuBackground {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: transparent;
  z-index: 99;
}

.menu-box-item {
  text-align: center;
  flex: 1;
  color: #666;
  font-size: 12px;
}

.menu-box-item .bg-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #aaa;
  color: #fff;
  line-height: 60rpx;
  display: inline-block;
  margin-bottom: 6rpx;
}

/* 点个赞 */
.likeBox {
  margin: 40rpx 0;
}

.likeBtn {
  width: 200rpx;
  height: 64rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #999;
  font-weight: 500;
  border: 1px solid #999;
  border-radius: 32rpx;
  margin: 0 auto;
}

.likeBtn-active {
  color: #FF6B35;
  border: 1px solid #FF6B35;
}

.likeBtn .icon-like {
  margin-right: 4rpx;
  font-size: 13px;
}

/* 点赞数 */
.likeNum {
  font-size: 28rpx;
  color: #959595;
  text-align: center;
  margin-top: 48rpx;
}

.likeNum>text {
  display: inline-block;
  position: relative;
}

.likeNum>text::before {
  content: "";
  display: block;
  width: 100rpx;
  height: 1px;
  background: #eee;
  position: absolute;
  left: -120rpx;
  top: 50%;
}

.likeNum>text::after {
  content: "";
  display: block;
  width: 100rpx;
  height: 1px;
  background: #eee;
  position: absolute;
  right: -120rpx;
  top: 50%;
}

/* 点赞头像 */
.likeGravatar {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin: 0 auto;
  margin-left: -20rpx;
  margin-top: 20rpx;
  padding: 0 60rpx;
}

.like-user-gravatar {
  position: relative;
  display: inline-block;
  padding-left: 20rpx;
  padding-bottom: 20rpx;
  width: 60rpx;
  overflow: visible;
}

.gravatarLikeImg {
  vertical-align: top;
  border-radius: 16rpx;
  height: 60rpx;
  width: 60rpx;
}

/* 文档字体图标颜色 */
.icon-doc-word {
  color: #5096f6;
}

.icon-doc-excel {
  color: #4eb66b;
}

.icon-doc-pdf {
  color: #f18350;
}

.icon-doc-ppt {
  color: #f8c44e;
}

.icon-doc-file {
  color: #cba376;
}

/* 右下角联系客服 */
.contact-service {
  position: fixed;
  bottom: 200rpx;
  right: 20rpx;
  z-index: 999;
  width: 88rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #FF6B35, #E65100);
  border-radius: 50%;
  box-shadow: 2rpx 4rpx 10rpx rgba(255, 107, 53, 0.3);
  overflow: hidden;
  margin-bottom: env(safe-area-inset-bottom);
}

.contact-service button {
  background: transparent;
  padding: 0;
  margin: 0;
}

.contact-service button::after {
  border: none;
}

.contact-service .iconfont {
  color: #FFF;
  font-size: 54rpx;
}

/* 微信小店首页 */
.wx_store_home .wx_store_home_img_desc {
	display: none;
}