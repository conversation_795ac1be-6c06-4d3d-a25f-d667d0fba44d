// pages/help/publish/publish.js
const app = getApp()
const config = require('../../../utils/config.js')
const Auth = require('../../../utils/auth.js')
const API = require('../../../utils/api.js')

Page({
  data: {
    // 表单数据
    formData: {
      title: '',
      content: '',
      urgency: 'normal',
      help_type: '家居维修', // 默认选择第一个类型
      points_reward: 0,
      location_info: '',
      contact_info: '',
      images: []
    },

    // 选项数据
    urgencyOptions: [
      { value: 'urgent', text: '紧急', icon: 'icon-fire' },
      { value: 'normal', text: '一般', icon: 'icon-clock' },
      { value: 'low', text: '不急', icon: 'icon-leaf' }
    ],
    typeOptions: [
      { text: '家居维修', value: '家居维修' },
      { text: '照顾宠物', value: '照顾宠物' },
      { text: '搬运物品', value: '搬运物品' },
      { text: '技能咨询', value: '技能咨询' },
      { text: '其他', value: '其他' }
    ],

    // 界面状态
    typeIndex: 0, // 默认选择第一个类型
    userPoints: 0,
    memberUserInfo: {}, // 用户会员信息
    loading: false,
    errors: {},
    shouldResetForm: false // 是否需要重置表单
  },

  onLoad(options) {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '发布互助需求'
    })

    // 获取用户积分
    this.getUserPoints()

    // 如果有草稿，加载草稿
    this.loadDraft()
  },

  onShow() {
    // 页面显示时检查是否需要清空表单
    // 只有在明确标记需要重置时才清空表单
    if (this.data.shouldResetForm) {
      this.resetFormData()
      this.setData({ shouldResetForm: false })
    }

    // 每次显示页面时更新用户积分
    this.getUserPoints()
  },

  // 获取用户积分
  getUserPoints() {
    console.log('=== 获取用户积分 ===');

    // 首先从本地存储获取用户会员信息
    const memberUserInfo = wx.getStorageSync('memberUserInfo');
    const userSession = wx.getStorageSync('userSession');

    if (memberUserInfo && memberUserInfo.integral !== undefined) {
      // 如果本地有积分数据，直接使用
      const userPoints = parseInt(memberUserInfo.integral) || 0;
      console.log('✅ 从本地存储获取积分:', userPoints);
      this.setData({
        userPoints: userPoints,
        memberUserInfo: memberUserInfo
      });
    } else if (userSession && userSession.sessionId) {
      // 如果本地没有积分数据，但有用户会话，则调用API获取
      console.log('📡 本地无积分数据，调用API获取');
      Auth.getMemberUserInfo(userSession, API).then(res => {
        if (res.memberUserInfo) {
          const userPoints = parseInt(res.memberUserInfo.integral) || 0;
          console.log('✅ 从API获取积分:', userPoints);

          // 更新页面数据
          this.setData({
            userPoints: userPoints,
            memberUserInfo: res.memberUserInfo
          });

          // 保存到本地存储
          wx.setStorageSync('memberUserInfo', res.memberUserInfo);
        } else {
          console.log('❌ API返回无会员信息');
          this.setData({ userPoints: 0 });
        }
      }).catch(error => {
        console.error('❌ 获取积分失败:', error);
        this.setData({ userPoints: 0 });
      });
    } else {
      // 没有用户会话，设置为0
      console.log('❌ 无用户会话，积分设为0');
      this.setData({ userPoints: 0 });
    }
  },

  // 加载草稿
  loadDraft() {
    try {
      const draft = wx.getStorageSync('help_publish_draft')
      if (draft) {
        this.setData({
          formData: { ...this.data.formData, ...draft }
        })

        // 设置类型选择器的索引
        if (draft.help_type) {
          const typeIndex = this.data.typeOptions.findIndex(item => item.value === draft.help_type)
          this.setData({ typeIndex })
        }
      }
    } catch (error) {
      console.error('加载草稿失败:', error)
    }
  },

  // 标题输入
  onTitleInput(e) {
    this.setData({
      'formData.title': e.detail.value,
      'errors.title': ''
    })
  },

  // 内容输入
  onContentInput(e) {
    this.setData({
      'formData.content': e.detail.value,
      'errors.content': ''
    })
  },

  // 类型选择
  onTypeChange(e) {
    const index = e.detail.value
    const selectedType = this.data.typeOptions[index]
    this.setData({
      typeIndex: index,
      'formData.help_type': selectedType.value,
      'errors.help_type': ''
    })
  },

  // 紧急程度选择
  onUrgencyChange(e) {
    const urgency = e.currentTarget.dataset.value
    this.setData({
      'formData.urgency': urgency
    })
  },

  // 积分输入
  onPointsInput(e) {
    const points = parseInt(e.detail.value) || 0
    this.setData({
      'formData.points_reward': points,
      'errors.points_reward': ''
    })
  },

  // 联系方式输入
  onContactInput(e) {
    this.setData({
      'formData.contact_info': e.detail.value,
      'errors.contact_info': ''
    })
  },

  // 选择图片
  onChooseImage() {
    const maxImages = 3
    const currentCount = this.data.formData.images.length

    if (currentCount >= maxImages) {
      wx.showToast({
        title: `最多只能上传${maxImages}张图片`,
        icon: 'none'
      })
      return
    }

    wx.chooseMedia({
      count: maxImages - currentCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFiles = res.tempFiles
        const newImages = [...this.data.formData.images]

        tempFiles.forEach(file => {
          newImages.push(file.tempFilePath)
        })

        this.setData({
          'formData.images': newImages
        })
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  // 预览图片
  onPreviewImage(e) {
    const index = e.currentTarget.dataset.index
    wx.previewImage({
      current: this.data.formData.images[index],
      urls: this.data.formData.images
    })
  },

  // 删除图片
  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index
    const images = [...this.data.formData.images]
    images.splice(index, 1)

    this.setData({
      'formData.images': images
    })
  },

  // 位置输入处理
  onLocationInput(e) {
    const value = e.detail.value;
    this.setData({
      'formData.location_info': value
    });

    // 清除位置相关的错误信息
    if (this.data.errors.location_info) {
      this.setData({
        'errors.location_info': ''
      });
    }
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data
    const errors = {}

    // 标题验证
    if (!formData.title.trim()) {
      errors.title = '请输入需求标题'
    } else if (formData.title.length < 5) {
      errors.title = '标题至少需要5个字符'
    }

    // 内容验证
    if (!formData.content.trim()) {
      errors.content = '请输入详细描述'
    } else if (formData.content.length < 10) {
      errors.content = '描述至少需要10个字符'
    }

    // 类型验证
    if (!formData.help_type) {
      errors.help_type = '请选择互助类型'
    }

    // 位置验证
    if (!formData.location_info.trim()) {
      errors.location_info = '请输入服务位置'
    } else if (formData.location_info.length < 2) {
      errors.location_info = '位置信息至少需要2个字符'
    }

    // 联系方式验证
    if (!formData.contact_info.trim()) {
      errors.contact_info = '请输入联系方式'
    }

    // 积分验证
    if (formData.points_reward > this.data.userPoints) {
      errors.points_reward = '积分奖励不能超过当前余额'
    }

    this.setData({ errors })
    return Object.keys(errors).length === 0
  },

  // 保存草稿
  onSaveDraft() {
    try {
      wx.setStorageSync('help_publish_draft', this.data.formData)
      wx.showToast({
        title: '草稿已保存',
        icon: 'success'
      })
    } catch (error) {
      console.error('保存草稿失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  // 发布需求
  onPublish() {
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    // 构建API URL
    const domain = config.default.getDomain
    const apiUrl = `https://${domain}/wp-json/minapper/v1/help/publish`

    // 获取用户登录信息
    const userSession = wx.getStorageSync('userSession')
    if (!userSession || !userSession.userId || !userSession.sessionId) {
      wx.showToast({
        title: '登录信息已过期，请重新登录',
        icon: 'none'
      })
      this.setData({ loading: false })
      return
    }

    // 准备提交数据
    const submitData = {
      ...this.data.formData,
      userid: userSession.userId,
      sessionid: userSession.sessionId
    }

    wx.request({
      url: apiUrl,
      method: 'POST',
      data: submitData,
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          // 清除草稿
          wx.removeStorageSync('help_publish_draft')

          // 重置表单数据
          this.resetFormData()

          wx.showToast({
            title: '发布成功',
            icon: 'success'
          })

          // 延迟跳转到列表页
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: res.data.message || '发布失败',
            icon: 'none'
          })
        }
      },
      fail: (error) => {
        console.error('发布失败:', error)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ loading: false })
      }
    })
  },

  // 重置表单数据
  resetFormData() {
    this.setData({
      formData: {
        title: '',
        content: '',
        urgency: 'normal',
        help_type: '家居维修', // 默认选择第一个类型
        points_reward: 0,
        location_info: '',
        contact_info: '',
        images: []
      },
      typeIndex: 0, // 默认选择第一个类型
      errors: {}
    })
  },

  // 页面卸载时保存草稿
  onUnload() {
    // 如果表单有内容且未发布，自动保存草稿
    const { formData } = this.data
    if (formData.title || formData.content) {
      this.onSaveDraft()
    }
  }
})