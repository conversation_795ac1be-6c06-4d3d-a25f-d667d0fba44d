/* pages/help/publish/publish.wxss */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.header {
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  padding: 40rpx 30rpx 60rpx;
  color: white;
  text-align: center;
}

.header .title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header .subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 表单区域 */
.form-section {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.required {
  color: #FF6B35;
  margin-left: 8rpx;
  font-size: 28rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

/* 输入框样式 */
.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
}

.input:focus {
  border-color: #FF6B35;
  background: white;
}

.input.error {
  border-color: #ff4757;
  background: #fff5f5;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
}

.textarea:focus {
  border-color: #FF6B35;
  background: white;
}

.textarea.error {
  border-color: #ff4757;
  background: #fff5f5;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.error-msg {
  color: #ff4757;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 图片上传 */
.image-upload {
  margin-top: 16rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
}

.add-image {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-image .iconfont {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 选择器样式 */
.picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fafafa;
}

.picker.error {
  border-color: #ff4757;
  background: #fff5f5;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker .iconfont {
  color: #999;
  font-size: 24rpx;
}

/* 紧急程度选择 */
.urgency-options {
  display: flex;
  gap: 20rpx;
  margin-top: 16rpx;
}

.urgency-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fafafa;
  transition: all 0.3s;
}

.urgency-item.active {
  border-color: #FF6B35;
  background: #fff5f0;
}

.urgency-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  color: white;
  font-size: 32rpx;
}

.urgency-icon.urgent {
  background: #ff4757;
}

.urgency-icon.normal {
  background: #ffa502;
}

.urgency-icon.low {
  background: #2ed573;
}

.urgency-text {
  font-size: 24rpx;
  color: #666;
}

/* 积分输入 */
.points-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.input.points {
  flex: 1;
  text-align: right;
}

.unit {
  font-size: 28rpx;
  color: #666;
}

.points-info {
  margin-top: 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.balance {
  display: block;
  font-size: 26rpx;
  color: #FF6B35;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.tip {
  font-size: 24rpx;
  color: #999;
}

/* 位置选择 */
.location-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fafafa;
}

.location-display {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.location-display.placeholder .location-text {
  color: #999;
}

.location-display .iconfont {
  color: #FF6B35;
  font-size: 28rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
}

.location-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

.contact-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 底部操作区 */
.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.btn-secondary {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border: 2rpx solid #FF6B35;
  border-radius: 40rpx;
  background: white;
  color: #FF6B35;
  font-size: 28rpx;
  text-align: center;
}

.btn-primary {
  flex: 2;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #FF6B35, #FFB74D);
  border-radius: 40rpx;
  color: white;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.btn-primary[disabled] {
  background: #ccc;
  color: #999;
}

.btn-secondary[disabled] {
  border-color: #ccc;
  color: #999;
}