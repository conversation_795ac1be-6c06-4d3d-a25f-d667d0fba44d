// pages/help/list/list.js
const app = getApp()
const config = require('../../../utils/config.js')
const Auth = require('../../../utils/auth.js')
const API = require('../../../utils/api.js')
const util = require('../../../utils/util.js')

Page({
  data: {
    helpList: [],
    keyword: '',
    urgencyFilter: '',
    typeFilter: '',
    urgencyOptions: [
      { text: '全部紧急程度', value: '' },
      { text: '紧急', value: 'urgent' },
      { text: '一般', value: 'normal' },
      { text: '不急', value: 'low' }
    ],
    typeOptions: [
      { text: '全部类型', value: '' },
      { text: '家居维修', value: '家居维修' },
      { text: '照顾宠物', value: '照顾宠物' },
      { text: '搬运物品', value: '搬运物品' },
      { text: '技能咨询', value: '技能咨询' },
      { text: '其他', value: '其他' }
    ],
    page: 1,
    per_page: 10,
    hasMore: true,
    loading: false,
    // 登录相关
    isLoginPopup: false,
    userInfo: {},
    userSession: {},
    hasWechatInstall: true,
    pendingAction: null, // 登录成功后要执行的操作
    isAuthenticating: false // 是否正在进行授权
  },

  onLoad: function (options) {
    this.loadHelpList()
  },

  onShow: function () {
    // 检查登录状态
    Auth.checkSession(app, API, this, 'isLoginLater', util)

    // 页面显示时刷新列表
    this.refreshList()
  },

  onHide: function () {
    // 页面隐藏时重置授权状态，防止在其他页面显示授权弹框
    this.setData({
      isAuthenticating: false
    })
  },

  onPullDownRefresh: function () {
    this.refreshList()
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 刷新列表
  refreshList: function () {
    this.setData({
      page: 1,
      helpList: [],
      hasMore: true
    })
    this.loadHelpList()
  },

  // 加载互助列表
  loadHelpList: function () {
    if (this.data.loading) return

    this.setData({ loading: true })

    const params = {
      page: this.data.page,
      per_page: this.data.per_page
    }

    if (this.data.keyword) {
      params.keyword = this.data.keyword
    }
    if (this.data.urgencyFilter) {
      params.urgency = this.data.urgencyFilter
    }
    if (this.data.typeFilter) {
      params.help_type = this.data.typeFilter
    }

    // 构建正确的API URL
    const domain = config.default.getDomain
    const apiUrl = `https://${domain}/wp-json/minapper/v1/help/list`

    wx.request({
      url: apiUrl,
      method: 'GET',
      data: params,
      success: (res) => {
        if (res.statusCode === 200 && res.data) {
          const newList = this.data.page === 1 ? res.data.list : [...this.data.helpList, ...res.data.list]
          
          this.setData({
            helpList: newList,
            hasMore: res.data.list.length === this.data.per_page,
            page: this.data.page + 1
          })
        } else {
          wx.showToast({
            title: '加载失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('加载互助列表失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ loading: false })
        wx.stopPullDownRefresh()
      }
    })
  },

  // 加载更多
  loadMore: function () {
    this.loadHelpList()
  },

  // 搜索关键词变化
  onKeywordChange: function (e) {
    this.setData({
      keyword: e.detail
    })
  },

  // 执行搜索
  onSearch: function () {
    this.refreshList()
  },

  // 紧急程度筛选变化
  onUrgencyChange: function (e) {
    this.setData({
      urgencyFilter: e.detail
    })
    this.refreshList()
  },

  // 类型筛选变化
  onTypeChange: function (e) {
    this.setData({
      typeFilter: e.detail
    })
    this.refreshList()
  },

  // 跳转到详情页
  goToDetail: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/help/detail/detail?id=${id}`
    })
  },

  // 跳转到发布页
  goToPublish: function () {
    // 检查用户登录状态 - 使用与项目其他页面一致的检查方式
    let userSession = wx.getStorageSync('userSession')

    // 检查本地存储的用户会话信息
    if (!userSession || !userSession.userId || !userSession.sessionId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })

      // 显示登录弹窗
      this.setData({
        isLoginPopup: true,
        pendingAction: 'publish' // 标记登录成功后要执行的操作
      })
      return
    }

    // 检查页面数据中的用户会话（如果存在）
    if (this.data.userSession && this.data.userSession.userId) {
      // 使用页面数据中的用户信息
      wx.navigateTo({
        url: '/pages/help/publish/publish'
      })
    } else {
      // 使用本地存储的用户信息
      wx.navigateTo({
        url: '/pages/help/publish/publish'
      })
    }
  },

  // 处理用户登录授权
  agreeGetUser(e) {
    // 检查页面是否仍然活跃，防止在页面切换时调用授权
    if (!this.data.isLoginPopup) {
      console.log('登录弹窗已关闭，取消授权操作');
      return;
    }

    // 防止重复调用
    if (this.data.isAuthenticating) {
      console.log('正在进行授权，请勿重复操作');
      return;
    }

    // 设置授权状态
    this.setData({ isAuthenticating: true });

    // 使用自定义的登录处理逻辑，直接在当前页面调用getUserProfile
    let wxLoginInfo = wx.getStorageSync('wxLoginInfo');
    if (wxLoginInfo.js_code) {
      // 直接在当前页面调用getUserProfile，确保弹框在正确位置显示
      wx.getUserProfile({
        lang: 'zh_CN',
        desc: '登录后信息展示',
        success: (profileRes) => {
          // 获取用户信息成功，调用登录接口
          let args = {
            js_code: wxLoginInfo.js_code,
            userInfo: profileRes.userInfo
          };

          wx.showLoading({ title: '登录中...' });

          API.wxUserLogin(args).then(res => {
            wx.hideLoading();
            this.setData({ isAuthenticating: false });

            console.log('登录结果:', res);
            if (res.errcode == "") {
              // 登录成功
              this.setData({
                userInfo: res.userInfo,
                userSession: res.userSession,
                isLoginPopup: false
              });

              // 保存用户信息到本地存储
              wx.setStorageSync('userInfo', res.userInfo);
              wx.setStorageSync('userSession', res.userSession);

              // 获取会员信息
              Auth.getMemberUserInfo(res.userSession, API).then(response => {
                if (response.memberUserInfo) {
                  this.setData({ memberUserInfo: response.memberUserInfo });
                  wx.setStorageSync('memberUserInfo', response.memberUserInfo);
                }
              });

              // 执行登录成功后的操作
              if (this.data.pendingAction === 'publish') {
                // 跳转到发布页面
                wx.navigateTo({
                  url: '/pages/help/publish/publish'
                });
              }

              // 清除待执行操作
              this.setData({ pendingAction: null });

              wx.showToast({
                title: '登录成功',
                icon: 'success'
              });
            } else {
              // 登录失败
              this.setData({ isLoginPopup: false });
              wx.showToast({
                title: '登录失败，请重试',
                icon: 'none'
              });
            }
          }).catch(error => {
            wx.hideLoading();
            this.setData({ isAuthenticating: false });
            console.error('登录错误:', error);
            wx.showToast({
              title: '登录失败，请重试',
              icon: 'none'
            });
          });
        },
        fail: (err) => {
          wx.hideLoading();
          this.setData({ isAuthenticating: false });

          let errorMsg = '授权失败';
          if (err.errMsg === 'getUserProfile:fail auth deny') {
            errorMsg = '用户拒绝了授权';
          } else if (err.errMsg === 'getUserProfile:fail privacy permission is not authorized') {
            errorMsg = '您未同意用户隐私协议，将无法登录本小程序';
          }

          wx.showToast({
            icon: 'none',
            title: errorMsg,
            duration: 2000
          });

          console.log('getUserProfile失败:', err);
        }
      });
    } else {
      // 没有微信登录信息，需要重新获取
      this.setData({ isAuthenticating: false });
      wx.login({
        success: (loginRes) => {
          if (loginRes.code) {
            wx.setStorageSync('wxLoginInfo', { js_code: loginRes.code });
            // 重新调用登录
            this.agreeGetUser(e);
          } else {
            this.setData({ isAuthenticating: false });
            wx.showToast({
              title: '获取登录信息失败',
              icon: 'none'
            });
          }
        },
        fail: () => {
          this.setData({ isAuthenticating: false });
          wx.showToast({
            title: '获取登录信息失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 关闭登录弹窗
  closeLoginPopup() {
    this.setData({
      isLoginPopup: false,
      pendingAction: null,
      isAuthenticating: false
    })
  },

  // 格式化时间显示
  formatTime: function (timeStr) {
    const time = new Date(timeStr)
    const now = new Date()
    const diff = now - time
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前'
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前'
    } else {
      return Math.floor(diff / 86400000) + '天前'
    }
  }
})
