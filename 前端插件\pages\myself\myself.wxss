page {
  background: #F5F7F7;
}

/* 用户信息 */
.header {
  padding: 160rpx 40rpx 48rpx;
  background: #FFF;
  position: relative;
}

.header .icon-bg {
  font-size: 200rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  opacity: .05;
}

.header .user {
  display: flex;
  align-items: center;
}

.header .user .avatar {
  width: 140rpx;
  height: 140rpx;
  border: 1rpx solid #EEE;
  border-radius: 50%;
  flex-shrink: 0;
}

.header .user .info {
  flex: 1;
  margin-left: 28rpx;
  position: relative;
}

.header .user .info .icon-arrow-right {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #ccc;
}

.header .user .info .name {
  font-size: 40rpx;
  font-weight: 500;
}

.header .user .info .member {
  font-weight: 13px;
  color: #c69b5b;
}

.description {
  margin-top: 8rpx;
  width: 420rpx;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  font-size: 13px;
  font-weight: 300;
  color:#999;
}

/* 数据展示 */
.header .data {
  display: flex;
  align-items: center;
  margin-top: 48rpx;
}

.header .data .divider {
  width: 1rpx;
  height: 60rpx;
  background: #ddd;
}

.header .data .item {
  flex: 1;
  text-align: center;
}

.header .data .item .label {
  font-size: 14px;
  color: #666;
}

.header .data .item .num {
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

/* 卡片公用样式 */
.card {
  margin: 24rpx;
  border-radius: 4px;
  background: #FFF;
}

.card .title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding: 40rpx 40rpx 20rpx;
}

.card .item {
  padding: 16rpx 10rpx 24rpx;
  text-align: center;
}

.card .item-btn .label {
  font-size: 13px;
  color: #333;
}

.card .item-btn {
  padding: 16rpx 10rpx 24rpx;
  margin: 0;
  line-height: 1.6;
  background-color: #fff;
}

.card .item-btn::after {
  border: none;
}

.card .item .iconfont {
  font-size: 36rpx;
  color: #666;
}

.card .item .label {
  font-size: 13px;
}

.card .item .val-red {
  position: relative;
}

.card .item .val-red .tag {
  position: absolute;
  top: 0;
  right: -2rpx;
  height: 32rpx;
  border-radius: 16rpx 16rpx 16rpx 0;
  padding: 0 12rpx;
  background: #fe645a;
  font-size: 10px;
  color: #fff;
  line-height: 32rpx;
}

/* vip会员 */
.card-vip {
  border-radius: 20rpx 20rpx 0 0;
  background: linear-gradient(to right, #FFB74D, #FF6B35);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 28rpx 48rpx 40rpx;
  margin-bottom: -50rpx;
}

.vip-des {
  font-size: 14px;
  color: #fff;
  line-height: 1;
  display: flex;
  align-items: center;
}

.vip-des .icon-vip {
  margin-right: 6rpx;
}

.vip-des .small {
  font-size: 10px;
  margin-top: 6rpx;
  color: #999;
}

.card-vip .point-btn {
  height: 60rpx;
  padding: 0 32rpx;
  margin-left: 20rpx;
  font-size: 12px;
  font-weight: 600;
  color: #fff;
  text-align: center;
  line-height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #E65100, #FF6B35);
}

/* 积分兑换 */
.card-point {
  background: linear-gradient(to right, #FFB74D, #FF6B35);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 24rpx 24rpx 40rpx;
  /* box-shadow: 2rpx 2rpx 6rpx rgba(100, 80, 20, 0.1); */
}

.point-des {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  line-height: 1;
  display: flex;
  align-items: center;
}

.point-des .icon-arrow-right {
  font-size: 24rpx;
  color: #fff;
  margin-left: 2rpx;
  display: block;
}

.point-des .icon-point {
  margin-right: 6rpx;
}

.card-point .point-btn {
  height: 60rpx;
  padding: 0 32rpx;
  margin-left: 20rpx;
  font-size: 12px;
  font-weight: 600;
  color: #FF6B35;
  text-align: center;
  line-height: 60rpx;
  border-radius: 30rpx;
  background: #fff;
}

/* 订阅消息 */
.subscribe .content {
   padding:0 24rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.subscribe-info {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 13px;
  color: #959595;
}

.subscribe .content .tip {
  font-size: 12px;
  color: #999;
}

.subscribe-btn {
  height: 48rpx;
  padding: 0 30rpx;
  margin-left: 20rpx;
  font-size: 12px;
  font-weight: 500;
  color: #FFF;
  text-align: center;
  line-height: 48rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #FF6B35, #E65100);
}