const app = getApp()

Component({
  data: {
    topicId: '',
    topicInfo: {},
    list: [],
    memberUserInfo: {},
    activeTab: 0,
    isIOS: false,
  },

  methods: {
    onLoad: function(e) {
      wx.showLoading({
        title: '加载中',
      })

      this.setData({
        isIOS: /ios/i.test(app.globalData.deviceInfo.system),
        topicId: e.id,
      })
      this.setUserInfo()
      this.getTopicInfo()
      this.getList()
      // 设置系统分享菜单
      wx.showShareMenu({
        withShareTicket: false,
        menus: ['shareAppMessage', 'shareTimeline']
      })
    },

    onShareAppMessage() {
      const { cover, term_id, name } = this.data.topicInfo
      const path = `/subpages/topic/posts?id=${term_id}`
      return {
        title: name,
        path: path,
        imageUrl: cover,
      }
    },

    // 自定义分享朋友圈
    onShareTimeline: function () {
      const {cover,term_id,name } = this.data.topicInfo    
      let appName = getApp().globalData.appName
      return {
        title: appName + '专栏:' + name,
        query: {
          id: term_id
        },
        imageUrl:cover
      }
    },

    async getTopicInfo() {
      let userInfo = wx.getStorageSync('userSession')
      let params = {
        id: this.data.topicId,
        sessionid: userInfo.sessionId,
        userid: userInfo.userId,
      }
      const res = await app.$api.getTopicDetail(params)
      const list = res || []
      const info = list[0] || {}
      this.setData({
        topicInfo: info
      })
      const name = info.name
      name && wx.setNavigationBarTitle({
        title: name,
      })
      wx.hideLoading()
    },

    async getList() {
      let userInfo = wx.getStorageSync('userSession')
      let params = {
        sessionid: userInfo.sessionId || undefined,
        userid: userInfo.userId || undefined,
        categories: this.data.topicId,
        page: 1,
        per_page: 999, // 暂时不分页，直接传一个比较大的数值
      }
      const res = await app.$api.getTopicPostList(params)
      let list = res || []
      this.setData({
        list
      })
    },

    redirectDetail(e) {
      const { id, trysee } = e.currentTarget.dataset
      const { memberUserInfo, topicInfo } = this.data
      const member = memberUserInfo.member
      const categorytype = topicInfo.categorytype

      if (trysee === 'disenable') {
        if (!this.checkLogin()) {
          this.onLogin()
          return
        }
        if (member != '00') {
          if (topicInfo.paytype === '1' && topicInfo.payflag === '0') {
            if (topicInfo.vipflag === '1') {
              if (member == '01') {
                wx.navigateTo({
                  url: `/subpages/topic/detail?id=${id}&type=${categorytype}`
                })
              } else {
                wx.showToast({
                  title: "购买专栏后查看",
                  icon: "none",
                  duration: 3000
                })
              }
            } else {
              wx.showToast({
                title: "购买专栏后查看",
                icon: "none",
                duration: 3000
              })
            }
          } else if ((topicInfo.paytype === '1' && topicInfo.payflag === '1') || topicInfo.paytype === '0') {
            wx.navigateTo({
              url: `/subpages/topic/detail?id=${id}&type=${categorytype}`
            })
          }
        } else {
          wx.navigateTo({
            url: `/subpages/topic/detail?id=${id}&type=${categorytype}`
          })
        }
      } else {
        wx.navigateTo({
          url: `/subpages/topic/detail?id=${id}&type=${categorytype}`
        })
      }
    },

    async handleBuy() {
      if (!this.checkLogin()) {
        this.onLogin()
        return
      }

      const userInfo = wx.getStorageSync('userSession')
      let memberUserInfo = wx.getStorageSync('memberUserInfo');
      var userIntegral = parseInt(memberUserInfo.integral);
      const { topicId, topicInfo, isIOS } = this.data
      const totalfee = topicInfo.saleprice
      const integral = topicInfo.saleintegral
      let params = {
        sessionid: userInfo.sessionId,
        userid: userInfo.userId,
        extid: topicId,
        // integral: +topicInfo.saleintegral,
        // totalfee,
      }
      if (isIOS) {
        params.integral = +topicInfo.saleintegral
        params.totalfee = 0
        params.os = "ios";
      } else {
        params.totalfee = totalfee
        params.integral = 0
        params.os = "android";
      }

      // #if NATIVE
      if (params.totalfee > 0) {
        wx.showToast({
          title: '暂不支持现金支付，请在微信小程序中使用此功能！',
          icon: "none"
        })
        return
      }
      // #endif

      if (!isIOS && totalfee > 0) {
        const res = await app.$api.payTopic(params)
        if (res.code === 'error') {
          wx.showToast({
            icon: 'none',
            title: res.message || '支付失败，请稍后重试',
          })
          return
        }
        const orderid = res.orderid || {}
        const order = res.order
        if (orderid) {
          wx.requestPayment({
            'timeStamp': res.timeStamp,
            'nonceStr': res.nonceStr,
            'package': res.package,
            'signType': res.signType,
            'paySign': res.paySign,
            'success': (r) => {
              wx.showToast({
                title: '支付成功',
                icon: 'success',
                duration: 1000,
                mask: true,
                success: (res) => {
                  this.updateOrder({
                    sessionid: userInfo.sessionId,
                    userid: userInfo.userId,
                    order: order
                  })
                }
              })
            },
            'fail': function(res) {
              wx.showToast({
                title: res.errMsg,
                mask: false,
                icon: "none",
                duration: 1e3
              });
            },
            complete: function(res) {
              if (res.errMsg == 'requestPayment:fail cancel') {
                wx.showToast({
                  title: '你取消了支付',
                  mask: false,
                  icon: "none",
                  duration: 1e3
                });
              }
            }
          })
        } else {
          wx.showToast({
            icon: 'none',
            title: '支付失败，请稍后重试',
          })
        }
      } else if (isIOS && integral > 0) {
        if (userIntegral < parseInt(integral)) {
          var _integral = parseInt(integral) - userIntegral
          wx.z.showDialog({
            type: "confirm",
            title: "提示",
            confirmText: "确认",
            // isCoverView: true,
            content: "积分不足,还需要" + _integral + "积分，是否去赚积分?",
            success: (res) => {
              if (res.confirm) {
                wx.navigateTo({
                  url: '../earnIntegral/earnIntegral'
                })
              }
            }
          })
          return
        } else {
          wx.z.showDialog({
            type: "confirm",
            title: "提示",
            confirmText: "确认",
            // isCoverView: true,
            content: "将使用积分" + integral + ",确认使用？",
            success: (res) => {
              if (res.confirm) {
                app.$api.payTopic(params).then(res => {
                  if (res.code == 'error') {
                    wx.showToast({
                      title: res.message,
                      mask: false,
                      icon: "none",
                      duration: 3000
                    });
                  } else {
                    wx.showToast({
                      title: '购买成功',
                    })
                    this.getTopicInfo()
                  }
                })
              }
            }
          })
        }
      }
    },

    updateOrder(params) {
      app.$api.updateTopicOrder(params).then(res => {
        if (res.code === 'error') {
          wx.showToast({
            icon: 'none',
            title: res.message || '更新订单出错',
          })
          return
        }
        if (res.success) {
          this.getTopicInfo()
          wx.showToast({
            title: '购买成功',
          })
        }
      })
    },

    handleBuyVIP() {
      if (!this.checkLogin()) {
        this.onLogin()
        return
      }
      wx.navigateTo({
        url: '/pages/buyvip/buyvip',
      })
    },

    checkLogin() {
      let userInfo = wx.getStorageSync('userSession')
      if (!userInfo.sessionId || !userInfo.userId) {
        return false
      } else {
        return true
      }
    },

    // 登录
    onLogin() {
      wx.z.showLogin({
        success: () => {
          this.setUserInfo()
        }
      })
    },

    setUserInfo() {
      const memberUserInfo = wx.getStorageSync('memberUserInfo') || {}
      this.setData({
        memberUserInfo
      })
    },

    switchTab(e) {
      const activeTab = e.currentTarget.dataset.idx
      this.setData({
        activeTab
      })
    },

    // dount分享
    dountShare() {
      const { term_id, name } = this.data.topicInfo
      wx.miniapp.shareMiniProgramMessage({
        userName: app.globalData.gh_id,
        path: 'subpages/topic/posts?id=' + term_id,
        imagePath: '/images/uploads/logo.png',
        webpageUrl: 'https://m.wolinwarm.com',
        title: name,
        description: app.globalData.appDes,
        withShareTicket: false,
        miniprogramType: 0,
        scene: 0,
        success(res) {
          // console.log('分享小程序成功:' + res)
        }
      })
    }
  }
})