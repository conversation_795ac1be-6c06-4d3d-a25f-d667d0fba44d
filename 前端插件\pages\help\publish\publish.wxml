<!--pages/help/publish/publish.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">发布互助需求</view>
    <view class="subtitle">让邻里之间的温暖传递</view>
  </view>

  <!-- 表单区域 -->
  <form bindsubmit="onSubmit" bindreset="onReset">
    <!-- 基础信息组 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">基础信息</text>
        <text class="required">*</text>
      </view>

      <!-- 标题输入 -->
      <view class="form-item">
        <view class="label">需求标题</view>
        <input
          class="input {{errors.title ? 'error' : ''}}"
          placeholder="简要描述您的互助需求"
          value="{{formData.title}}"
          bindinput="onTitleInput"
          maxlength="50"
        />
        <view class="char-count">{{formData.title.length}}/50</view>
        <view class="error-msg" wx:if="{{errors.title}}">{{errors.title}}</view>
      </view>

      <!-- 详细描述 -->
      <view class="form-item">
        <view class="label">详细描述</view>
        <textarea
          class="textarea {{errors.content ? 'error' : ''}}"
          placeholder="详细描述您的需求，包括具体要求、时间安排等"
          value="{{formData.content}}"
          bindinput="onContentInput"
          maxlength="500"
          auto-height
        />
        <view class="char-count">{{formData.content.length}}/500</view>
        <view class="error-msg" wx:if="{{errors.content}}">{{errors.content}}</view>
      </view>

      <!-- 图片上传 -->
      <view class="form-item">
        <view class="label">相关图片</view>
        <view class="image-upload">
          <view class="image-list">
            <view class="image-item" wx:for="{{formData.images}}" wx:key="index">
              <image src="{{item}}" mode="aspectFill" bindtap="onPreviewImage" data-index="{{index}}"/>
              <view class="delete-btn" bindtap="onDeleteImage" data-index="{{index}}">
                <text class="iconfont icon-close"></text>
              </view>
            </view>
            <view class="add-image" wx:if="{{formData.images.length < 3}}" bindtap="onChooseImage">
              <text class="iconfont icon-add"></text>
              <text class="add-text">添加图片</text>
            </view>
          </view>
          <view class="upload-tip">最多上传3张图片，支持jpg、png格式</view>
        </view>
      </view>
    </view>

    <!-- 分类设置组 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">分类设置</text>
        <text class="required">*</text>
      </view>

      <!-- 互助类型 -->
      <view class="form-item">
        <view class="label">互助类型</view>
        <picker
          bindchange="onTypeChange"
          value="{{typeIndex}}"
          range="{{typeOptions}}"
          range-key="text"
        >
          <view class="picker {{errors.help_type ? 'error' : ''}}">
            <text class="picker-text">{{formData.help_type || '请选择互助类型'}}</text>
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </picker>
        <view class="error-msg" wx:if="{{errors.help_type}}">{{errors.help_type}}</view>
      </view>

      <!-- 紧急程度 -->
      <view class="form-item">
        <view class="label">紧急程度</view>
        <view class="urgency-options">
          <view
            class="urgency-item {{formData.urgency === item.value ? 'active' : ''}}"
            wx:for="{{urgencyOptions}}"
            wx:key="value"
            bindtap="onUrgencyChange"
            data-value="{{item.value}}"
          >
            <view class="urgency-icon {{item.value}}">
              <text class="iconfont {{item.icon}}"></text>
            </view>
            <view class="urgency-text">{{item.text}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 奖励设置组 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">积分奖励</text>
      </view>

      <view class="form-item">
        <view class="label">奖励积分</view>
        <view class="points-input">
          <input
            class="input points"
            type="number"
            placeholder="0"
            value="{{formData.points_reward}}"
            bindinput="onPointsInput"
          />
          <text class="unit">积分</text>
        </view>
        <view class="points-info">
          <text class="balance">当前余额：{{userPoints}}积分</text>
          <text class="tip">设置积分奖励可以吸引更多邻居帮助</text>
        </view>
        <view class="error-msg" wx:if="{{errors.points_reward}}">{{errors.points_reward}}</view>
      </view>
    </view>

    <!-- 位置信息组 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">位置信息</text>
      </view>

      <view class="form-item">
        <view class="label">服务位置</view>
        <view class="input-wrapper">
          <text class="iconfont icon-location input-icon"></text>
          <input
            class="form-input"
            placeholder="请输入服务位置（如：XX小区、XX街道等）"
            value="{{formData.location_info}}"
            bindinput="onLocationInput"
            maxlength="100"
          />
        </view>
        <view class="location-tip">请输入具体的服务位置，帮助邻居更好地了解需求</view>
        <view class="error-msg" wx:if="{{errors.location_info}}">{{errors.location_info}}</view>
      </view>
    </view>

    <!-- 联系方式组 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">联系方式</text>
        <text class="required">*</text>
      </view>

      <view class="form-item">
        <view class="label">联系方式</view>
        <input
          class="input {{errors.contact_info ? 'error' : ''}}"
          placeholder="请输入微信号、手机号等联系方式"
          value="{{formData.contact_info}}"
          bindinput="onContactInput"
          maxlength="50"
        />
        <view class="contact-tip">联系方式仅在需求被响应后显示给帮助者</view>
        <view class="error-msg" wx:if="{{errors.contact_info}}">{{errors.contact_info}}</view>
      </view>
    </view>
  </form>

  <!-- 底部操作区 -->
  <view class="footer-actions">
    <button class="btn-secondary" bindtap="onSaveDraft" disabled="{{loading}}">
      保存草稿
    </button>
    <button class="btn-primary" bindtap="onPublish" disabled="{{loading}}">
      {{loading ? '发布中...' : '立即发布'}}
    </button>
  </view>
</view>