<!--pages/help/list/list.wxml-->
<import src="../../../templates/login-popup.wxml" />
<view class="container">
  <!-- 顶部搜索和筛选 -->
  <view class="search-bar">
    <view class="search-input">
      <van-field
        value="{{ keyword }}"
        placeholder="搜索互助需求..."
        border="{{ false }}"
        bind:change="onKeywordChange"
        bind:confirm="onSearch"
        use-button-slot
      >
        <van-button slot="button" size="small" type="primary" bind:click="onSearch">
          搜索
        </van-button>
      </van-field>
    </view>
    
    <view class="filter-bar">
      <van-dropdown-menu>
        <van-dropdown-item value="{{ urgencyFilter }}" options="{{ urgencyOptions }}" bind:change="onUrgencyChange" />
        <van-dropdown-item value="{{ typeFilter }}" options="{{ typeOptions }}" bind:change="onTypeChange" />
      </van-dropdown-menu>
    </view>
  </view>

  <!-- 互助列表 -->
  <view class="help-list">
    <view wx:for="{{ helpList }}" wx:key="id" class="help-item" bind:tap="goToDetail" data-id="{{ item.id }}">
      <view class="help-header">
        <view class="urgency-tag urgency-{{ item.urgency }}">
          {{ item.urgency === 'urgent' ? '紧急' : item.urgency === 'normal' ? '一般' : '不急' }}
        </view>
        <view class="help-type">{{ item.help_type }}</view>
        <view class="points-reward" wx:if="{{ item.points_reward > 0 }}">
          <van-icon name="gold-coin-o" />
          {{ item.points_reward }}积分
        </view>
      </view>
      
      <view class="help-title">{{ item.post_title }}</view>
      <view class="help-content">{{ item.post_content }}</view>
      
      <view class="help-footer">
        <view class="user-info">
          <van-icon name="user-o" />
          {{ item.user_name }}
        </view>
        <view class="location-info" wx:if="{{ item.location_info }}">
          <van-icon name="location-o" />
          {{ item.location_info }}
        </view>
        <view class="time-info">
          {{ item.created_at }}
        </view>
      </view>
      
      <view class="help-status status-{{ item.status }}">
        {{ item.status === 'pending' ? '待响应' : item.status === 'in_progress' ? '进行中' : '已完成' }}
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{ hasMore }}">
    <van-button 
      loading="{{ loading }}" 
      bind:click="loadMore" 
      type="default" 
      size="small"
      block
    >
      {{ loading ? '加载中...' : '加载更多' }}
    </van-button>
  </view>

  <!-- 空状态 -->
  <van-empty wx:if="{{ !helpList.length && !loading }}" description="暂无互助需求" />

  <!-- 发布按钮 -->
  <view class="publish-btn">
    <van-button 
      type="primary" 
      round 
      icon="plus" 
      bind:click="goToPublish"
      color="#FF6B35"
    >
      发布互助
    </van-button>
  </view>
</view>

<!-- 登录弹窗 -->
<template is="login-popup" data="{{show:isLoginPopup,userInfo:userInfo,hasWechatInstall:hasWechatInstall}}"></template>
