-- MySQL dump 10.13  Distrib 8.0.36, for Linux (x86_64)
--
-- Host: localhost    Database: wolinwarm_com
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `wo_commentmeta`
--

DROP TABLE IF EXISTS `wo_commentmeta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_commentmeta` (
  `meta_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `comment_id` bigint unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`meta_id`),
  KEY `comment_id` (`comment_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_comments`
--

DROP TABLE IF EXISTS `wo_comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_comments` (
  `comment_ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `comment_post_ID` bigint unsigned NOT NULL DEFAULT '0',
  `comment_author` tinytext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `comment_author_email` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_author_url` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_author_IP` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_date_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_content` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `comment_karma` int NOT NULL DEFAULT '0',
  `comment_approved` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '1',
  `comment_agent` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_type` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'comment',
  `comment_parent` bigint unsigned NOT NULL DEFAULT '0',
  `user_id` bigint unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`comment_ID`),
  KEY `comment_post_ID` (`comment_post_ID`),
  KEY `comment_approved_date_gmt` (`comment_approved`,`comment_date_gmt`),
  KEY `comment_date_gmt` (`comment_date_gmt`),
  KEY `comment_parent` (`comment_parent`),
  KEY `comment_author_email` (`comment_author_email`(10))
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_links`
--

DROP TABLE IF EXISTS `wo_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_links` (
  `link_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `link_url` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_image` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_target` varchar(25) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_description` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_visible` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'Y',
  `link_owner` bigint unsigned NOT NULL DEFAULT '1',
  `link_rating` int NOT NULL DEFAULT '0',
  `link_updated` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `link_rel` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_notes` mediumtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `link_rss` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`link_id`),
  KEY `link_visible` (`link_visible`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_cache`
--

DROP TABLE IF EXISTS `wo_minapper_cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_cache` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `cachekey` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cachevalue` longtext COLLATE utf8mb4_unicode_ci,
  `cachetype` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pagetype` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pageid` bigint DEFAULT NULL,
  `status` bigint DEFAULT '0',
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `lifetime` timestamp NULL DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `cachekey` (`cachekey`(768)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_cardcode`
--

DROP TABLE IF EXISTS `wo_minapper_cardcode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_cardcode` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint unsigned DEFAULT NULL COMMENT '用户id',
  `code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '卡密代码',
  `codetype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '卡密类型 0 无 money 现金直减 integral 积分充值 discount折扣',
  `userobject` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '使用范围或对象 全场通用 all 全部专题(分类) allcat 全部文章 allpost 指定专题(分类) specialcat  指定文章 specialpost',
  `objectid` bigint unsigned DEFAULT NULL COMMENT '专题(分类)或文章id',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '状态 0未使用 1已使用',
  `salemoney` float(6,2) DEFAULT NULL COMMENT '直减金额',
  `saleintegral` bigint unsigned DEFAULT NULL COMMENT '积分数量',
  `salediscount` float(2,1) DEFAULT NULL COMMENT '折扣',
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `enddate` timestamp NULL DEFAULT NULL COMMENT '到期时间',
  `usedate` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `orderid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单id',
  PRIMARY KEY (`ID`),
  KEY `userid` (`userid`) USING BTREE,
  KEY `codetype` (`codetype`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `orderid` (`orderid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_cooperation_shop`
--

DROP TABLE IF EXISTS `wo_minapper_cooperation_shop`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_cooperation_shop` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `appid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nickname` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vip` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bind_time` timestamp NULL DEFAULT NULL,
  `unbind_time` timestamp NULL DEFAULT NULL,
  `cancel_time` timestamp NULL DEFAULT NULL,
  `longitude` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `latitude` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `location` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `appid` (`appid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_cooperation_shop_order`
--

DROP TABLE IF EXISTS `wo_minapper_cooperation_shop_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_cooperation_shop_order` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tousername` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `openid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `unionid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order_id` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shop_appid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order_detail` longtext COLLATE utf8mb4_unicode_ci,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `order_id` (`order_id`) USING BTREE,
  KEY `tousername` (`tousername`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_cooperation_shop_product`
--

DROP TABLE IF EXISTS `wo_minapper_cooperation_shop_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_cooperation_shop_product` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shop_appid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `min_price` bigint unsigned DEFAULT NULL,
  `img_urls` longtext COLLATE utf8mb4_unicode_ci,
  `category` bigint unsigned DEFAULT NULL,
  `preferred` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT '0',
  `salesvolume` bigint unsigned DEFAULT NULL,
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `product_detail` longtext COLLATE utf8mb4_unicode_ci,
  `product_type` bigint DEFAULT NULL,
  `qrcode` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `taglink` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `h5url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sub_title` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `head_imgs` longtext COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`ID`),
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_custom_fields`
--

DROP TABLE IF EXISTS `wo_minapper_custom_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_custom_fields` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `posttypes` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fieldname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fieldkey` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `datatype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `orderby` bigint DEFAULT '0',
  `datalength` bigint DEFAULT '100',
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`ID`),
  KEY `fieldname` (`fieldname`) USING BTREE,
  KEY `fieldkey` (`fieldkey`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_custom_form`
--

DROP TABLE IF EXISTS `wo_minapper_custom_form`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_custom_form` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `author` bigint DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `excerpt` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent` bigint DEFAULT NULL,
  `category` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`ID`),
  KEY `title` (`title`) USING BTREE,
  KEY `author` (`author`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_ext`
--

DROP TABLE IF EXISTS `wo_minapper_ext`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_ext` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint unsigned DEFAULT NULL,
  `extid` bigint unsigned DEFAULT NULL,
  `extype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extkey` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extvalue` longtext COLLATE utf8mb4_unicode_ci,
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`ID`),
  KEY `extid` (`extid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE,
  KEY `extkey` (`extkey`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_form_field`
--

DROP TABLE IF EXISTS `wo_minapper_form_field`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_form_field` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `parent` bigint DEFAULT NULL,
  `fieldkey` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fieldvalue` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`ID`),
  KEY `fieldkey` (`fieldkey`) USING BTREE,
  KEY `fieldvalue` (`fieldvalue`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_integral`
--

DROP TABLE IF EXISTS `wo_minapper_integral`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_integral` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint unsigned DEFAULT NULL,
  `integraltype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `flag` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `itemid` bigint unsigned DEFAULT NULL,
  `itemname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `integral` bigint unsigned NOT NULL DEFAULT '0',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '0',
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `amount` bigint DEFAULT '0',
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`ID`),
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_logs`
--

DROP TABLE IF EXISTS `wo_minapper_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_logs` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint unsigned DEFAULT NULL,
  `openid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `logtype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `objectid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `field1` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `field2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`ID`),
  KEY `userid` (`userid`) USING BTREE,
  KEY `logtype` (`logtype`) USING BTREE,
  KEY `objectid` (`objectid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_message`
--

DROP TABLE IF EXISTS `wo_minapper_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_message` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `fromid` bigint unsigned DEFAULT NULL,
  `userid` bigint unsigned DEFAULT NULL,
  `objectid` bigint unsigned DEFAULT NULL,
  `messagetype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `messagestatus` bigint unsigned NOT NULL DEFAULT '0',
  `messagevalue` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`ID`),
  KEY `objectid` (`objectid`) USING BTREE,
  KEY `fromid` (`fromid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE,
  KEY `messagetype` (`messagetype`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_minishop_cate`
--

DROP TABLE IF EXISTS `wo_minapper_minishop_cate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_minishop_cate` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `shopcat_id` bigint unsigned DEFAULT NULL,
  `shopcat_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `f_shopcat_id` bigint unsigned DEFAULT NULL,
  `cat_level` bigint unsigned DEFAULT NULL,
  `weights` bigint unsigned DEFAULT NULL,
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `shopcat_id` (`shopcat_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_minishop_product`
--

DROP TABLE IF EXISTS `wo_minapper_minishop_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_minishop_product` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` bigint unsigned DEFAULT NULL,
  `out_product_id` bigint unsigned DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sub_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `head_img` longtext COLLATE utf8mb4_unicode_ci,
  `desc_info` longtext COLLATE utf8mb4_unicode_ci,
  `brand_id` bigint unsigned DEFAULT NULL,
  `status` bigint unsigned DEFAULT NULL,
  `edit_status` bigint unsigned DEFAULT NULL,
  `min_price` bigint unsigned DEFAULT NULL,
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `spu_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `product_type` bigint unsigned DEFAULT NULL,
  `cats` longtext COLLATE utf8mb4_unicode_ci,
  `attrs` longtext COLLATE utf8mb4_unicode_ci,
  `model` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `express_info` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shopcat` longtext COLLATE utf8mb4_unicode_ci,
  `skus` longtext COLLATE utf8mb4_unicode_ci,
  `couponcode_info` longtext COLLATE utf8mb4_unicode_ci,
  `source` bigint unsigned DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expressInfo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_minishop_productcat`
--

DROP TABLE IF EXISTS `wo_minapper_minishop_productcat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_minishop_productcat` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` bigint unsigned DEFAULT NULL,
  `shopcat_id` bigint unsigned DEFAULT NULL,
  `shopcat_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `f_shopcat_id` bigint unsigned DEFAULT NULL,
  `cat_level` bigint unsigned DEFAULT NULL,
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `shopcat_id` (`shopcat_id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_minishop_productext`
--

DROP TABLE IF EXISTS `wo_minapper_minishop_productext`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_minishop_productext` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` bigint unsigned DEFAULT NULL,
  `recommend` bigint unsigned DEFAULT NULL,
  `selected` bigint unsigned DEFAULT NULL,
  `istop` bigint unsigned DEFAULT NULL,
  `quantity` bigint unsigned DEFAULT NULL,
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_mpsubscribe_user`
--

DROP TABLE IF EXISTS `wo_minapper_mpsubscribe_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_mpsubscribe_user` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `openid` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'openid',
  `subscribe` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户是否订阅该公众号标识',
  `nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `headimgurl` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户头像',
  `subscribe_time` timestamp NULL DEFAULT NULL COMMENT '关注时间',
  `unionid` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'unionid',
  `remark` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tagid_list` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `subscribe_scene` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '返回用户关注的渠道来源',
  `qr_scene` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '返回用户关注的渠道来源',
  `qr_scene_str` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '返回用户关注的渠道来源',
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `openid` (`openid`) USING BTREE,
  KEY `unionid` (`unionid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_mpuser_tag`
--

DROP TABLE IF EXISTS `wo_minapper_mpuser_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_mpuser_tag` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `openid` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'openid',
  `tagid` bigint unsigned DEFAULT NULL COMMENT '用户标签id',
  `tagname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户标签名',
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `openid` (`openid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_order`
--

DROP TABLE IF EXISTS `wo_minapper_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_order` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint unsigned DEFAULT NULL,
  `extid` bigint unsigned DEFAULT NULL,
  `extname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `orderid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `noncestr` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ordertype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` bigint unsigned DEFAULT NULL,
  `totalfee` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `wxorderid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '0',
  `paydate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `payenddate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `integral` bigint DEFAULT NULL,
  `issend` bigint DEFAULT NULL,
  `deliveryname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deliveryid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `waybillid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receivename` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receivephone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receiveaddress` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `goodscode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `isreceived` bigint DEFAULT NULL,
  `transactionid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tracewaybill` longtext COLLATE utf8mb4_unicode_ci,
  `goodssnapshot` longtext COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`ID`),
  KEY `orderid` (`orderid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE,
  KEY `ordertype` (`ordertype`) USING BTREE,
  KEY `extid` (`extid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_product`
--

DROP TABLE IF EXISTS `wo_minapper_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_product` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `productid` bigint unsigned DEFAULT NULL,
  `productname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `productype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `number` bigint unsigned DEFAULT NULL,
  `payrequired` bigint unsigned DEFAULT NULL,
  `price` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `originalprice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` longtext COLLATE utf8mb4_unicode_ci,
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`ID`),
  KEY `productid` (`productid`) USING BTREE,
  KEY `productype` (`productype`) USING BTREE,
  KEY `productname` (`productname`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_shop_address`
--

DROP TABLE IF EXISTS `wo_minapper_shop_address`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_shop_address` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint unsigned DEFAULT NULL,
  `receivename` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receivephone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receiveaddress` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `latitude` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `longitude` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `isdefault` bigint unsigned DEFAULT NULL,
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_subscribe_cron`
--

DROP TABLE IF EXISTS `wo_minapper_subscribe_cron`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_subscribe_cron` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `author` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postid` bigint DEFAULT NULL,
  `posttype` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extkey` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extid` bigint DEFAULT '0',
  `flag` bigint DEFAULT '0',
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `extkey` (`extkey`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_user_session`
--

DROP TABLE IF EXISTS `wo_minapper_user_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_user_session` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint unsigned DEFAULT NULL,
  `deviceid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sessionid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sessionexpire` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `platform` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `brand` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `model` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `brandimg` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mark` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `ip` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `sessionid` (`sessionid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_wechat_shop_ext`
--

DROP TABLE IF EXISTS `wo_minapper_wechat_shop_ext`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_wechat_shop_ext` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `extid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extype` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extkey` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extvalue` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `extid` (`extid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_wechat_shop_order`
--

DROP TABLE IF EXISTS `wo_minapper_wechat_shop_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_wechat_shop_order` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `order_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` bigint unsigned DEFAULT NULL,
  `openid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `unionid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order_detail` longtext COLLATE utf8mb4_unicode_ci,
  `aftersale_detail` longtext COLLATE utf8mb4_unicode_ci,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_present` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `order_id` (`order_id`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_wechat_shop_product`
--

DROP TABLE IF EXISTS `wo_minapper_wechat_shop_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_wechat_shop_product` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `min_price` bigint unsigned DEFAULT NULL,
  `head_imgs` longtext COLLATE utf8mb4_unicode_ci,
  `title` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sub_title` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `h5url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `taglink` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `qrcode` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `product_type` bigint unsigned DEFAULT NULL,
  `product_detail` longtext COLLATE utf8mb4_unicode_ci,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_wechat_shop_user`
--

DROP TABLE IF EXISTS `wo_minapper_wechat_shop_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_wechat_shop_user` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `unionid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `openid` (`openid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_minapper_weixin_users`
--

DROP TABLE IF EXISTS `wo_minapper_weixin_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_minapper_weixin_users` (
  `openid` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userid` bigint unsigned DEFAULT NULL,
  `unionid` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` int DEFAULT NULL,
  `language` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `province` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatarurl` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sessionid` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `invitecode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sessionexpire` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `member` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '10',
  `forumslevel` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1',
  `memberbegindate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `memberenddate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `integral` bigint unsigned NOT NULL DEFAULT '0',
  `creatdate` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`openid`),
  KEY `userid` (`userid`) USING BTREE,
  KEY `sessionid` (`sessionid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_options`
--

DROP TABLE IF EXISTS `wo_options`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_options` (
  `option_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `option_name` varchar(191) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `option_value` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `autoload` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'yes',
  PRIMARY KEY (`option_id`),
  UNIQUE KEY `option_name` (`option_name`),
  KEY `autoload` (`autoload`)
) ENGINE=InnoDB AUTO_INCREMENT=2666 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_postmeta`
--

DROP TABLE IF EXISTS `wo_postmeta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_postmeta` (
  `meta_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `post_id` bigint unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`meta_id`),
  KEY `post_id` (`post_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB AUTO_INCREMENT=340 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_posts`
--

DROP TABLE IF EXISTS `wo_posts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_posts` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `post_author` bigint unsigned NOT NULL DEFAULT '0',
  `post_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_date_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_content` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_title` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_excerpt` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'publish',
  `comment_status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'open',
  `ping_status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'open',
  `post_password` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `post_name` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `to_ping` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `pinged` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_modified` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_modified_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_content_filtered` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_parent` bigint unsigned NOT NULL DEFAULT '0',
  `guid` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `menu_order` int NOT NULL DEFAULT '0',
  `post_type` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'post',
  `post_mime_type` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_count` bigint NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `post_name` (`post_name`(191)),
  KEY `type_status_date` (`post_type`,`post_status`,`post_date`,`ID`),
  KEY `post_parent` (`post_parent`),
  KEY `post_author` (`post_author`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_term_relationships`
--

DROP TABLE IF EXISTS `wo_term_relationships`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_term_relationships` (
  `object_id` bigint unsigned NOT NULL DEFAULT '0',
  `term_taxonomy_id` bigint unsigned NOT NULL DEFAULT '0',
  `term_order` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`object_id`,`term_taxonomy_id`),
  KEY `term_taxonomy_id` (`term_taxonomy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_term_taxonomy`
--

DROP TABLE IF EXISTS `wo_term_taxonomy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_term_taxonomy` (
  `term_taxonomy_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `term_id` bigint unsigned NOT NULL DEFAULT '0',
  `taxonomy` varchar(32) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `parent` bigint unsigned NOT NULL DEFAULT '0',
  `count` bigint NOT NULL DEFAULT '0',
  PRIMARY KEY (`term_taxonomy_id`),
  UNIQUE KEY `term_id_taxonomy` (`term_id`,`taxonomy`),
  KEY `taxonomy` (`taxonomy`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_termmeta`
--

DROP TABLE IF EXISTS `wo_termmeta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_termmeta` (
  `meta_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `term_id` bigint unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`meta_id`),
  KEY `term_id` (`term_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_terms`
--

DROP TABLE IF EXISTS `wo_terms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_terms` (
  `term_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `slug` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `term_group` bigint NOT NULL DEFAULT '0',
  PRIMARY KEY (`term_id`),
  KEY `slug` (`slug`(191)),
  KEY `name` (`name`(191))
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_usermeta`
--

DROP TABLE IF EXISTS `wo_usermeta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_usermeta` (
  `umeta_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`umeta_id`),
  KEY `user_id` (`user_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wo_users`
--

DROP TABLE IF EXISTS `wo_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wo_users` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_login` varchar(60) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_pass` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_nicename` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_email` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_url` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_registered` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `user_activation_key` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_status` int NOT NULL DEFAULT '0',
  `display_name` varchar(250) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`ID`),
  KEY `user_login_key` (`user_login`),
  KEY `user_nicename` (`user_nicename`),
  KEY `user_email` (`user_email`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-04 16:20:26
