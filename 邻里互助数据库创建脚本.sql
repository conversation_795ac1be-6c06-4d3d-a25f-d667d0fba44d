-- 邻里互助模块数据库创建脚本
-- 请将 'wo_' 替换为您实际的WordPress表前缀

-- 1. 创建互助需求表
CREATE TABLE IF NOT EXISTS `wo_minapper_help_requests` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `topic_id` bigint(20) UNSIGNED NOT NULL COMMENT '关联bbPress topic ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '发布用户ID',
  `urgency` enum('urgent','normal','low') DEFAULT 'normal' COMMENT '紧急程度',
  `help_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '互助类型',
  `points_reward` int DEFAULT 0 COMMENT '积分奖励',
  `location_info` text COLLATE utf8mb4_unicode_ci COMMENT '位置信息JSON',
  `contact_info` varchar(255) COLLATE utf8mb4_unicode_ci COMMENT '联系方式',
  `status` enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `topic_id` (`topic_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `urgency` (`urgency`),
  KEY `help_type` (`help_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 创建互助响应表
CREATE TABLE IF NOT EXISTS `wo_minapper_help_responses` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `request_id` bigint(20) UNSIGNED NOT NULL COMMENT '互助需求ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '响应用户ID',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '响应内容',
  `estimated_time` varchar(100) COLLATE utf8mb4_unicode_ci COMMENT '预计完成时间',
  `status` enum('pending','selected','completed','rejected') DEFAULT 'pending',
  `selected_at` timestamp NULL COMMENT '被选中时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `request_id` (`request_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 创建互助评价表
CREATE TABLE IF NOT EXISTS `wo_minapper_help_ratings` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `request_id` bigint(20) UNSIGNED NOT NULL COMMENT '互助需求ID',
  `rater_id` bigint(20) UNSIGNED NOT NULL COMMENT '评价者ID',
  `rated_id` bigint(20) UNSIGNED NOT NULL COMMENT '被评价者ID',
  `rating` tinyint(1) NOT NULL COMMENT '评分1-5',
  `comment` text COLLATE utf8mb4_unicode_ci COMMENT '评价内容',
  `type` enum('requester_to_helper','helper_to_requester') NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `request_id` (`request_id`),
  KEY `rater_id` (`rater_id`),
  KEY `rated_id` (`rated_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 添加互助相关的自定义字段（如果 wo_minapper_custom_fields 表存在）
INSERT IGNORE INTO `wo_minapper_custom_fields` (`posttypes`, `category`, `fieldname`, `fieldkey`, `datatype`, `mark`, `orderby`) VALUES 
('topic', 'help', '紧急程度', 'help_urgency', 'select', '紧急,一般,不急', 1),
('topic', 'help', '互助类型', 'help_type', 'select', '家居维修,照顾宠物,搬运物品,技能咨询,其他', 2),
('topic', 'help', '积分奖励', 'help_points', 'number', NULL, 3),
('topic', 'help', '位置信息', 'help_location', 'text', NULL, 4),
('topic', 'help', '联系方式', 'help_contact', 'text', NULL, 5);

-- 5. 创建邻里互助论坛（如果使用bbPress）
-- 注意：这个需要根据实际情况调整，可能需要在WordPress后台手动创建
INSERT IGNORE INTO `wo_posts` (`post_author`, `post_date`, `post_date_gmt`, `post_content`, `post_title`, `post_excerpt`, `post_status`, `comment_status`, `ping_status`, `post_password`, `post_name`, `to_ping`, `pinged`, `post_modified`, `post_modified_gmt`, `post_content_filtered`, `post_parent`, `guid`, `menu_order`, `post_type`, `post_mime_type`, `comment_count`) VALUES
(1, NOW(), UTC_TIMESTAMP(), '邻里互助论坛，邻里之间相互帮助的温暖社区', '邻里互助', '', 'publish', 'closed', 'closed', '', 'help-forum', '', '', NOW(), UTC_TIMESTAMP(), '', 0, '', 0, 'forum', '', 0);

-- 验证表是否创建成功的查询
-- SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME LIKE '%minapper_help%';

-- 验证自定义字段是否添加成功的查询
-- SELECT * FROM wo_minapper_custom_fields WHERE category = 'help';

-- 使用说明：
-- 1. 请将所有的 'wo_' 替换为您实际的WordPress数据库表前缀
-- 2. 在phpMyAdmin或其他数据库管理工具中执行此脚本
-- 3. 执行完成后，检查表是否创建成功
-- 4. 如果遇到权限问题，请确保数据库用户有CREATE和INSERT权限
