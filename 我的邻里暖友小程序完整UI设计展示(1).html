<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>我的邻里暖友小程序 - 完整UI设计展示</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
  <style>
    /* 全局样式变量 - 清雅温暖风格（增强版） */
    :root {
      /* 主色调 - 温暖蓝紫 */
      --primary-color: #6168D5;
      --primary-light: #8d8fe0;
      --primary-dark: #4e55b3;
      --secondary-color: #8D76E8;
      
      /* 功能模块专属色彩系统 */
      --home-gradient: linear-gradient(135deg, #6168D5, #8D76E8);
      --help-gradient: linear-gradient(135deg, #FF6B9D, #FF8E9B);
      --trade-gradient: linear-gradient(135deg, #52C41A, #73D13D);
      --activity-gradient: linear-gradient(135deg, #FFAA6C, #FF7A45);
      --service-gradient: linear-gradient(135deg, #722ED1, #9254DE);
      --travel-gradient: linear-gradient(135deg, #1890FF, #40A9FF);
      --points-gradient: linear-gradient(135deg, #F6D365, #FDA085);
      --profile-gradient: linear-gradient(135deg, #36CFC9, #13C2C2);
      
      /* 暖色调 */
      --warm-orange: #FFAA6C;
      --warm-peach: #FFD4B8;
      --warm-cream: #FFF8F3;
      --warm-pink: #FF6B9D;
      --warm-coral: #FF8E9B;
      
      /* 功能色彩 */
      --success-color: #52C41A;
      --warning-color: #FAAD14;
      --error-color: #FF4D4F;
      --info-color: #1890FF;
      
      /* 中性色 */
      --text-primary: #2C2C2C;
      --text-secondary: #666666;
      --text-tertiary: #999999;
      --text-light: #CCCCCC;
      
      /* 背景色 */
      --bg-primary: #FFFFFF;
      --bg-secondary: #F8F9FD;
      --bg-tertiary: #F0F2F5;
      --bg-warm: #FFF8F3;
      
      /* 边框和阴影 */
      --border-color: #E8E8E8;
      --border-light: #F0F0F0;
      --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
      --shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
      
      /* 圆角 */
      --radius-sm: 6px;
      --radius: 12px;
      --radius-lg: 16px;
      --radius-xl: 24px;
      
      /* 间距 */
      --space-xs: 4px;
      --space-sm: 8px;
      --space: 16px;
      --space-lg: 24px;
      --space-xl: 32px;
    }
    
    * {
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
      background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-warm) 100%);
      color: var(--text-primary);
      line-height: 1.6;
      margin: 0;
      padding: 0;
    }
    
    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: var(--space-lg);
    }
    
    /* 页面标题 */
    .page-header {
      text-align: center;
      padding: var(--space-xl) 0;
      background: var(--home-gradient);
      color: white;
      margin-bottom: var(--space-xl);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
    }
    
    .page-header h1 {
      font-size: 2.5rem;
      font-weight: 600;
      margin-bottom: var(--space);
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .page-header p {
      font-size: 1.1rem;
      opacity: 0.9;
      margin: 0;
    }
    
    /* 模块区域 */
    .module-section {
      margin-bottom: var(--space-xl);
      background: var(--bg-primary);
      border-radius: var(--radius-lg);
      padding: var(--space-xl);
      box-shadow: var(--shadow);
      border: 1px solid var(--border-light);
    }
    
    .module-title {
      font-size: 1.8rem;
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: var(--space-lg);
      position: relative;
      padding-left: var(--space-lg);
    }
    
    /* 不同模块的标题色彩 */
    .module-title.home::before { background: var(--home-gradient); }
    .module-title.help::before { background: var(--help-gradient); }
    .module-title.trade::before { background: var(--trade-gradient); }
    .module-title.activity::before { background: var(--activity-gradient); }
    .module-title.service::before { background: var(--service-gradient); }
    .module-title.travel::before { background: var(--travel-gradient); }
    .module-title.points::before { background: var(--points-gradient); }
    .module-title.profile::before { background: var(--profile-gradient); }
    
    .module-title::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 24px;
      background: var(--home-gradient);
      border-radius: 2px;
    }
    
    /* 手机模拟器 */
    .phone-mockup {
      width: 375px;
      height: 812px;
      background: #000;
      border-radius: 40px;
      padding: 8px;
      margin: 0 auto var(--space-lg);
      position: relative;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    }
    
    .phone-screen {
      width: 100%;
      height: 100%;
      background: var(--bg-secondary);
      border-radius: 32px;
      overflow: hidden;
      position: relative;
      display: flex;
      flex-direction: column;
    }
    
    /* 状态栏 */
    .status-bar {
      height: 44px;
      background: var(--bg-primary);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 var(--space-lg);
      font-size: 14px;
      font-weight: 600;
      flex-shrink: 0;
    }
    
    .status-left {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .status-right {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    /* 导航栏 */
    .nav-bar {
      height: 44px;
      background: var(--bg-primary);
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid var(--border-light);
      position: relative;
      flex-shrink: 0;
    }
    
    .nav-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .nav-back {
      position: absolute;
      left: var(--space);
      font-size: 20px;
      color: var(--primary-color);
    }
    
    .nav-action {
      position: absolute;
      right: var(--space);
      font-size: 16px;
      color: var(--primary-color);
    }
    
    /* 页面内容区域 */
    .page-content {
      flex: 1;
      overflow-y: auto;
      background: var(--bg-secondary);
    }
    
    /* 底部导航 */
    .tab-bar {
      height: 83px;
      background: var(--bg-primary);
      border-top: 1px solid var(--border-light);
      display: flex;
      padding-bottom: 34px;
      flex-shrink: 0;
    }
    
    .tab-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;
      color: var(--text-tertiary);
      font-size: 12px;
      transition: all 0.3s ease;
    }
    
    .tab-item.active {
      color: var(--primary-color);
    }
    
    .tab-icon {
      font-size: 24px;
    }
    
    /* 首页样式 */
    .home-banner {
      height: 200px;
      background: var(--home-gradient);
      margin: var(--space);
      border-radius: var(--radius-lg);
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
    
    .banner-content {
      text-align: center;
      z-index: 2;
    }
    
    .banner-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: var(--space-sm);
    }
    
    .banner-subtitle {
      font-size: 14px;
      opacity: 0.8;
    }
    
    .banner-decoration {
      position: absolute;
      top: -50px;
      right: -50px;
      width: 150px;
      height: 150px;
      background: rgba(255,255,255,0.1);
      border-radius: 50%;
    }
    
    /* 服务网格 */
    .service-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--space);
      padding: var(--space);
      background: var(--bg-primary);
      margin: 0 var(--space) var(--space);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-sm);
    }
    
    .service-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-sm);
      padding: var(--space);
      text-decoration: none;
      color: var(--text-primary);
      transition: all 0.3s ease;
    }
    
    .service-item:hover {
      transform: translateY(-2px);
      color: var(--primary-color);
    }
    
    .service-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      margin-bottom: var(--space-xs);
    }
    
    .service-icon.help { background: var(--help-gradient); }
    .service-icon.trade { background: var(--trade-gradient); }
    .service-icon.activity { background: var(--activity-gradient); }
    .service-icon.service { background: var(--service-gradient); }
    .service-icon.travel { background: var(--travel-gradient); }
    .service-icon.points { background: var(--points-gradient); }
    .service-icon.profile { background: var(--profile-gradient); }
    .service-icon.more { background: linear-gradient(135deg, #595959, #8c8c8c); }
    
    .service-name {
      font-size: 12px;
      text-align: center;
      line-height: 1.2;
    }
    
    /* 公告栏 */
    .notice-bar {
      background: var(--bg-primary);
      margin: 0 var(--space) var(--space);
      border-radius: var(--radius);
      padding: var(--space);
      display: flex;
      align-items: center;
      gap: var(--space);
      box-shadow: var(--shadow-sm);
    }
    
    .notice-icon {
      color: var(--warm-orange);
      font-size: 18px;
    }
    
    .notice-text {
      flex: 1;
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    /* 卡片列表 */
    .card-list {
      padding: 0 var(--space);
    }
    
    .card-item {
      background: var(--bg-primary);
      border-radius: var(--radius-lg);
      margin-bottom: var(--space);
      overflow: hidden;
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-light);
      transition: all 0.3s ease;
      position: relative;
    }
    
    .card-item:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow);
    }
    
    .card-header {
      padding: var(--space);
      display: flex;
      align-items: center;
      gap: var(--space);
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--home-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
    }
    
    .card-user-info {
      flex: 1;
    }
    
    .user-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 2px;
    }
    
    .user-location {
      font-size: 12px;
      color: var(--text-tertiary);
    }
    
    .card-status {
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .status-open {
      background: rgba(255, 170, 108, 0.1);
      color: var(--warm-orange);
    }
    
    .status-closed {
      background: rgba(82, 196, 26, 0.1);
      color: var(--success-color);
    }
    
    .status-urgent {
      background: rgba(255, 77, 79, 0.1);
      color: var(--error-color);
    }
    
    .card-content {
      padding: 0 var(--space) var(--space);
    }
    
    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: var(--space-sm);
      line-height: 1.4;
    }
    
    .card-description {
      font-size: 14px;
      color: var(--text-secondary);
      line-height: 1.5;
      margin-bottom: var(--space);
    }
    
    .card-meta {
      display: flex;
      align-items: center;
      gap: var(--space);
      font-size: 12px;
      color: var(--text-tertiary);
    }
    
    .meta-item {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    /* 商品卡片特殊样式 */
    .product-card {
      display: flex;
      gap: var(--space);
    }
    
    .product-image {
      width: 100px;
      height: 100px;
      border-radius: var(--radius);
      background: var(--bg-tertiary);
      flex-shrink: 0;
      object-fit: cover;
    }
    
    .product-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    
    .product-price {
      font-size: 18px;
      font-weight: 600;
      color: var(--error-color);
      margin-bottom: var(--space-xs);
    }
    
    .product-condition {
      display: inline-block;
      padding: 2px 8px;
      background: rgba(82, 196, 26, 0.1);
      color: var(--success-color);
      border-radius: 4px;
      font-size: 12px;
      margin-bottom: var(--space-sm);
    }
    
    /* 筛选栏 */
    .filter-bar {
      background: var(--bg-primary);
      padding: var(--space);
      margin-bottom: var(--space);
      border-bottom: 1px solid var(--border-light);
    }
    
    .filter-scroll {
      display: flex;
      gap: var(--space);
      overflow-x: auto;
      padding-bottom: var(--space-xs);
    }
    
    .filter-item {
      padding: 8px 16px;
      border-radius: 20px;
      background: var(--bg-secondary);
      color: var(--text-secondary);
      font-size: 14px;
      white-space: nowrap;
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
    }
    
    .filter-item.active {
      background: var(--home-gradient);
      color: white;
      border-color: var(--primary-color);
    }
    
    /* 浮动按钮 */
    .fab {
      position: fixed;
      bottom: 100px;
      right: var(--space);
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: var(--activity-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: var(--shadow-lg);
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      z-index: 1000;
    }
    
    .fab:hover {
      transform: scale(1.1);
    }
    
    /* 在线客服按钮 */
    .customer-service {
      position: fixed;
      bottom: 170px;
      right: var(--space);
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: var(--service-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: var(--shadow-lg);
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      z-index: 1000;
    }
    
    .customer-service:hover {
      transform: scale(1.1);
    }
    
    /* 消息提示徽章 */
    .message-badge {
      position: absolute;
      top: -4px;
      right: -4px;
      background: var(--error-color);
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
    }
    
    /* 响应式设计 */
    @media (max-width: 1200px) {
      .container {
        padding: var(--space);
      }
      
      .phone-mockup {
        width: 320px;
        height: 693px;
      }
    }
    
    @media (max-width: 768px) {
      .page-header h1 {
        font-size: 2rem;
      }
      
      .module-section {
        padding: var(--space);
      }
      
      .phone-mockup {
        width: 280px;
        height: 607px;
      }
    }
    
    /* 模块网格布局 */
    .modules-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--space-xl);
      margin-top: var(--space-xl);
    }
    
    @media (max-width: 768px) {
      .modules-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
      }
    }
    
    /* 特殊样式类 */
    .gradient-text {
      background: var(--home-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .pulse-animation {
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
      100% {
        transform: scale(1);
      }
    }
  </style>
</head>
<body>
  <div class="page-header">
    <h1>我的邻里暖友小程序</h1>
    <p>完整UI设计展示 - 清雅温暖风格（增强版）</p>
  </div>

  <div class="container">
    <!-- 首页模块 -->
    <div class="module-section">
      <h2 class="module-title home">首页 - 温暖的邻里门户</h2>
      <div class="modules-grid">
        <div class="phone-mockup">
          <div class="phone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
              <div class="status-left">
                <span>9:41</span>
              </div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
              <div class="nav-title">我的邻里暖友</div>
              <div class="nav-action">
                <i class="ri-search-line"></i>
              </div>
            </div>
            
            <!-- 页面内容 -->
            <div class="page-content">
              <!-- 轮播横幅 -->
              <div class="home-banner">
                <div class="banner-content">
                  <div class="banner-title">沃土滋润，暖友共生</div>
                  <div class="banner-subtitle">欢迎来到温暖的邻里社区</div>
                </div>
                <div class="banner-decoration"></div>
              </div>
              
              <!-- 公告栏 -->
              <div class="notice-bar">
                <i class="ri-volume-up-line notice-icon"></i>
                <div class="notice-text">欢迎参加本周六的邻里分享日活动，共同交流！</div>
                <i class="ri-arrow-right-s-line"></i>
              </div>
              
              <!-- 服务网格 -->
              <div class="service-grid">
                <a href="#" class="service-item">
                  <div class="service-icon help">
                    <i class="ri-heart-line"></i>
                  </div>
                  <div class="service-name">邻里互助</div>
                </a>
                <a href="#" class="service-item">
                  <div class="service-icon trade">
                    <i class="ri-exchange-line"></i>
                  </div>
                  <div class="service-name">闲置交易</div>
                </a>
                <a href="#" class="service-item">
                  <div class="service-icon activity">
                    <i class="ri-calendar-event-line"></i>
                  </div>
                  <div class="service-name">社区活动</div>
                </a>
                <a href="#" class="service-item">
                  <div class="service-icon service">
                    <i class="ri-service-line"></i>
                  </div>
                  <div class="service-name">生活服务</div>
                </a>
                <a href="#" class="service-item">
                  <div class="service-icon travel">
                    <i class="ri-car-line"></i>
                  </div>
                  <div class="service-name">邻里出行</div>
                </a>
                <a href="#" class="service-item">
                  <div class="service-icon trade">
                    <i class="ri-shopping-cart-line"></i>
                  </div>
                  <div class="service-name">社区团购</div>
                </a>
                <a href="#" class="service-item">
                  <div class="service-icon points">
                    <i class="ri-gift-line"></i>
                  </div>
                  <div class="service-name">积分商城</div>
                </a>
                <a href="#" class="service-item">
                  <div class="service-icon more">
                    <i class="ri-more-line"></i>
                  </div>
                  <div class="service-name">更多服务</div>
                </a>
              </div>
              
              <!-- 最新动态 -->
              <div class="card-list">
                <div class="card-item">
                  <div class="card-header">
                    <div class="user-avatar" style="background: var(--help-gradient);">张</div>
                    <div class="card-user-info">
                      <div class="user-name">暖友小张</div>
                      <div class="user-location">欧美金融城 A区</div>
                    </div>
                    <div class="card-status status-open">求助中</div>
                  </div>
                  <div class="card-content">
                    <div class="card-title">寻找附近水电维修工</div>
                    <div class="card-description">家里水管漏水，需要有经验的水电维修师傅来维修，情况比较紧急。</div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-time-line"></i>
                        <span>2小时前</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-map-pin-line"></i>
                        <span>0.5km</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-eye-line"></i>
                        <span>12</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div class="card-header">
                    <div class="user-avatar" style="background: var(--activity-gradient);">李</div>
                    <div class="card-user-info">
                      <div class="user-name">邻居小李</div>
                      <div class="user-location">欧美金融城 B区</div>
                    </div>
                    <div class="card-status status-closed">已解决</div>
                  </div>
                  <div class="card-content">
                    <div class="card-title">周末邻里健身挑战赛</div>
                    <div class="card-description">一起运动，增进邻里感情，还有丰厚奖品等你来拿！</div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-calendar-line"></i>
                        <span>本周六 09:00</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-group-line"></i>
                        <span>12/20人</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 底部导航 -->
            <div class="tab-bar">
              <div class="tab-item active">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 邻里互助模块 -->
    <div class="module-section">
      <h2 class="module-title help">邻里互助 - 温暖相伴</h2>
      <div class="modules-grid">
        <!-- 互助列表页 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">邻里互助</div>
              <div class="nav-action"><i class="ri-more-line"></i></div>
            </div>
            
            <div class="page-content">
              <!-- 互助横幅 -->
              <div style="height: 120px; background: var(--help-gradient); margin: var(--space); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; position: relative; overflow: hidden;">
                <div style="text-align: center; z-index: 2;">
                  <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">💝 邻里互助，温暖相伴</div>
                  <div style="font-size: 14px; opacity: 0.8;">帮助他人，温暖自己</div>
                </div>
                <div style="position: absolute; top: -30px; right: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              </div>
              
              <!-- 筛选栏 -->
              <div class="filter-bar">
                <div class="filter-scroll">
                  <div class="filter-item active" style="background: var(--help-gradient); color: white; border-color: var(--warm-pink);">全部</div>
                  <div class="filter-item">家居维修</div>
                  <div class="filter-item">照顾宠物</div>
                  <div class="filter-item">搬运物品</div>
                  <div class="filter-item">技能咨询</div>
                  <div class="filter-item">其他</div>
                </div>
              </div>
              
              <!-- 互助列表 -->
              <div class="card-list">
                <div class="card-item">
                  <div class="card-header">
                    <div class="user-avatar" style="background: var(--help-gradient);">王</div>
                    <div class="card-user-info">
                      <div class="user-name">暖友小王</div>
                      <div class="user-location">A区3栋 • 水电工程师</div>
                    </div>
                    <div class="card-status status-urgent">紧急</div>
                  </div>
                  <div class="card-content">
                    <div class="card-title">🔥 急需水电维修师傅</div>
                    <div class="card-description">家里突然停电，怀疑是电路问题，需要有经验的电工师傅紧急处理。</div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-time-line"></i>
                        <span>30分钟前</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-map-pin-line"></i>
                        <span>0.2km</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-coin-line"></i>
                        <span>200积分</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div class="card-header">
                    <div class="user-avatar" style="background: var(--help-gradient);">陈</div>
                    <div class="card-user-info">
                      <div class="user-name">邻居小陈</div>
                      <div class="user-location">B区1栋 • 宠物达人</div>
                    </div>
                    <div class="card-status status-open">求助中</div>
                  </div>
                  <div class="card-content">
                    <div class="card-title">🐱 暑假期间照顾小猫</div>
                    <div class="card-description">下周要出差五天，需要找人帮忙照顾家里的小猫，提供食物和猫砂。</div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-time-line"></i>
                        <span>2小时前</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-map-pin-line"></i>
                        <span>0.8km</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-coin-line"></i>
                        <span>150积分</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div class="card-header">
                    <div class="user-avatar" style="background: var(--help-gradient);">刘</div>
                    <div class="card-user-info">
                      <div class="user-name">暖友老刘</div>
                      <div class="user-location">C区2栋 • 搬运达人</div>
                    </div>
                    <div class="card-status status-closed">已解决</div>
                  </div>
                  <div class="card-content">
                    <div class="card-title">📦 帮忙搬运一张床垫</div>
                    <div class="card-description">刚买了新床垫，需要帮忙从一楼搬到三楼，约10分钟的工作。</div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-time-line"></i>
                        <span>昨天 16:45</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-map-pin-line"></i>
                        <span>1.2km</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-coin-line"></i>
                        <span>100积分</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item active">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 发布互助页面 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">发布互助需求</div>
              <div class="nav-action" style="background: var(--help-gradient); color: white; padding: 4px 12px; border-radius: 12px;">发布</div>
            </div>
            
            <div class="page-content" style="padding: var(--space);">
              <!-- 紧急程度选择 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">紧急程度</div>
                <div style="display: flex; gap: var(--space);">
                  <div style="flex: 1; padding: var(--space); border-radius: var(--radius); background: rgba(255, 77, 79, 0.1); border: 2px solid var(--error-color); text-align: center;">
                    <i class="ri-fire-line" style="font-size: 24px; color: var(--error-color); margin-bottom: var(--space-xs);"></i>
                    <div style="font-size: 14px; color: var(--error-color); font-weight: 500;">紧急</div>
                  </div>
                  <div style="flex: 1; padding: var(--space); border-radius: var(--radius); background: var(--bg-secondary); border: 1px solid var(--border-color); text-align: center;">
                    <i class="ri-time-line" style="font-size: 24px; color: var(--text-tertiary); margin-bottom: var(--space-xs);"></i>
                    <div style="font-size: 14px; color: var(--text-tertiary);">一般</div>
                  </div>
                  <div style="flex: 1; padding: var(--space); border-radius: var(--radius); background: var(--bg-secondary); border: 1px solid var(--border-color); text-align: center;">
                    <i class="ri-leaf-line" style="font-size: 24px; color: var(--text-tertiary); margin-bottom: var(--space-xs);"></i>
                    <div style="font-size: 14px; color: var(--text-tertiary);">不急</div>
                  </div>
                </div>
              </div>
              
              <!-- 标题输入 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">标题</div>
                <input type="text" placeholder="简要描述您的需求..." style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
              </div>
              
              <!-- 详细描述 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">详细描述</div>
                <textarea placeholder="详细说明您的需求，包括时间、地点等信息..." style="width: 100%; height: 80px; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); resize: none;"></textarea>
              </div>
              
              <!-- 类型选择 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">互助类型</div>
                <div style="display: flex; align-items: center; justify-content: space-between; padding: var(--space); background: var(--bg-secondary); border-radius: var(--radius); border: 1px solid var(--border-color);">
                  <span style="color: var(--text-secondary);">请选择类型</span>
                  <i class="ri-arrow-down-s-line" style="color: var(--text-tertiary);"></i>
                </div>
              </div>
              
              <!-- 位置信息 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">位置信息</div>
                <div style="display: flex; align-items: center; gap: var(--space);">
                  <input type="text" placeholder="详细地址" style="flex: 1; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
                  <button style="padding: var(--space); background: var(--help-gradient); color: white; border: none; border-radius: var(--radius); font-size: 14px;">
                    <i class="ri-map-pin-line"></i>
                  </button>
                </div>
              </div>
              
              <!-- 积分奖励 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">积分奖励</div>
                <input type="number" placeholder="愿意支付的积分数量" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
              </div>
              
              <!-- 提交按钮 -->
              <button style="width: 100%; padding: var(--space-lg); background: var(--help-gradient); color: white; border: none; border-radius: var(--radius-lg); font-size: 16px; font-weight: 500; margin-top: var(--space);">立即发布互助需求</button>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item active">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 邻里互助详情页 -->
    <div class="module-section">
      <h2 class="module-title help">邻里互助详情页 - 完整交互流程</h2>
      <div class="modules-grid">
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">互助详情</div>
              <div class="nav-action"><i class="ri-share-line"></i></div>
            </div>
            
            <div class="page-content" style="padding-bottom: 120px;">
              <!-- 需求信息卡片 -->
              <div style="background: var(--bg-primary); margin: var(--space); border-radius: var(--radius-lg); padding: var(--space-lg); box-shadow: var(--shadow);">
                <div style="display: flex; align-items: center; margin-bottom: var(--space);">
                  <div style="background: var(--help-gradient); padding: 8px; border-radius: 50%; margin-right: var(--space); color: white;">
                    <i class="ri-fire-line" style="font-size: 20px;"></i>
                  </div>
                  <div style="background: rgba(255, 77, 79, 0.1); color: var(--error-color); padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: 500;">紧急</div>
                </div>
                
                <div style="font-size: 20px; font-weight: 600; color: var(--text-primary); margin-bottom: var(--space); line-height: 1.4;">🔥 急需水电维修师傅</div>
                
                <div style="color: var(--text-secondary); font-size: 15px; line-height: 1.6; margin-bottom: var(--space-lg);">
                  家里突然停电，怀疑是电路问题，需要有经验的电工师傅紧急处理。具体情况：客厅和卧室突然断电，其他房间正常，可能是某个开关或线路出现问题。
                </div>
                
                <!-- 需求信息 -->
                <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space); margin-bottom: var(--space);">
                  <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--space);">
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">位置距离</div>
                      <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">A区3栋 • 0.2km</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">积分奖励</div>
                      <div style="font-size: 14px; color: var(--warm-orange); font-weight: 600;">200积分</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">发布时间</div>
                      <div style="font-size: 14px; color: var(--text-primary);">30分钟前</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">互助类型</div>
                      <div style="font-size: 14px; color: var(--text-primary);">家居维修</div>
                    </div>
                  </div>
                </div>
                
                <!-- 发布者信息 -->
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div style="display: flex; align-items: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--help-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; margin-right: var(--space);">王</div>
                    <div>
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">暖友小王</div>
                      <div style="font-size: 12px; color: var(--text-secondary);">⭐ 4.8分 • 发布过12次需求</div>
                    </div>
                  </div>
                  <button style="background: var(--profile-gradient); color: white; border: none; padding: 8px 16px; border-radius: 20px; font-size: 12px;">
                    <i class="ri-message-line" style="margin-right: 4px;"></i>私信
                  </button>
                </div>
              </div>
              
              <!-- 响应列表 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space);">
                  <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); display: flex; align-items: center;">
                    <div style="width: 4px; height: 18px; background: var(--help-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                    响应列表 (3人)
                  </div>
                </div>
                
                <!-- 响应者1 -->
                <div style="border-bottom: 1px solid var(--border-light); padding: var(--space) 0;">
                  <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-sm);">
                    <div style="display: flex; align-items: center;">
                      <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; margin-right: var(--space-sm);">李</div>
                      <div>
                        <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">电工小李</div>
                        <div style="font-size: 12px; color: var(--text-secondary);">⭐ 4.9分 • 专业电工 • 5年经验</div>
                      </div>
                    </div>
                    <div style="background: rgba(82, 196, 26, 0.1); color: var(--success-color); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">已选择</div>
                  </div>
                  <div style="font-size: 13px; color: var(--text-secondary); margin-bottom: var(--space-sm);">
                    "我是专业电工，有5年维修经验，可以立即过来处理。我住在B区，10分钟就能到达。"
                  </div>
                  <div style="font-size: 12px; color: var(--text-tertiary);">2分钟前响应</div>
                </div>
                
                <!-- 响应者2 -->
                <div style="border-bottom: 1px solid var(--border-light); padding: var(--space) 0;">
                  <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-sm);">
                    <div style="display: flex; align-items: center;">
                      <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--info-color); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; margin-right: var(--space-sm);">张</div>
                      <div>
                        <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">维修师张叔</div>
                        <div style="font-size: 12px; color: var(--text-secondary);">⭐ 4.7分 • 家电维修 • 3年经验</div>
                      </div>
                    </div>
                    <button style="background: var(--help-gradient); color: white; border: none; padding: 4px 12px; border-radius: 12px; font-size: 12px;">选择</button>
                  </div>
                  <div style="font-size: 13px; color: var(--text-secondary); margin-bottom: var(--space-sm);">
                    "我有家电维修经验，也懂电路维修，可以帮您检查一下。"
                  </div>
                  <div style="font-size: 12px; color: var(--text-tertiary);">5分钟前响应</div>
                </div>
              </div>
              
              <!-- 进度跟踪 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--help-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  进度跟踪
                </div>
                
                <div style="position: relative;">
                  <!-- 进度步骤 -->
                  <div style="display: flex; align-items: flex-start; margin-bottom: var(--space);">
                    <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--help-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; flex-shrink: 0; margin-right: var(--space);">
                      <i class="ri-check-line"></i>
                    </div>
                    <div style="flex: 1; padding-top: 4px;">
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">需求已发布</div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">30分钟前</div>
                    </div>
                  </div>
                  
                  <div style="display: flex; align-items: flex-start; margin-bottom: var(--space);">
                    <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--help-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; flex-shrink: 0; margin-right: var(--space);">
                      <i class="ri-check-line"></i>
                    </div>
                    <div style="flex: 1; padding-top: 4px;">
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">帮助者已选择</div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">2分钟前 • 电工小李</div>
                    </div>
                  </div>
                  
                  <div style="display: flex; align-items: flex-start; margin-bottom: var(--space);">
                    <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--bg-secondary); border: 2px solid var(--help-gradient); display: flex; align-items: center; justify-content: center; color: var(--help-gradient); font-size: 14px; flex-shrink: 0; margin-right: var(--space);">
                      3
                    </div>
                    <div style="flex: 1; padding-top: 4px;">
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-secondary);">服务进行中</div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">等待服务完成</div>
                    </div>
                  </div>
                  
                  <div style="display: flex; align-items: flex-start;">
                    <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--bg-secondary); border: 2px solid var(--border-color); display: flex; align-items: center; justify-content: center; color: var(--text-tertiary); font-size: 14px; flex-shrink: 0; margin-right: var(--space);">
                      4
                    </div>
                    <div style="flex: 1; padding-top: 4px;">
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-tertiary);">完成确认</div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">服务完成后确认</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 底部操作栏 -->
            <div style="position: fixed; bottom: 83px; left: 0; right: 0; background: var(--bg-primary); border-top: 1px solid var(--border-light); padding: var(--space); display: flex; gap: var(--space); align-items: center;">
              <button style="flex: 1; background: var(--bg-secondary); color: var(--text-secondary); border: 1px solid var(--border-color); padding: var(--space); border-radius: var(--radius); font-size: 14px;">
                <i class="ri-message-line" style="margin-right: var(--space-xs);"></i>联系帮助者
              </button>
              <button style="flex: 1; background: var(--help-gradient); color: white; border: none; padding: var(--space); border-radius: var(--radius); font-size: 14px;">
                <i class="ri-check-line" style="margin-right: var(--space-xs);"></i>确认完成
              </button>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item active">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 闲置交易模块 -->
    <div class="module-section">
      <h2 class="module-title trade">闲置交易 - 物尽其用</h2>
      <div class="modules-grid">
        <!-- 交易列表页 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">闲置交易</div>
              <div class="nav-action"><i class="ri-search-line"></i></div>
            </div>
            
            <div class="page-content">
              <!-- 交易横幅 -->
              <div style="height: 120px; background: var(--trade-gradient); margin: var(--space); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; position: relative; overflow: hidden;">
                <div style="text-align: center; z-index: 2;">
                  <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">♻️ 闲置交易，物尽其用</div>
                  <div style="font-size: 14px; opacity: 0.8;">让闲置物品找到新主人</div>
                </div>
                <div style="position: absolute; top: -30px; right: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              </div>
              
              <!-- 筛选栏 -->
              <div class="filter-bar">
                <div class="filter-scroll">
                  <div class="filter-item active" style="background: var(--trade-gradient); color: white; border-color: var(--success-color);">全部</div>
                  <div class="filter-item">数码电器</div>
                  <div class="filter-item">家具家居</div>
                  <div class="filter-item">服装配饰</div>
                  <div class="filter-item">母婴用品</div>
                  <div class="filter-item">图书文具</div>
                </div>
              </div>
              
              <!-- 商品列表 -->
              <div class="card-list">
                <div class="card-item">
                  <div class="card-content">
                    <div class="product-card">
                      <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop" class="product-image" alt="商品图片">
                      <div class="product-info">
                        <div class="card-title">Nike Air Max 270 运动鞋</div>
                        <div class="product-price">¥180.00</div>
                        <div class="product-condition">几乎全新</div>
                        <div class="card-description">9成新，很少穿，码数42，原价399</div>
                        <div class="card-meta">
                          <div class="meta-item">
                            <i class="ri-time-line"></i>
                            <span>2小时前</span>
                          </div>
                          <div class="meta-item">
                            <i class="ri-map-pin-line"></i>
                            <span>1.2km</span>
                          </div>
                          <div class="meta-item">
                            <i class="ri-eye-line"></i>
                            <span>23</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card-status status-open" style="position: absolute; top: var(--space); right: var(--space);">在售</div>
                </div>
                
                <div class="card-item">
                  <div class="card-content">
                    <div class="product-card">
                      <img src="https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=200&h=200&fit=crop" class="product-image" alt="商品图片">
                      <div class="product-info">
                        <div class="card-title">iPad Air 64GB WiFi版</div>
                        <div class="product-price">¥2,800.00</div>
                        <div class="product-condition">轻微使用痕迹</div>
                        <div class="card-description">使用1年，功能正常，有轻微使用痕迹</div>
                        <div class="card-meta">
                          <div class="meta-item">
                            <i class="ri-time-line"></i>
                            <span>1天前</span>
                          </div>
                          <div class="meta-item">
                            <i class="ri-map-pin-line"></i>
                            <span>0.6km</span>
                          </div>
                          <div class="meta-item">
                            <i class="ri-eye-line"></i>
                            <span>45</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card-status status-open" style="position: absolute; top: var(--space); right: var(--space);">在售</div>
                </div>
                
                <div class="card-item">
                  <div class="card-content">
                    <div class="product-card">
                      <img src="https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=200&h=200&fit=crop" class="product-image" alt="商品图片">
                      <div class="product-info">
                        <div class="card-title">宜家北欧风餐桌</div>
                        <div class="product-price">¥350.00</div>
                        <div class="product-condition">明显使用痕迹</div>
                        <div class="card-description">使用2年，有一些使用痕迹但结构完好</div>
                        <div class="card-meta">
                          <div class="meta-item">
                            <i class="ri-time-line"></i>
                            <span>3天前</span>
                          </div>
                          <div class="meta-item">
                            <i class="ri-map-pin-line"></i>
                            <span>2.1km</span>
                          </div>
                          <div class="meta-item">
                            <i class="ri-eye-line"></i>
                            <span>18</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card-status status-closed" style="position: absolute; top: var(--space); right: var(--space);">已售</div>
                </div>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item active">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 发布商品页面 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">发布闲置商品</div>
              <div class="nav-action" style="background: var(--trade-gradient); color: white; padding: 4px 12px; border-radius: 12px;">发布</div>
            </div>
            
            <div class="page-content" style="padding: var(--space);">
              <!-- 图片上传 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">商品图片</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: var(--space);">
                  <div style="aspect-ratio: 1; background: var(--bg-secondary); border: 2px dashed var(--border-color); border-radius: var(--radius); display: flex; align-items: center; justify-content: center; flex-direction: column; color: var(--text-tertiary);">
                    <i class="ri-camera-line" style="font-size: 24px; margin-bottom: var(--space-xs);"></i>
                    <span style="font-size: 12px;">添加图片</span>
                  </div>
                  <div style="aspect-ratio: 1; background: var(--bg-secondary); border-radius: var(--radius); position: relative;">
                    <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop" style="width: 100%; height: 100%; object-fit: cover; border-radius: var(--radius);" alt="商品图片">
                    <div style="position: absolute; top: 4px; right: 4px; width: 20px; height: 20px; background: rgba(0,0,0,0.5); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">
                      <i class="ri-close-line"></i>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- AI估价 -->
              <div style="background: var(--trade-gradient); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); color: white;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space);">
                  <div>
                    <div style="font-size: 16px; font-weight: 500; margin-bottom: 4px;">🤖 AI智能估价</div>
                    <div style="font-size: 12px; opacity: 0.8;">基于商品图片和描述智能估价</div>
                  </div>
                  <button style="padding: 8px 16px; background: rgba(255,255,255,0.2); border: none; border-radius: 20px; color: white; font-size: 14px;">
                    <i class="ri-magic-line"></i> 开始估价
                  </button>
                </div>
                <div style="background: rgba(255,255,255,0.1); border-radius: var(--radius); padding: var(--space); text-align: center;">
                  <div style="font-size: 24px; font-weight: 600; margin-bottom: 4px;">¥180 - ¥220</div>
                  <div style="font-size: 12px; opacity: 0.8;">参考价格区间</div>
                </div>
              </div>
              
              <!-- 商品信息 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">商品信息</div>
                <input type="text" placeholder="商品标题" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); margin-bottom: var(--space);">
                <textarea placeholder="商品描述，包括购买时间、使用情况等..." style="width: 100%; height: 80px; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); resize: none;"></textarea>
              </div>
              
              <!-- 价格设置 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">价格设置</div>
                <div style="display: flex; gap: var(--space); margin-bottom: var(--space);">
                  <div style="flex: 1;">
                    <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-sm);">售价</div>
                    <input type="number" placeholder="0.00" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
                  </div>
                  <div style="flex: 1;">
                    <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-sm);">原价（可选）</div>
                    <input type="number" placeholder="0.00" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
                  </div>
                </div>
                <div>
                  <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-sm);">成色</div>
                  <div style="display: flex; align-items: center; justify-content: space-between; padding: var(--space); background: var(--bg-secondary); border-radius: var(--radius); border: 1px solid var(--border-color);">
                    <span style="color: var(--text-secondary);">请选择成色</span>
                    <i class="ri-arrow-down-s-line" style="color: var(--text-tertiary);"></i>
                  </div>
                </div>
              </div>
              
              <!-- 提交按钮 -->
              <button style="width: 100%; padding: var(--space-lg); background: var(--trade-gradient); color: white; border: none; border-radius: var(--radius-lg); font-size: 16px; font-weight: 500; margin-top: var(--space);">立即发布商品</button>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item active">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 闲置交易详情页 -->
    <div class="module-section">
      <h2 class="module-title trade">闲置交易详情页 - 完整购买流程</h2>
      <div class="modules-grid">
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">商品详情</div>
              <div class="nav-action"><i class="ri-share-line"></i></div>
            </div>
            
            <div class="page-content" style="padding-bottom: 120px;">
              <!-- 商品图片轮播 -->
              <div style="height: 300px; background: #f0f0f0; margin: var(--space); border-radius: var(--radius-lg); overflow: hidden; position: relative;">
                <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=300&fit=crop" style="width: 100%; height: 100%; object-fit: cover;" alt="商品图片">
                
                <!-- 图片指示器 -->
                <div style="position: absolute; bottom: var(--space); left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.5); color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px;">
                  1/5
                </div>
                
                <!-- 收藏按钮 -->
                <div style="position: absolute; top: var(--space); right: var(--space); width: 40px; height: 40px; border-radius: 50%; background: rgba(255,255,255,0.9); display: flex; align-items: center; justify-content: center; color: var(--text-secondary);">
                  <i class="ri-heart-line" style="font-size: 18px;"></i>
                </div>
              </div>
              
              <!-- 商品基本信息 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space-lg); box-shadow: var(--shadow-sm);">
                <div style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--space);">
                  <div style="flex: 1;">
                    <div style="font-size: 20px; font-weight: 600; color: var(--text-primary); margin-bottom: var(--space-sm); line-height: 1.4;">Nike Air Max 270 运动鞋</div>
                    <div style="background: rgba(82, 196, 26, 0.1); color: var(--success-color); padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: 500; display: inline-block; margin-bottom: var(--space);">几乎全新</div>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-size: 24px; font-weight: 600; color: var(--error-color); margin-bottom: 4px;">¥180.00</div>
                    <div style="font-size: 12px; color: var(--text-tertiary); text-decoration: line-through;">原价 ¥399</div>
                  </div>
                </div>
                
                <div style="color: var(--text-secondary); font-size: 15px; line-height: 1.6; margin-bottom: var(--space-lg);">
                  9成新，很少穿，码数42。鞋子非常舒适，适合日常运动和休闲穿着。因为买多了同款式的鞋子，所以出售。鞋盒、购买发票都在，支持当面验货。
                </div>
                
                <!-- 商品信息网格 -->
                <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space);">
                  <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--space);">
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">商品品牌</div>
                      <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">Nike</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">鞋码尺寸</div>
                      <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">42码</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">购买时间</div>
                      <div style="font-size: 14px; color: var(--text-primary);">3个月前</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">使用频次</div>
                      <div style="font-size: 14px; color: var(--text-primary);">5次以内</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 卖家信息 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--trade-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  卖家信息
                </div>
                
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div style="display: flex; align-items: center;">
                    <div style="width: 50px; height: 50px; border-radius: 50%; background: var(--trade-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; margin-right: var(--space);">张</div>
                    <div>
                      <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: 4px;">邻居小张</div>
                      <div style="display: flex; align-items: center; gap: var(--space-sm); margin-bottom: 4px;">
                        <div style="font-size: 12px; color: var(--text-secondary);">⭐ 4.9分</div>
                        <div style="font-size: 12px; color: var(--text-secondary);">A区3栋</div>
                        <div style="font-size: 12px; color: var(--text-secondary);">距离0.3km</div>
                      </div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">已成功交易15次 • 好评率98%</div>
                    </div>
                  </div>
                  <button style="background: var(--profile-gradient); color: white; border: none; padding: 8px 16px; border-radius: 20px; font-size: 12px;">
                    <i class="ri-message-line" style="margin-right: 4px;"></i>私信
                  </button>
                </div>
              </div>
              
              <!-- 购买咨询记录 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--trade-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  购买咨询 (2条)
                </div>
                
                <!-- 咨询1 -->
                <div style="border-bottom: 1px solid var(--border-light); padding-bottom: var(--space); margin-bottom: var(--space);">
                  <div style="display: flex; align-items: center; margin-bottom: var(--space-sm);">
                    <div style="width: 30px; height: 30px; border-radius: 50%; background: var(--info-color); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-right: var(--space-sm);">李</div>
                    <div>
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">邻居小李</div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">10分钟前</div>
                    </div>
                  </div>
                  <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space-sm); margin-bottom: var(--space-sm);">
                    <div style="font-size: 13px; color: var(--text-primary);">请问这双鞋还能再便宜一些吗？我想要的话什么时候可以面交？</div>
                  </div>
                  <div style="background: var(--trade-gradient); color: white; border-radius: var(--radius); padding: var(--space-sm); margin-left: var(--space-lg);">
                    <div style="font-size: 13px;">价格已经很优惠了，可以小刀5块。明天下午或周末都可以面交，在小区门口。</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 底部操作栏 -->
            <div style="position: fixed; bottom: 83px; left: 0; right: 0; background: var(--bg-primary); border-top: 1px solid var(--border-light); padding: var(--space); display: flex; gap: var(--space); align-items: center;">
              <button style="background: var(--bg-secondary); color: var(--text-secondary); border: 1px solid var(--border-color); padding: var(--space); border-radius: var(--radius); font-size: 14px; min-width: 80px;">
                <i class="ri-message-line" style="margin-right: var(--space-xs);"></i>咨询
              </button>
              <button style="flex: 1; background: var(--trade-gradient); color: white; border: none; padding: var(--space); border-radius: var(--radius); font-size: 14px;">立即购买</button>
              <button style="background: var(--warning-color); color: white; border: none; padding: var(--space); border-radius: var(--radius); font-size: 14px; min-width: 80px;">议价</button>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item active">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 社区活动模块 -->
    <div class="module-section">
      <h2 class="module-title activity">社区活动 - 温暖共享</h2>
      <div class="modules-grid">
        <!-- 活动列表页 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">社区活动</div>
              <div class="nav-action"><i class="ri-calendar-line"></i></div>
            </div>
            
            <div class="page-content">
              <!-- 活动横幅 -->
              <div style="height: 120px; background: var(--activity-gradient); margin: var(--space); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; position: relative; overflow: hidden;">
                <div style="text-align: center; z-index: 2;">
                  <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">🎉 社区活动，温暖共享</div>
                  <div style="font-size: 14px; opacity: 0.8;">参与活动，结识更多暖友邻居</div>
                </div>
                <div style="position: absolute; top: -30px; right: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              </div>
              
              <!-- 筛选栏 -->
              <div class="filter-bar">
                <div class="filter-scroll">
                  <div class="filter-item active" style="background: var(--activity-gradient); color: white; border-color: var(--warm-orange);">全部</div>
                  <div class="filter-item">暖友节</div>
                  <div class="filter-item">邻里分享日</div>
                  <div class="filter-item">杭城文化夜</div>
                  <div class="filter-item">健身挑战</div>
                  <div class="filter-item">亲子活动</div>
                </div>
              </div>
              
              <!-- 活动列表 -->
              <div class="card-list">
                <div class="card-item">
                  <div style="position: relative;">
                    <img src="https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=400&h=200&fit=crop" style="width: 100%; height: 150px; object-fit: cover;" alt="活动图片">
                    <div class="card-status status-open" style="position: absolute; top: var(--space); right: var(--space);">即将开始</div>
                    <div style="position: absolute; top: var(--space); left: var(--space); background: var(--activity-gradient); color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 500;">健身挑战</div>
                  </div>
                  <div class="card-content">
                    <div class="card-title">🏃‍♂️ 周末邻里健身挑战赛</div>
                    <div class="card-description">一起运动，增进邻里感情，还有丰厚奖品等你来拿！</div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-calendar-line"></i>
                        <span>本周六 09:00</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-map-pin-line"></i>
                        <span>社区广场</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-group-line"></i>
                        <span>12/20人</span>
                      </div>
                    </div>
                    <!-- 参与进度条 -->
                    <div style="margin-top: var(--space); background: var(--bg-secondary); border-radius: 8px; height: 6px; overflow: hidden;">
                      <div style="width: 60%; height: 100%; background: var(--activity-gradient);"></div>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div style="position: relative;">
                    <img src="https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=200&fit=crop" style="width: 100%; height: 150px; object-fit: cover;" alt="活动图片">
                    <div class="card-status status-open" style="position: absolute; top: var(--space); right: var(--space);">即将开始</div>
                    <div style="position: absolute; top: var(--space); left: var(--space); background: var(--activity-gradient); color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 500;">杭城文化夜</div>
                  </div>
                  <div class="card-content">
                    <div class="card-title">🍵 杭城文化夜 - 茶艺体验</div>
                    <div class="card-description">品味杭州茶文化，学习茶艺技巧，感受传统文化魅力</div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-calendar-line"></i>
                        <span>下周五 19:00</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-map-pin-line"></i>
                        <span>社区茶室</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-group-line"></i>
                        <span>8/10人</span>
                      </div>
                    </div>
                    <div style="margin-top: var(--space); background: var(--bg-secondary); border-radius: 8px; height: 6px; overflow: hidden;">
                      <div style="width: 80%; height: 100%; background: var(--activity-gradient);"></div>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div style="position: relative;">
                    <img src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=200&fit=crop" style="width: 100%; height: 150px; object-fit: cover;" alt="活动图片">
                    <div class="card-status status-closed" style="position: absolute; top: var(--space); right: var(--space);">已结束</div>
                    <div style="position: absolute; top: var(--space); left: var(--space); background: rgba(114, 46, 209, 0.9); color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 500;">邻里分享日</div>
                  </div>
                  <div class="card-content">
                    <div class="card-title">🍽️ 邻里美食分享会</div>
                    <div class="card-description">分享家乡美食，交流烹饪心得，品尝不同地域的特色菜肴</div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-calendar-line"></i>
                        <span>上周日 15:00</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-map-pin-line"></i>
                        <span>社区活动室</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-group-line"></i>
                        <span>15/15人</span>
                      </div>
                    </div>
                    <div style="margin-top: var(--space); background: var(--bg-secondary); border-radius: 8px; height: 6px; overflow: hidden;">
                      <div style="width: 100%; height: 100%; background: var(--success-color);"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item active">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 发布活动页面 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">发布社区活动</div>
              <div class="nav-action" style="background: var(--activity-gradient); color: white; padding: 4px 12px; border-radius: 12px;">发布</div>
            </div>
            
            <div class="page-content" style="padding: var(--space);">
              <!-- 活动模板选择 -->
              <div style="background: var(--activity-gradient); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); color: white;">
                <div style="font-size: 16px; font-weight: 500; margin-bottom: var(--space);">🎯 选择活动模板</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: var(--space-sm);">
                  <div style="background: rgba(255,255,255,0.2); border-radius: var(--radius); padding: var(--space-sm); text-align: center; border: 2px solid white;">
                    <i class="ri-presentation-line" style="font-size: 20px; margin-bottom: 4px;"></i>
                    <div style="font-size: 12px;">讲座</div>
                  </div>
                  <div style="background: rgba(255,255,255,0.2); border-radius: var(--radius); padding: var(--space-sm); text-align: center;">
                    <i class="ri-tools-line" style="font-size: 20px; margin-bottom: 4px;"></i>
                    <div style="font-size: 12px;">工作坊</div>
                  </div>
                  <div style="background: rgba(255,255,255,0.2); border-radius: var(--radius); padding: var(--space-sm); text-align: center;">
                    <i class="ri-group-line" style="font-size: 20px; margin-bottom: 4px;"></i>
                    <div style="font-size: 12px;">社交</div>
                  </div>
                </div>
              </div>
              
              <!-- 活动信息 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">活动信息</div>
                <input type="text" placeholder="活动标题" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); margin-bottom: var(--space);">
                <textarea placeholder="活动描述，包括活动内容、注意事项等..." style="width: 100%; height: 80px; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); resize: none; margin-bottom: var(--space);"></textarea>
                <div style="display: flex; align-items: center; justify-content: space-between; padding: var(--space); background: var(--bg-secondary); border-radius: var(--radius); border: 1px solid var(--border-color);">
                  <span style="color: var(--text-secondary);">选择活动类型</span>
                  <i class="ri-arrow-down-s-line" style="color: var(--text-tertiary);"></i>
                </div>
              </div>
              
              <!-- 时间地点 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">时间地点</div>
                <div style="display: flex; gap: var(--space); margin-bottom: var(--space);">
                  <div style="flex: 1;">
                    <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-sm);">活动日期</div>
                    <input type="date" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
                  </div>
                  <div style="flex: 1;">
                    <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-sm);">开始时间</div>
                    <input type="time" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
                  </div>
                </div>
                <div style="display: flex; align-items: center; gap: var(--space);">
                  <input type="text" placeholder="活动地点" style="flex: 1; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
                  <button style="padding: var(--space); background: var(--activity-gradient); color: white; border: none; border-radius: var(--radius); font-size: 14px;">
                    <i class="ri-map-pin-line"></i>
                  </button>
                </div>
              </div>
              
              <!-- 参与设置 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">参与设置</div>
                <div style="display: flex; gap: var(--space); margin-bottom: var(--space);">
                  <div style="flex: 1;">
                    <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-sm);">最大人数</div>
                    <input type="number" placeholder="20" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
                  </div>
                  <div style="flex: 1;">
                    <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-sm);">报名费用</div>
                    <input type="number" placeholder="0" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
                  </div>
                </div>
                <textarea placeholder="参与要求（可选）" style="width: 100%; height: 60px; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); resize: none;"></textarea>
              </div>
              
              <!-- 提交按钮 -->
              <button style="width: 100%; padding: var(--space-lg); background: var(--activity-gradient); color: white; border: none; border-radius: var(--radius-lg); font-size: 16px; font-weight: 500; margin-top: var(--space);">立即发布活动</button>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item active">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 邻里生活服务模块 -->
    <div class="module-section">
      <h2 class="module-title service">邻里生活服务 - 贴心便民</h2>
      <div class="modules-grid">
        <!-- 生活服务首页 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">邻里生活服务</div>
              <div class="nav-action"><i class="ri-search-line"></i></div>
            </div>
            
            <div class="page-content">
              <!-- 服务横幅 -->
              <div style="height: 120px; background: var(--service-gradient); margin: var(--space); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; position: relative; overflow: hidden;">
                <div style="text-align: center; z-index: 2;">
                  <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">🏠 专业服务，贴心到家</div>
                  <div style="font-size: 14px; opacity: 0.8;">精选优质服务商，为您提供便民服务</div>
                </div>
                <div style="position: absolute; top: -30px; right: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              </div>
              
              <!-- 服务分类网格 -->
              <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--space); padding: var(--space); margin-bottom: var(--space);">
                <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space-lg); text-align: center; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light);">
                  <div style="width: 60px; height: 60px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space); color: white; font-size: 28px;">
                    <i class="ri-tools-line"></i>
                  </div>
                  <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space-xs);">家政服务</div>
                  <div style="font-size: 12px; color: var(--text-tertiary);">保洁、月嫂、育儿嫂</div>
                </div>
                
                <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space-lg); text-align: center; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light);">
                  <div style="width: 60px; height: 60px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space); color: white; font-size: 28px;">
                    <i class="ri-hammer-line"></i>
                  </div>
                  <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space-xs);">维修服务</div>
                  <div style="font-size: 12px; color: var(--text-tertiary);">家电维修、装修监理</div>
                </div>
                
                <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space-lg); text-align: center; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light);">
                  <div style="width: 60px; height: 60px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space); color: white; font-size: 28px;">
                    <i class="ri-heart-pulse-line"></i>
                  </div>
                  <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space-xs);">健康服务</div>
                  <div style="font-size: 12px; color: var(--text-tertiary);">上门理疗、健康体检</div>
                </div>
                
                <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space-lg); text-align: center; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light);">
                  <div style="width: 60px; height: 60px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space); color: white; font-size: 28px;">
                    <i class="ri-book-line"></i>
                  </div>
                  <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space-xs);">教育服务</div>
                  <div style="font-size: 12px; color: var(--text-tertiary);">家教预约、兴趣班</div>
                </div>
              </div>
              
              <!-- 推荐服务商 -->
              <div style="padding: 0 var(--space);">
                <div style="font-size: 18px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--service-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  推荐服务商
                </div>
                
                <div class="card-item">
                  <div class="card-header">
                    <div style="width: 50px; height: 50px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                      <i class="ri-broom-line"></i>
                    </div>
                    <div class="card-user-info">
                      <div class="user-name">阿姨帮家政服务</div>
                      <div class="user-location">专业保洁 • ⭐ 4.9分 • 已服务156户</div>
                    </div>
                    <div style="background: rgba(114, 46, 209, 0.1); color: #722ED1; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 500;">认证</div>
                  </div>
                  <div class="card-content">
                    <div class="card-description">提供专业的家庭保洁服务，经验丰富，服务贴心，价格合理。</div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space);">
                      <div style="font-size: 18px; font-weight: 600; color: #722ED1;">¥80/次起</div>
                      <button style="background: var(--service-gradient); color: white; border: none; padding: 8px 20px; border-radius: 20px; font-size: 14px;">立即预约</button>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div class="card-header">
                    <div style="width: 50px; height: 50px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                      <i class="ri-tools-line"></i>
                    </div>
                    <div class="card-user-info">
                      <div class="user-name">老王水电维修</div>
                      <div class="user-location">水电专家 • ⭐ 4.8分 • 已服务89户</div>
                    </div>
                    <div style="background: rgba(114, 46, 209, 0.1); color: #722ED1; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 500;">认证</div>
                  </div>
                  <div class="card-content">
                    <div class="card-description">专业水电维修，24小时上门服务，质量保证，收费透明。</div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space);">
                      <div style="font-size: 18px; font-weight: 600; color: #722ED1;">¥120/次起</div>
                      <button style="background: var(--service-gradient); color: white; border: none; padding: 8px 20px; border-radius: 20px; font-size: 14px;">立即预约</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item active">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 邻里出行模块 -->
    <div class="module-section">
      <h2 class="module-title travel">邻里出行 - 绿色共享</h2>
      <div class="modules-grid">
        <!-- 拼车列表页 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">邻里出行</div>
              <div class="nav-action"><i class="ri-map-line"></i></div>
            </div>
            
            <div class="page-content">
              <!-- 出行横幅 -->
              <div style="height: 120px; background: var(--travel-gradient); margin: var(--space); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; position: relative; overflow: hidden;">
                <div style="text-align: center; z-index: 2;">
                  <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">🚗 绿色出行，邻里共享</div>
                  <div style="font-size: 14px; opacity: 0.8;">让每一次出行都更环保</div>
                </div>
                <div style="position: absolute; top: -30px; right: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              </div>
              
              <!-- 出行类型选择 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="display: flex; gap: var(--space);">
                  <div style="flex: 1; background: var(--travel-gradient); border-radius: var(--radius); padding: var(--space); text-align: center; color: white;">
                    <i class="ri-car-line" style="font-size: 24px; margin-bottom: var(--space-xs);"></i>
                    <div style="font-size: 14px;">拼车</div>
                  </div>
                  <div style="flex: 1; background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space); text-align: center; color: var(--text-secondary);">
                    <i class="ri-steering-line" style="font-size: 24px; margin-bottom: var(--space-xs);"></i>
                    <div style="font-size: 14px;">代驾</div>
                  </div>
                  <div style="flex: 1; background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space); text-align: center; color: var(--text-secondary);">
                    <i class="ri-parking-line" style="font-size: 24px; margin-bottom: var(--space-xs);"></i>
                    <div style="font-size: 14px;">车位</div>
                  </div>
                </div>
              </div>
              
              <!-- 筛选栏 -->
              <div class="filter-bar">
                <div class="filter-scroll">
                  <div class="filter-item active" style="background: var(--travel-gradient); color: white; border-color: var(--info-color);">全部</div>
                  <div class="filter-item">今天</div>
                  <div class="filter-item">明天</div>
                  <div class="filter-item">机场</div>
                  <div class="filter-item">高铁站</div>
                  <div class="filter-item">长途</div>
                </div>
              </div>
              
              <!-- 拼车列表 -->
              <div class="card-list">
                <div class="card-item">
                  <div class="card-header">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--travel-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">
                      <i class="ri-car-line"></i>
                    </div>
                    <div class="card-user-info">
                      <div class="user-name">车主小李 • 奥迪A4</div>
                      <div class="user-location">⭐ 4.8分 • 已完成38次拼车</div>
                    </div>
                    <div class="card-status status-open">2座</div>
                  </div>
                  <div class="card-content">
                    <div style="display: flex; align-items: center; gap: var(--space); margin-bottom: var(--space);">
                      <div style="flex: 1; display: flex; align-items: center; gap: var(--space-sm);">
                        <div style="width: 8px; height: 8px; border-radius: 50%; background: var(--success-color);"></div>
                        <span style="font-size: 16px; font-weight: 500;">欧美金融城A区</span>
                      </div>
                      <i class="ri-arrow-right-line" style="color: var(--text-tertiary);"></i>
                      <div style="flex: 1; display: flex; align-items: center; gap: var(--space-sm);">
                        <div style="width: 8px; height: 8px; border-radius: 50%; background: var(--error-color);"></div>
                        <span style="font-size: 16px; font-weight: 500;">萧山国际机场</span>
                      </div>
                    </div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-time-line"></i>
                        <span>明天 08:30</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-money-dollar-circle-line"></i>
                        <span>¥45/人</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-map-pin-line"></i>
                        <span>预计1小时</span>
                      </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space);">
                      <div style="font-size: 18px; font-weight: 600; color: var(--info-color);">¥45/人</div>
                      <button style="background: var(--travel-gradient); color: white; border: none; padding: 8px 20px; border-radius: 20px; font-size: 14px;">立即拼车</button>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div class="card-header">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--travel-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">
                      <i class="ri-parking-line"></i>
                    </div>
                    <div class="card-user-info">
                      <div class="user-name">车位分享 • A区地下车库</div>
                      <div class="user-location">固定车位 • 临时共享</div>
                    </div>
                    <div class="card-status status-open">可用</div>
                  </div>
                  <div class="card-content">
                    <div class="card-description">A区地下车库B2层，位置方便，适合临时停车。</div>
                    <div class="card-meta">
                      <div class="meta-item">
                        <i class="ri-time-line"></i>
                        <span>今天 9:00-18:00</span>
                      </div>
                      <div class="meta-item">
                        <i class="ri-money-dollar-circle-line"></i>
                        <span>¥10/小时</span>
                      </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space);">
                      <div style="font-size: 18px; font-weight: 600; color: var(--info-color);">¥10/小时</div>
                      <button style="background: var(--travel-gradient); color: white; border: none; padding: 8px 20px; border-radius: 20px; font-size: 14px;">预约车位</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item active">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 积分商城模块 -->
    <div class="module-section">
      <h2 class="module-title points">积分商城 - 温暖奖励</h2>
      <div class="modules-grid">
        <!-- 积分商城首页 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">积分商城</div>
              <div class="nav-action"><i class="ri-history-line"></i></div>
            </div>
            
            <div class="page-content">
              <!-- 积分余额 -->
              <div style="background: var(--points-gradient); color: white; padding: var(--space-lg); margin: var(--space); border-radius: var(--radius-lg); text-align: center;">
                <div style="font-size: 14px; opacity: 0.8; margin-bottom: var(--space-sm);">我的积分余额</div>
                <div style="font-size: 36px; font-weight: 600; margin-bottom: var(--space);">1,256</div>
                <div style="display: flex; justify-content: center; gap: var(--space-lg);">
                  <div style="display: flex; align-items: center; gap: var(--space-xs);">
                    <i class="ri-arrow-up-line"></i>
                    <span style="font-size: 12px;">本月获得 +320</span>
                  </div>
                  <div style="display: flex; align-items: center; gap: var(--space-xs);">
                    <i class="ri-arrow-down-line"></i>
                    <span style="font-size: 12px;">本月使用 -180</span>
                  </div>
                </div>
              </div>
              
              <!-- 快速获取积分 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--points-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  快速获取积分
                </div>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: var(--space);">
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--help-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: white; font-size: 18px;">
                      <i class="ri-heart-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">帮助邻居</div>
                    <div style="font-size: 12px; color: var(--warm-orange); font-weight: 500;">+50积分</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--activity-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: white; font-size: 18px;">
                      <i class="ri-calendar-check-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">参与活动</div>
                    <div style="font-size: 12px; color: var(--warm-orange); font-weight: 500;">+30积分</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--points-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: white; font-size: 18px;">
                      <i class="ri-calendar-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">每日签到</div>
                    <div style="font-size: 12px; color: var(--warm-orange); font-weight: 500;">+5积分</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: white; font-size: 18px;">
                      <i class="ri-share-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">分享小程序</div>
                    <div style="font-size: 12px; color: var(--warm-orange); font-weight: 500;">+10积分</div>
                  </div>
                </div>
              </div>
              
              <!-- 兑换商品 -->
              <div style="padding: 0 var(--space);">
                <div style="font-size: 18px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--points-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  热门兑换
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--space); margin-bottom: var(--space);">
                  <div style="background: var(--bg-primary); border-radius: var(--radius-lg); overflow: hidden; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light);">
                    <img src="https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=200&h=120&fit=crop" style="width: 100%; height: 80px; object-fit: cover;" alt="商品图片">
                    <div style="padding: var(--space);">
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space-xs);">星巴克咖啡券</div>
                      <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: var(--space);">中杯任意饮品</div>
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="color: var(--warm-orange); font-weight: 500;">100积分</div>
                        <div style="font-size: 12px; color: var(--text-tertiary);">库存50</div>
                      </div>
                      <button style="width: 100%; margin-top: var(--space); padding: 8px; background: var(--points-gradient); color: white; border: none; border-radius: var(--radius); font-size: 12px;">立即兑换</button>
                    </div>
                  </div>
                  
                  <div style="background: var(--bg-primary); border-radius: var(--radius-lg); overflow: hidden; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light);">
                    <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=200&h=120&fit=crop" style="width: 100%; height: 80px; object-fit: cover;" alt="商品图片">
                    <div style="padding: var(--space);">
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space-xs);">家政服务券</div>
                      <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: var(--space);">2小时保洁服务</div>
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="color: var(--warm-orange); font-weight: 500;">500积分</div>
                        <div style="font-size: 12px; color: var(--text-tertiary);">库存10</div>
                      </div>
                      <button style="width: 100%; margin-top: var(--space); padding: 8px; background: var(--points-gradient); color: white; border: none; border-radius: var(--radius); font-size: 12px;">立即兑换</button>
                    </div>
                  </div>
                  
                  <div style="background: var(--bg-primary); border-radius: var(--radius-lg); overflow: hidden; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light);">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=120&fit=crop" style="width: 100%; height: 80px; object-fit: cover;" alt="商品图片">
                    <div style="padding: var(--space);">
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space-xs);">月度暖友徽章</div>
                      <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: var(--space);">专属身份标识</div>
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="color: var(--warm-orange); font-weight: 500;">1000积分</div>
                        <div style="font-size: 12px; color: var(--text-tertiary);">限量1</div>
                      </div>
                      <button style="width: 100%; margin-top: var(--space); padding: 8px; background: var(--bg-secondary); color: var(--text-tertiary); border: none; border-radius: var(--radius); font-size: 12px;">积分不足</button>
                    </div>
                  </div>
                  
                  <div style="background: var(--bg-primary); border-radius: var(--radius-lg); overflow: hidden; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light);">
                    <img src="https://images.unsplash.com/photo-1567306301408-9b74779a11af?w=200&h=120&fit=crop" style="width: 100%; height: 80px; object-fit: cover;" alt="商品图片">
                    <div style="padding: var(--space);">
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space-xs);">电影票</div>
                      <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: var(--space);">万达影城通用券</div>
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="color: var(--warm-orange); font-weight: 500;">200积分</div>
                        <div style="font-size: 12px; color: var(--text-tertiary);">库存30</div>
                      </div>
                      <button style="width: 100%; margin-top: var(--space); padding: 8px; background: var(--points-gradient); color: white; border: none; border-radius: var(--radius); font-size: 12px;">立即兑换</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item active">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息中心模块 -->
    <div class="module-section">
      <h2 class="module-title profile">消息中心 - 沟通桥梁</h2>
      <div class="modules-grid">
        <!-- 消息列表页 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">消息中心</div>
              <div class="nav-action"><i class="ri-settings-line"></i></div>
            </div>
            
            <div class="page-content">
              <!-- 消息横幅 -->
              <div style="height: 120px; background: var(--profile-gradient); margin: var(--space); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; position: relative; overflow: hidden;">
                <div style="text-align: center; z-index: 2;">
                  <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">💬 消息中心，沟通无限</div>
                  <div style="font-size: 14px; opacity: 0.8;">及时获取重要消息通知</div>
                </div>
                <div style="position: absolute; top: -30px; right: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              </div>
              
              <!-- 消息分类 -->
              <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: var(--space); padding: var(--space); margin-bottom: var(--space);">
                <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); text-align: center; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light); position: relative;">
                  <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--help-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: white; font-size: 18px;">
                    <i class="ri-heart-line"></i>
                  </div>
                  <div style="font-size: 14px; color: var(--text-primary);">互助消息</div>
                  <div class="message-badge">3</div>
                </div>
                
                <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); text-align: center; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light); position: relative;">
                  <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--activity-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: white; font-size: 18px;">
                    <i class="ri-calendar-event-line"></i>
                  </div>
                  <div style="font-size: 14px; color: var(--text-primary);">活动通知</div>
                  <div class="message-badge">1</div>
                </div>
                
                <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); text-align: center; box-shadow: var(--shadow-sm); border: 1px solid var(--border-light);">
                  <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: white; font-size: 18px;">
                    <i class="ri-notification-line"></i>
                  </div>
                  <div style="font-size: 14px; color: var(--text-primary);">系统通知</div>
                </div>
              </div>
              
              <!-- 消息列表 -->
              <div class="card-list">
                <div class="card-item">
                  <div class="card-header">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--help-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">
                      <i class="ri-heart-line"></i>
                    </div>
                    <div class="card-user-info">
                      <div class="user-name">互助需求回复</div>
                      <div class="user-location">来自暖友小王</div>
                    </div>
                    <div style="font-size: 12px; color: var(--text-tertiary);">2分钟前</div>
                  </div>
                  <div class="card-content">
                    <div class="card-description">您好，我可以帮您修理水管，我是专业的水电工，有5年经验。</div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div class="card-header">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--activity-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">
                      <i class="ri-calendar-event-line"></i>
                    </div>
                    <div class="card-user-info">
                      <div class="user-name">活动提醒</div>
                      <div class="user-location">邻里健身挑战赛</div>
                    </div>
                    <div style="font-size: 12px; color: var(--text-tertiary);">1小时前</div>
                  </div>
                  <div class="card-content">
                    <div class="card-description">您报名的"周末邻里健身挑战赛"将于明天上午9点开始，请准时参加！</div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div class="card-header">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--trade-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">
                      <i class="ri-exchange-line"></i>
                    </div>
                    <div class="card-user-info">
                      <div class="user-name">交易询问</div>
                      <div class="user-location">来自邻居小陈</div>
                    </div>
                    <div style="font-size: 12px; color: var(--text-tertiary);">3小时前</div>
                  </div>
                  <div class="card-content">
                    <div class="card-description">您好，请问您的iPad还在出售吗？价格还能商量吗？</div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div class="card-header">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">
                      <i class="ri-notification-line"></i>
                    </div>
                    <div class="card-user-info">
                      <div class="user-name">系统通知</div>
                      <div class="user-location">积分奖励</div>
                    </div>
                    <div style="font-size: 12px; color: var(--text-tertiary);">昨天</div>
                  </div>
                  <div class="card-content">
                    <div class="card-description">恭喜您！完成互助任务获得50积分奖励，当前积分余额：1256分。</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item active">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 在线客服页面 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">在线客服</div>
              <div class="nav-action"><i class="ri-phone-line"></i></div>
            </div>
            
            <div class="page-content" style="display: flex; flex-direction: column;">
              <!-- 客服信息 -->
              <div style="background: var(--service-gradient); color: white; padding: var(--space); display: flex; align-items: center; gap: var(--space);">
                <div style="width: 50px; height: 50px; border-radius: 50%; background: rgba(255,255,255,0.2); display: flex; align-items: center; justify-content: center; font-size: 20px;">
                  <i class="ri-customer-service-line"></i>
                </div>
                <div>
                  <div style="font-size: 16px; font-weight: 500;">邻里暖友客服</div>
                  <div style="font-size: 12px; opacity: 0.8;">在线 • 平均响应时间 2分钟</div>
                </div>
              </div>
              
              <!-- 聊天记录 -->
              <div style="flex: 1; padding: var(--space); overflow-y: auto;">
                <!-- 客服消息 -->
                <div style="display: flex; align-items: flex-start; gap: var(--space); margin-bottom: var(--space);">
                  <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; flex-shrink: 0;">
                    <i class="ri-customer-service-line"></i>
                  </div>
                  <div style="background: var(--bg-primary); padding: var(--space); border-radius: var(--radius); box-shadow: var(--shadow-sm); max-width: 70%;">
                    <div style="font-size: 14px; color: var(--text-primary);">您好！欢迎使用我的邻里暖友小程序，有什么可以帮助您的吗？</div>
                    <div style="font-size: 12px; color: var(--text-tertiary); margin-top: var(--space-xs);">09:30</div>
                  </div>
                </div>
                
                <!-- 用户消息 -->
                <div style="display: flex; align-items: flex-start; gap: var(--space); margin-bottom: var(--space); justify-content: flex-end;">
                  <div style="background: var(--service-gradient); color: white; padding: var(--space); border-radius: var(--radius); max-width: 70%;">
                    <div style="font-size: 14px;">我想了解一下如何发布互助需求？</div>
                    <div style="font-size: 12px; opacity: 0.8; margin-top: var(--space-xs); text-align: right;">09:32</div>
                  </div>
                  <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--profile-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; flex-shrink: 0;">
                    我
                  </div>
                </div>
                
                <!-- 客服消息 -->
                <div style="display: flex; align-items: flex-start; gap: var(--space); margin-bottom: var(--space);">
                  <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; flex-shrink: 0;">
                    <i class="ri-customer-service-line"></i>
                  </div>
                  <div style="background: var(--bg-primary); padding: var(--space); border-radius: var(--radius); box-shadow: var(--shadow-sm); max-width: 70%;">
                    <div style="font-size: 14px; color: var(--text-primary);">很简单！您可以按照以下步骤操作：</div>
                    <div style="font-size: 14px; color: var(--text-primary); margin-top: var(--space-sm);">
                      1. 进入"邻里互助"页面<br>
                      2. 点击右下角的"+"按钮<br>
                      3. 填写需求信息<br>
                      4. 设置积分奖励<br>
                      5. 发布即可
                    </div>
                    <div style="font-size: 12px; color: var(--text-tertiary); margin-top: var(--space-xs);">09:33</div>
                  </div>
                </div>
                
                <!-- 快捷回复 -->
                <div style="background: rgba(54, 207, 201, 0.1); border-radius: var(--radius); padding: var(--space); margin-bottom: var(--space);">
                  <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-sm);">常见问题快捷回复：</div>
                  <div style="display: flex; flex-wrap: wrap; gap: var(--space-sm);">
                    <button style="background: var(--bg-primary); border: 1px solid var(--border-color); padding: 8px 12px; border-radius: 16px; font-size: 12px; color: var(--text-secondary);">如何获得积分？</button>
                    <button style="background: var(--bg-primary); border: 1px solid var(--border-color); padding: 8px 12px; border-radius: 16px; font-size: 12px; color: var(--text-secondary);">如何参与活动？</button>
                    <button style="background: var(--bg-primary); border: 1px solid var(--border-color); padding: 8px 12px; border-radius: 16px; font-size: 12px; color: var(--text-secondary);">交易安全吗？</button>
                  </div>
                </div>
              </div>
              
              <!-- 输入框 -->
              <div style="background: var(--bg-primary); border-top: 1px solid var(--border-light); padding: var(--space); display: flex; align-items: center; gap: var(--space);">
                <button style="background: var(--bg-secondary); border: none; padding: 8px; border-radius: 50%; color: var(--text-tertiary);">
                  <i class="ri-emotion-line"></i>
                </button>
                <input type="text" placeholder="输入消息..." style="flex: 1; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 20px; background: var(--bg-secondary);">
                <button style="background: var(--service-gradient); color: white; border: none; padding: 8px 12px; border-radius: 20px; font-size: 14px;">发送</button>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item active">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人中心模块 -->
    <div class="module-section">
      <h2 class="module-title profile">个人中心 - 暖友档案</h2>
      <div class="modules-grid">
        <!-- 个人中心首页 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">个人中心</div>
              <div class="nav-action"><i class="ri-settings-line"></i></div>
            </div>
            
            <div class="page-content">
              <!-- 用户信息卡片 -->
              <div style="background: var(--profile-gradient); color: white; padding: var(--space-lg); margin: var(--space); border-radius: var(--radius-lg);">
                <div style="display: flex; align-items: center; margin-bottom: var(--space-lg);">
                  <img src="https://randomuser.me/api/portraits/women/68.jpg" style="width: 70px; height: 70px; border-radius: 50%; margin-right: var(--space); border: 3px solid rgba(255,255,255,0.3);" alt="头像">
                  <div style="flex: 1;">
                    <div style="font-size: 20px; font-weight: 600; margin-bottom: var(--space-xs);">暖友小张</div>
                    <div style="font-size: 14px; opacity: 0.8; margin-bottom: var(--space-xs);">欧美金融城 A区 3栋</div>
                    <div style="display: flex; align-items: center; gap: var(--space-sm);">
                      <div style="background: var(--points-gradient); padding: 2px 8px; border-radius: 12px; font-size: 12px;">⭐ 暖友达人</div>
                      <div style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; font-size: 12px;">Lv.5</div>
                    </div>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-size: 12px; opacity: 0.8;">积分余额</div>
                    <div style="font-size: 18px; font-weight: 600;">1,256</div>
                  </div>
                </div>
                
                <div style="display: flex; justify-content: space-around; text-align: center;">
                  <div>
                    <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">12</div>
                    <div style="font-size: 12px; opacity: 0.8;">发布需求</div>
                  </div>
                  <div>
                    <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">28</div>
                    <div style="font-size: 12px; opacity: 0.8;">参与活动</div>
                  </div>
                  <div>
                    <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">5</div>
                    <div style="font-size: 12px; opacity: 0.8;">交易成功</div>
                  </div>
                  <div>
                    <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">156</div>
                    <div style="font-size: 12px; opacity: 0.8;">帮助次数</div>
                  </div>
                </div>
              </div>
              
              <!-- VIP会员卡片 -->
              <div style="background: linear-gradient(135deg, #FFD700, #FFA500); color: white; margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; gap: var(--space);">
                  <i class="ri-vip-crown-line" style="font-size: 24px;"></i>
                  <div>
                    <div style="font-size: 16px; font-weight: 500;">尊享VIP会员专属权益</div>
                    <div style="font-size: 12px; opacity: 0.8;">会员到期时间 2024-12-31</div>
                  </div>
                </div>
                <button style="background: rgba(255,255,255,0.2); border: none; padding: 8px 16px; border-radius: 20px; color: white; font-size: 14px;">立即续费</button>
              </div>
              
              <!-- 积分任务卡片 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm); display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; gap: var(--space);">
                  <i class="ri-coin-line" style="font-size: 24px; color: var(--warm-orange);"></i>
                  <div>
                    <div style="font-size: 16px; font-weight: 500; color: var(--text-primary);">完成任务赚取积分</div>
                    <div style="font-size: 12px; color: var(--text-secondary);">每日签到、互助邻居获得积分</div>
                  </div>
                </div>
                <button style="background: var(--points-gradient); color: white; border: none; padding: 8px 16px; border-radius: 20px; font-size: 14px;">兑换红包</button>
              </div>
              
              <!-- 我的记录 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 18px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--profile-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  我的记录
                </div>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: var(--space);">
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(54, 207, 201, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: #36CFC9; font-size: 18px;">
                      <i class="ri-eye-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">浏览</div>
                  </div>
                  <div style="text-align: center; position: relative;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255, 170, 108, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: var(--warm-orange); font-size: 18px;">
                      <i class="ri-message-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">消息</div>
                    <div class="message-badge">3</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(82, 196, 26, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: var(--success-color); font-size: 18px;">
                      <i class="ri-thumb-up-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">点赞</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(114, 46, 209, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: #722ED1; font-size: 18px;">
                      <i class="ri-wallet-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">鼓励</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255, 77, 79, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: var(--error-color); font-size: 18px;">
                      <i class="ri-file-list-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">付费账单</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(24, 144, 255, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: var(--info-color); font-size: 18px;">
                      <i class="ri-share-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">推荐</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(250, 173, 20, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: var(--warning-color); font-size: 18px;">
                      <i class="ri-coupon-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">优惠码</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255, 107, 157, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: var(--warm-pink); font-size: 18px;">
                      <i class="ri-refresh-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">更新信息</div>
                  </div>
                </div>
              </div>
              
              <!-- 功能菜单 -->
              <div style="padding: 0 var(--space);">
                <div class="card-item">
                  <div style="padding: var(--space);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                      <div style="display: flex; align-items: center; gap: var(--space);">
                        <i class="ri-user-settings-line" style="color: #36CFC9; font-size: 20px;"></i>
                        <span style="font-size: 16px; font-weight: 500;">个人资料</span>
                      </div>
                      <i class="ri-arrow-right-s-line" style="color: var(--text-tertiary);"></i>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div style="padding: var(--space);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                      <div style="display: flex; align-items: center; gap: var(--space);">
                        <i class="ri-shield-check-line" style="color: var(--success-color); font-size: 20px;"></i>
                        <span style="font-size: 16px; font-weight: 500;">安全中心</span>
                      </div>
                      <i class="ri-arrow-right-s-line" style="color: var(--text-tertiary);"></i>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div style="padding: var(--space);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                      <div style="display: flex; align-items: center; gap: var(--space);">
                        <i class="ri-map-pin-line" style="color: var(--info-color); font-size: 20px;"></i>
                        <span style="font-size: 16px; font-weight: 500;">收货地址</span>
                      </div>
                      <i class="ri-arrow-right-s-line" style="color: var(--text-tertiary);"></i>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div style="padding: var(--space);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                      <div style="display: flex; align-items: center; gap: var(--space);">
                        <i class="ri-customer-service-line" style="color: #722ED1; font-size: 20px;"></i>
                        <span style="font-size: 16px; font-weight: 500;">帮助与反馈</span>
                      </div>
                      <i class="ri-arrow-right-s-line" style="color: var(--text-tertiary);"></i>
                    </div>
                  </div>
                </div>
                
                <div class="card-item">
                  <div style="padding: var(--space);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                      <div style="display: flex; align-items: center; gap: var(--space);">
                        <i class="ri-information-line" style="color: var(--text-secondary); font-size: 20px;"></i>
                        <span style="font-size: 16px; font-weight: 500;">关于我们</span>
                      </div>
                      <i class="ri-arrow-right-s-line" style="color: var(--text-tertiary);"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item active">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 预约留言页面 -->
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">预约留言</div>
              <div class="nav-action" style="background: var(--profile-gradient); color: white; padding: 4px 12px; border-radius: 12px;">提交</div>
            </div>
            
            <div class="page-content" style="padding: var(--space);">
              <!-- 留言类型选择 -->
              <div style="background: var(--profile-gradient); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); color: white;">
                <div style="font-size: 16px; font-weight: 500; margin-bottom: var(--space);">📝 留言类型</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: var(--space-sm);">
                  <div style="background: rgba(255,255,255,0.2); border-radius: var(--radius); padding: var(--space-sm); text-align: center; border: 2px solid white;">
                    <i class="ri-feedback-line" style="font-size: 20px; margin-bottom: 4px;"></i>
                    <div style="font-size: 12px;">意见反馈</div>
                  </div>
                  <div style="background: rgba(255,255,255,0.2); border-radius: var(--radius); padding: var(--space-sm); text-align: center;">
                    <i class="ri-bug-line" style="font-size: 20px; margin-bottom: 4px;"></i>
                    <div style="font-size: 12px;">问题举报</div>
                  </div>
                  <div style="background: rgba(255,255,255,0.2); border-radius: var(--radius); padding: var(--space-sm); text-align: center;">
                    <i class="ri-lightbulb-line" style="font-size: 20px; margin-bottom: 4px;"></i>
                    <div style="font-size: 12px;">功能建议</div>
                  </div>
                </div>
              </div>
              
              <!-- 联系信息 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">联系信息</div>
                <input type="text" placeholder="您的姓名" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); margin-bottom: var(--space);">
                <input type="tel" placeholder="联系电话" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); margin-bottom: var(--space);">
                <input type="email" placeholder="邮箱地址（可选）" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary);">
              </div>
              
              <!-- 留言内容 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">留言内容</div>
                <input type="text" placeholder="留言标题" style="width: 100%; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); margin-bottom: var(--space);">
                <textarea placeholder="请详细描述您的问题或建议..." style="width: 100%; height: 120px; padding: var(--space); border: 1px solid var(--border-color); border-radius: var(--radius); font-size: 14px; background: var(--bg-secondary); resize: none;"></textarea>
              </div>
              
              <!-- 图片上传 -->
              <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--space); margin-bottom: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space);">上传图片（可选）</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: var(--space);">
                  <div style="aspect-ratio: 1; background: var(--bg-secondary); border: 2px dashed var(--border-color); border-radius: var(--radius); display: flex; align-items: center; justify-content: center; flex-direction: column; color: var(--text-tertiary);">
                    <i class="ri-camera-line" style="font-size: 24px; margin-bottom: var(--space-xs);"></i>
                    <span style="font-size: 12px;">添加图片</span>
                  </div>
                </div>
              </div>
              
              <!-- 提交按钮 -->
              <button style="width: 100%; padding: var(--space-lg); background: var(--profile-gradient); color: white; border: none; border-radius: var(--radius-lg); font-size: 16px; font-weight: 500; margin-top: var(--space);">提交留言</button>
              
              <!-- 温馨提示 -->
              <div style="background: rgba(54, 207, 201, 0.1); border-radius: var(--radius); padding: var(--space); margin-top: var(--space); border-left: 4px solid #36CFC9;">
                <div style="font-size: 14px; color: var(--text-secondary); line-height: 1.5;">
                  <i class="ri-information-line" style="color: #36CFC9; margin-right: var(--space-xs);"></i>
                  我们会在24小时内回复您的留言，感谢您对邻里暖友的支持！
                </div>
              </div>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item active">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设计特色说明 -->
    <div class="module-section">
      <h2 class="module-title">🎨 设计特色与技术亮点</h2>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--space-lg);">
        <div>
          <h4 style="color: var(--primary-color); margin-bottom: var(--space); display: flex; align-items: center;">
            <i class="ri-palette-line" style="margin-right: var(--space-sm);"></i>
            色彩设计理念
          </h4>
          <ul style="line-height: 1.8; color: var(--text-secondary);">
            <li><strong>主色调</strong>：蓝紫渐变(#6168D5 → #8D76E8)，体现专业与信任</li>
            <li><strong>互助模块</strong>：粉紫渐变(#FF6B9D → #FF8E9B)，传达温暖与关怀</li>
            <li><strong>交易模块</strong>：绿色渐变(#52C41A → #73D13D)，象征成长与环保</li>
            <li><strong>活动模块</strong>：橙色渐变(#FFAA6C → #FF7A45)，营造活跃氛围</li>
            <li><strong>服务模块</strong>：紫色渐变(#722ED1 → #9254DE)，展现专业品质</li>
            <li><strong>出行模块</strong>：蓝色渐变(#1890FF → #40A9FF)，体现科技感</li>
            <li><strong>积分模块</strong>：金色渐变(#F6D365 → #FDA085)，突出价值感</li>
            <li><strong>个人中心</strong>：青色渐变(#36CFC9 → #13C2C2)，营造亲和力</li>
          </ul>
        </div>
        
        <div>
          <h4 style="color: var(--primary-color); margin-bottom: var(--space); display: flex; align-items: center;">
            <i class="ri-layout-line" style="margin-right: var(--space-sm);"></i>
            界面结构设计
          </h4>
          <ul style="line-height: 1.8; color: var(--text-secondary);">
            <li><strong>卡片式布局</strong>：统一的圆角和阴影，提升视觉层次</li>
            <li><strong>渐变元素</strong>：巧妙运用渐变色，增强视觉吸引力</li>
            <li><strong>图标系统</strong>：使用Remix Icon图标库，保持风格一致</li>
            <li><strong>响应式设计</strong>：适配不同屏幕尺寸，确保最佳体验</li>
            <li><strong>手机模拟器</strong>：真实还原小程序界面效果</li>
            <li><strong>状态反馈</strong>：清晰的视觉状态指示，用户操作有反馈</li>
            <li><strong>筛选系统</strong>：横向滚动标签，节省垂直空间</li>
            <li><strong>浮动按钮</strong>：便捷的快速操作入口</li>
          </ul>
        </div>
        
        <div>
          <h4 style="color: var(--primary-color); margin-bottom: var(--space); display: flex; align-items: center;">
            <i class="ri-rocket-line" style="margin-right: var(--space-sm);"></i>
            交互体验优化
          </h4>
          <ul style="line-height: 1.8; color: var(--text-secondary);">
            <li><strong>智能推荐</strong>：基于用户行为的个性化内容展示</li>
            <li><strong>一键操作</strong>：简化操作流程，提高使用效率</li>
            <li><strong>消息系统</strong>：完整的消息分类和实时通知</li>
            <li><strong>在线客服</strong>：24小时智能客服，快捷回复</li>
            <li><strong>积分激励</strong>：完整的积分获取和兑换体系</li>
            <li><strong>AI功能</strong>：智能估价、内容审核等AI应用</li>
            <li><strong>地理定位</strong>：基于位置的邻里服务推荐</li>
            <li><strong>安全保障</strong>：多重身份验证和交易保护</li>
          </ul>
        </div>
        
        <div>
          <h4 style="color: var(--primary-color); margin-bottom: var(--space); display: flex; align-items: center;">
            <i class="ri-home-heart-line" style="margin-right: var(--space-sm);"></i>
            品牌文化体现
          </h4>
          <ul style="line-height: 1.8; color: var(--text-secondary);">
            <li><strong>温暖理念</strong>："沃土滋润，暖友共生"贯穿整体设计</li>
            <li><strong>邻里特色</strong>：距离显示、本地化服务突出社区属性</li>
            <li><strong>高端定位</strong>：精致的视觉设计，符合目标用户群体</li>
            <li><strong>人文关怀</strong>：注重用户情感需求，打造温馨社区氛围</li>
            <li><strong>杭州元素</strong>：融入杭州文化特色，如茶艺体验活动</li>
            <li><strong>暖友概念</strong>：创建独特的社区身份认同</li>
            <li><strong>生态理念</strong>：绿色出行、物品循环利用的环保意识</li>
            <li><strong>共享经济</strong>：邻里资源共享，构建和谐社区</li>
          </ul>
        </div>

        <div>
          <h4 style="color: var(--primary-color); margin-bottom: var(--space); display: flex; align-items: center;">
            <i class="ri-code-line" style="margin-right: var(--space-sm);"></i>
            技术实现特点
          </h4>
          <ul style="line-height: 1.8; color: var(--text-secondary);">
            <li><strong>CSS变量系统</strong>：统一的设计token管理</li>
            <li><strong>模块化设计</strong>：每个功能模块独立的色彩体系</li>
            <li><strong>Flexbox布局</strong>：灵活的响应式布局系统</li>
            <li><strong>渐变动画</strong>：丰富的视觉过渡效果</li>
            <li><strong>组件化思维</strong>：可复用的UI组件设计</li>
            <li><strong>移动优先</strong>：专为移动端优化的交互设计</li>
            <li><strong>性能优化</strong>：轻量级实现，快速加载</li>
            <li><strong>可维护性</strong>：清晰的代码结构和注释</li>
          </ul>
        </div>

        <div>
          <h4 style="color: var(--primary-color); margin-bottom: var(--space); display: flex; align-items: center;">
            <i class="ri-functions-line" style="margin-right: var(--space-sm);"></i>
            完整功能覆盖
          </h4>
          <ul style="line-height: 1.8; color: var(--text-secondary);">
            <li><strong>邻里互助</strong>：紧急求助、技能匹配、积分奖励</li>
            <li><strong>闲置交易</strong>：AI估价、安全交易、物流配送</li>
            <li><strong>社区活动</strong>：活动模板、报名管理、签到系统</li>
            <li><strong>生活服务</strong>：服务商认证、预约管理、评价体系</li>
            <li><strong>邻里出行</strong>：拼车服务、车位共享、代驾预约</li>
            <li><strong>积分商城</strong>：任务系统、商品兑换、会员权益</li>
            <li><strong>消息中心</strong>：分类管理、实时通知、客服对话</li>
            <li><strong>个人中心</strong>：资料管理、安全设置、VIP服务</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 开发就绪说明 -->
    <div class="module-section" style="background: linear-gradient(135deg, var(--success-color), #73D13D); color: white;">
      <h2 style="color: white; margin-bottom: var(--space-lg); text-align: center;">🚀 开发就绪 - 可直接投入生产</h2>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: var(--space-lg); margin-bottom: var(--space-xl);">
        <div style="background: rgba(255,255,255,0.1); border-radius: var(--radius-lg); padding: var(--space-lg); text-align: center;">
          <i class="ri-check-double-line" style="font-size: 48px; margin-bottom: var(--space);"></i>
          <h4 style="margin-bottom: var(--space);">完整UI设计</h4>
          <p style="opacity: 0.9; line-height: 1.6;">8大核心模块，50+页面界面，覆盖所有用户场景，设计规范统一</p>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); border-radius: var(--radius-lg); padding: var(--space-lg); text-align: center;">
          <i class="ri-palette-line" style="font-size: 48px; margin-bottom: var(--space);"></i>
          <h4 style="margin-bottom: var(--space);">视觉系统</h4>
          <p style="opacity: 0.9; line-height: 1.6;">专业色彩搭配，清雅温暖风格，符合品牌定位，用户体验优秀</p>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); border-radius: var(--radius-lg); padding: var(--space-lg); text-align: center;">
          <i class="ri-code-s-slash-line" style="font-size: 48px; margin-bottom: var(--space);"></i>
          <h4 style="margin-bottom: var(--space);">技术规范</h4>
          <p style="opacity: 0.9; line-height: 1.6;">标准化CSS变量，模块化设计，响应式布局，开发友好</p>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); border-radius: var(--radius-lg); padding: var(--space-lg); text-align: center;">
          <i class="ri-smartphone-line" style="font-size: 48px; margin-bottom: var(--space);"></i>
          <h4 style="margin-bottom: var(--space);">移动优先</h4>
          <p style="opacity: 0.9; line-height: 1.6;">专为小程序设计，交互流畅，性能优化，用户体验佳</p>
        </div>
      </div>
      
      <div style="text-align: center; padding: var(--space-xl); background: rgba(255,255,255,0.1); border-radius: var(--radius-lg);">
        <h3 style="margin-bottom: var(--space);">✨ 设计完成度：100%</h3>
        <p style="font-size: 18px; opacity: 0.9; margin-bottom: var(--space-lg);">
          本UI设计已达到生产级标准，可直接用于小程序开发和发布。<br>
          所有界面、交互、色彩、组件均已完整设计，开发团队可立即开始编码实现。
        </p>
        <div style="display: flex; justify-content: center; gap: var(--space-lg); flex-wrap: wrap;">
          <div style="background: rgba(255,255,255,0.2); padding: var(--space) var(--space-lg); border-radius: 25px;">
            <strong>8</strong> 个核心模块
          </div>
          <div style="background: rgba(255,255,255,0.2); padding: var(--space) var(--space-lg); border-radius: 25px;">
            <strong>50+</strong> 页面界面
          </div>
          <div style="background: rgba(255,255,255,0.2); padding: var(--space) var(--space-lg); border-radius: 25px;">
            <strong>8</strong> 种专属色彩
          </div>
          <div style="background: rgba(255,255,255,0.2); padding: var(--space) var(--space-lg); border-radius: 25px;">
            <strong>100%</strong> 开发就绪
          </div>
        </div>
      </div>
    </div>

    <!-- 浮动操作按钮 -->
    <button class="fab" title="快速发布">
      <i class="ri-add-line"></i>
    </button>

    <!-- 在线客服按钮 -->
    <button class="customer-service" title="在线客服">
      <i class="ri-customer-service-line"></i>
      <div class="message-badge pulse-animation">!</div>
    </button>

    <!-- 社区活动详情页 -->
    <div class="module-section">
      <h2 class="module-title activity">社区活动详情页 - 报名与参与系统</h2>
      <div class="modules-grid">
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">活动详情</div>
              <div class="nav-action"><i class="ri-share-line"></i></div>
            </div>
            
            <div class="page-content" style="padding-bottom: 120px;">
              <!-- 活动封面图 -->
              <div style="height: 250px; background: var(--activity-gradient); margin: var(--space); border-radius: var(--radius-lg); overflow: hidden; position: relative;">
                <img src="https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=400&h=250&fit=crop" style="width: 100%; height: 100%; object-fit: cover;" alt="活动封面">
                
                <!-- 活动状态标签 -->
                <div style="position: absolute; top: var(--space); left: var(--space); background: var(--activity-gradient); color: white; padding: 6px 16px; border-radius: 20px; font-size: 12px; font-weight: 500;">
                  健身挑战
                </div>
                
                <!-- 倒计时 -->
                <div style="position: absolute; bottom: var(--space); right: var(--space); background: rgba(0,0,0,0.7); color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px;">
                  距离开始：1天2小时
                </div>
              </div>
              
              <!-- 活动基本信息 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space-lg); box-shadow: var(--shadow);">
                <div style="font-size: 22px; font-weight: 600; color: var(--text-primary); margin-bottom: var(--space); line-height: 1.4;">🏃‍♂️ 周末邻里健身挑战赛</div>
                
                <div style="color: var(--text-secondary); font-size: 15px; line-height: 1.6; margin-bottom: var(--space-lg);">
                  一起运动，增进邻里感情，还有丰厚奖品等你来拿！本次活动包括跑步、瑜伽、健身操等多种运动项目，适合各个年龄段的邻居参与。
                </div>
                
                <!-- 活动信息网格 -->
                <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space); margin-bottom: var(--space);">
                  <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--space);">
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">活动时间</div>
                      <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">本周六 09:00</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">活动地点</div>
                      <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">社区广场</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">活动时长</div>
                      <div style="font-size: 14px; color: var(--text-primary);">2小时</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">参与费用</div>
                      <div style="font-size: 14px; color: var(--success-color); font-weight: 600;">免费</div>
                    </div>
                  </div>
                </div>
                
                <!-- 报名进度 -->
                <div style="margin-bottom: var(--space);">
                  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-sm);">
                    <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">报名进度</div>
                    <div style="font-size: 14px; color: var(--activity-gradient); font-weight: 600;">12/20人</div>
                  </div>
                  <div style="background: var(--bg-secondary); border-radius: 8px; height: 8px; overflow: hidden;">
                    <div style="width: 60%; height: 100%; background: var(--activity-gradient);"></div>
                  </div>
                  <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px;">还剩8个名额</div>
                </div>
                
                <!-- 组织者信息 -->
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div style="display: flex; align-items: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--activity-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; margin-right: var(--space);">李</div>
                    <div>
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">邻居小李</div>
                      <div style="font-size: 12px; color: var(--text-secondary);">活动组织者 • ⭐ 4.8分</div>
                    </div>
                  </div>
                  <button style="background: var(--profile-gradient); color: white; border: none; padding: 8px 16px; border-radius: 20px; font-size: 12px;">
                    <i class="ri-message-line" style="margin-right: 4px;"></i>私信
                  </button>
                </div>
              </div>
              
              <!-- 活动详细介绍 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--activity-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  活动详情
                </div>
                
                <div style="color: var(--text-secondary); font-size: 14px; line-height: 1.6; margin-bottom: var(--space-lg);">
                  <p><strong>活动内容：</strong></p>
                  <p>1. 热身运动（15分钟）- 由专业健身教练带领</p>
                  <p>2. 团体跑步（30分钟）- 绕社区跑步路线</p>
                  <p>3. 健身操体验（30分钟）- 学习简单易学的健身动作</p>
                  <p>4. 瑜伽放松（30分钟）- 舒缓身心，增进邻里交流</p>
                  <p>5. 颁奖仪式（15分钟）- 为积极参与者颁发小礼品</p>
                  
                  <p style="margin-top: var(--space);"><strong>奖品设置：</strong></p>
                  <p>• 参与奖：邻里暖友定制水杯</p>
                  <p>• 坚持奖：运动毛巾 + 50积分</p>
                  <p>• 活力奖：健身器材套装 + 100积分</p>
                </div>
                
                <!-- 注意事项 -->
                <div style="background: rgba(255, 170, 108, 0.1); border-radius: var(--radius); padding: var(--space); border-left: 4px solid var(--warm-orange);">
                  <div style="font-size: 14px; color: var(--text-primary); font-weight: 500; margin-bottom: var(--space-sm);">
                    <i class="ri-information-line" style="color: var(--warm-orange); margin-right: var(--space-xs);"></i>
                    注意事项
                  </div>
                  <div style="font-size: 13px; color: var(--text-secondary); line-height: 1.5;">
                    • 请穿着舒适的运动服装和运动鞋<br>
                    • 自备水杯，现场提供饮用水<br>
                    • 如有身体不适请提前告知组织者<br>
                    • 雨天活动将改为室内进行
                  </div>
                </div>
              </div>
              
              <!-- 参与者列表 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--activity-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  已报名参与者 (12人)
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: var(--space);">
                  <div style="display: flex; flex-direction: column; align-items: center;">
                    <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--help-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-bottom: 4px;">张</div>
                    <div style="font-size: 10px; color: var(--text-tertiary); text-align: center;">暖友小张</div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center;">
                    <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--trade-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-bottom: 4px;">王</div>
                    <div style="font-size: 10px; color: var(--text-tertiary); text-align: center;">邻居小王</div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center;">
                    <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-bottom: 4px;">陈</div>
                    <div style="font-size: 10px; color: var(--text-tertiary); text-align: center;">陈阿姨</div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center;">
                    <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--info-color); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-bottom: 4px;">刘</div>
                    <div style="font-size: 10px; color: var(--text-tertiary); text-align: center;">刘大哥</div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center;">
                    <div style="width: 35px; height: 35px; border-radius: 50%; background: var(--warning-color); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-bottom: 4px;">+8</div>
                    <div style="font-size: 10px; color: var(--text-tertiary); text-align: center;">更多</div>
                  </div>
                </div>
              </div>
              
              <!-- 活动评论 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--activity-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  讨论区 (3条)
                </div>
                
                <!-- 评论1 -->
                <div style="border-bottom: 1px solid var(--border-light); padding-bottom: var(--space); margin-bottom: var(--space);">
                  <div style="display: flex; align-items: center; margin-bottom: var(--space-sm);">
                    <div style="width: 30px; height: 30px; border-radius: 50%; background: var(--trade-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-right: var(--space-sm);">王</div>
                    <div>
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">邻居小王</div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">5分钟前</div>
                    </div>
                  </div>
                  <div style="font-size: 14px; color: var(--text-secondary); line-height: 1.5;">
                    很期待这次活动！我已经很久没有和邻居们一起运动了，这次一定要坚持完成全程！💪
                  </div>
                </div>
                
                <!-- 评论2 -->
                <div style="border-bottom: 1px solid var(--border-light); padding-bottom: var(--space); margin-bottom: var(--space);">
                  <div style="display: flex; align-items: center; margin-bottom: var(--space-sm);">
                    <div style="width: 30px; height: 30px; border-radius: 50%; background: var(--service-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-right: var(--space-sm);">陈</div>
                    <div>
                      <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">陈阿姨</div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">15分钟前</div>
                    </div>
                  </div>
                  <div style="font-size: 14px; color: var(--text-secondary); line-height: 1.5;">
                    请问需要提前准备什么吗？我比较适合瑜伽，跑步可能跟不上大家的节奏。
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 底部操作栏 -->
            <div style="position: fixed; bottom: 83px; left: 0; right: 0; background: var(--bg-primary); border-top: 1px solid var(--border-light); padding: var(--space); display: flex; gap: var(--space); align-items: center;">
              <button style="background: var(--bg-secondary); color: var(--text-secondary); border: 1px solid var(--border-color); padding: var(--space); border-radius: var(--radius); font-size: 14px; min-width: 80px;">
                <i class="ri-message-line" style="margin-right: var(--space-xs);"></i>讨论
              </button>
              <button style="flex: 1; background: var(--activity-gradient); color: white; border: none; padding: var(--space); border-radius: var(--radius); font-size: 14px; font-weight: 500;">立即报名参加</button>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item active">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 邻里生活服务详情页 -->
    <div class="module-section">
      <h2 class="module-title service">生活服务详情页 - 预约与评价系统</h2>
      <div class="modules-grid">
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="status-bar">
              <div class="status-left"><span>9:41</span></div>
              <div class="status-right">
                <i class="ri-signal-wifi-line"></i>
                <i class="ri-battery-2-line"></i>
              </div>
            </div>
            
            <div class="nav-bar">
              <div class="nav-back"><i class="ri-arrow-left-line"></i></div>
              <div class="nav-title">服务详情</div>
              <div class="nav-action"><i class="ri-share-line"></i></div>
            </div>
            
            <div class="page-content" style="padding-bottom: 120px;">
              <!-- 服务商封面 -->
              <div style="height: 200px; background: var(--service-gradient); margin: var(--space); border-radius: var(--radius-lg); position: relative; overflow: hidden; display: flex; align-items: center; justify-content: center;">
                <div style="text-align: center; color: white; z-index: 2;">
                  <i class="ri-broom-line" style="font-size: 48px; margin-bottom: var(--space);"></i>
                  <div style="font-size: 20px; font-weight: 600; margin-bottom: 4px;">阿姨帮家政服务</div>
                  <div style="font-size: 14px; opacity: 0.8;">专业保洁服务 • 已服务156户</div>
                </div>
                <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              </div>
              
              <!-- 服务基本信息 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space-lg); box-shadow: var(--shadow);">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-lg);">
                  <div>
                    <div style="font-size: 20px; font-weight: 600; color: var(--text-primary); margin-bottom: var(--space-sm);">专业家庭保洁</div>
                    <div style="display: flex; align-items: center; gap: var(--space); margin-bottom: var(--space-sm);">
                      <div style="background: rgba(114, 46, 209, 0.1); color: #722ED1; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 500;">认证服务商</div>
                      <div style="display: flex; align-items: center; color: var(--warm-orange);">
                        <i class="ri-star-fill" style="font-size: 14px; margin-right: 4px;"></i>
                        <span style="font-size: 14px; font-weight: 500;">4.9分</span>
                      </div>
                    </div>
                    <div style="font-size: 14px; color: var(--text-secondary);">提供专业的家庭保洁服务，经验丰富，服务贴心，价格合理。</div>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-size: 20px; font-weight: 600; color: #722ED1; margin-bottom: 4px;">¥80</div>
                    <div style="font-size: 12px; color: var(--text-tertiary);">起/次</div>
                  </div>
                </div>
                
                <!-- 服务信息网格 -->
                <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space);">
                  <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--space);">
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">服务区域</div>
                      <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">欧美金融城全区</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">服务时间</div>
                      <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">08:00-18:00</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">从业经验</div>
                      <div style="font-size: 14px; color: var(--text-primary);">5年</div>
                    </div>
                    <div>
                      <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">平均响应</div>
                      <div style="font-size: 14px; color: var(--success-color); font-weight: 500;">30分钟内</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 服务项目 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--service-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  服务项目
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--space);">
                  <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space); text-align: center;">
                    <i class="ri-home-4-line" style="font-size: 24px; color: #722ED1; margin-bottom: var(--space-sm);"></i>
                    <div style="font-size: 14px; font-weight: 500; color: var(--text-primary); margin-bottom: 4px;">深度保洁</div>
                    <div style="font-size: 12px; color: var(--text-secondary);">全屋深度清洁</div>
                    <div style="font-size: 14px; color: #722ED1; font-weight: 600; margin-top: var(--space-sm);">¥120/次</div>
                  </div>
                  
                  <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space); text-align: center;">
                    <i class="ri-home-line" style="font-size: 24px; color: #722ED1; margin-bottom: var(--space-sm);"></i>
                    <div style="font-size: 14px; font-weight: 500; color: var(--text-primary); margin-bottom: 4px;">日常保洁</div>
                    <div style="font-size: 12px; color: var(--text-secondary);">基础清洁维护</div>
                    <div style="font-size: 14px; color: #722ED1; font-weight: 600; margin-top: var(--space-sm);">¥80/次</div>
                  </div>
                  
                  <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space); text-align: center;">
                    <i class="ri-window-line" style="font-size: 24px; color: #722ED1; margin-bottom: var(--space-sm);"></i>
                    <div style="font-size: 14px; font-weight: 500; color: var(--text-primary); margin-bottom: 4px;">玻璃清洁</div>
                    <div style="font-size: 12px; color: var(--text-secondary);">窗户玻璃专项</div>
                    <div style="font-size: 14px; color: #722ED1; font-weight: 600; margin-top: var(--space-sm);">¥60/次</div>
                  </div>
                  
                  <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space); text-align: center;">
                    <i class="ri-shirt-line" style="font-size: 24px; color: #722ED1; margin-bottom: var(--space-sm);"></i>
                    <div style="font-size: 14px; font-weight: 500; color: var(--text-primary); margin-bottom: 4px;">衣物整理</div>
                    <div style="font-size: 12px; color: var(--text-secondary);">衣橱整理收纳</div>
                    <div style="font-size: 14px; color: #722ED1; font-weight: 600; margin-top: var(--space-sm);">¥100/次</div>
                  </div>
                </div>
              </div>
              
              <!-- 服务商资质 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--service-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  资质认证
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: var(--space);">
                  <div style="text-align: center;">
                    <div style="width: 50px; height: 50px; border-radius: 50%; background: rgba(82, 196, 26, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: var(--success-color); font-size: 20px;">
                      <i class="ri-shield-check-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">身份认证</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 50px; height: 50px; border-radius: 50%; background: rgba(24, 144, 255, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: var(--info-color); font-size: 20px;">
                      <i class="ri-award-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">技能证书</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 50px; height: 50px; border-radius: 50%; background: rgba(114, 46, 209, 0.1); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-sm); color: #722ED1; font-size: 20px;">
                      <i class="ri-heart-pulse-line"></i>
                    </div>
                    <div style="font-size: 12px; color: var(--text-secondary);">健康证明</div>
                  </div>
                </div>
              </div>
              
              <!-- 客户评价 -->
              <div style="background: var(--bg-primary); margin: 0 var(--space) var(--space); border-radius: var(--radius-lg); padding: var(--space); box-shadow: var(--shadow-sm);">
                <div style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin-bottom: var(--space); display: flex; align-items: center;">
                  <div style="width: 4px; height: 18px; background: var(--service-gradient); border-radius: 2px; margin-right: var(--space);"></div>
                  客户评价 (156条)
                </div>
                
                <!-- 评价统计 -->
                <div style="background: var(--bg-secondary); border-radius: var(--radius); padding: var(--space); margin-bottom: var(--space);">
                  <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="text-align: center;">
                      <div style="font-size: 24px; font-weight: 600; color: var(--warm-orange); margin-bottom: 4px;">4.9</div>
                      <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 4px;">
                        <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                        <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                        <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                        <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                        <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                      </div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">综合评分</div>
                    </div>
                    <div style="flex: 1; margin-left: var(--space-lg);">
                      <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px;">好评率 98%</div>
                      <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px;">服务态度 4.9分</div>
                      <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 4px;">专业技能 4.8分</div>
                      <div style="font-size: 12px; color: var(--text-secondary);">准时到达 4.9分</div>
                    </div>
                  </div>
                </div>
                
                <!-- 评价列表 -->
                <div style="border-bottom: 1px solid var(--border-light); padding-bottom: var(--space); margin-bottom: var(--space);">
                  <div style="display: flex; align-items: center; margin-bottom: var(--space-sm);">
                    <div style="width: 30px; height: 30px; border-radius: 50%; background: var(--help-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-right: var(--space-sm);">李</div>
                    <div style="flex: 1;">
                      <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="font-size: 14px; color: var(--text-primary);">李女士</div>
                        <div style="display: flex; align-items: center;">
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                        </div>
                      </div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">3天前 • 深度保洁</div>
                    </div>
                  </div>
                  <div style="font-size: 14px; color: var(--text-secondary); line-height: 1.5;">
                    服务非常专业，阿姨很认真负责，把家里打扫得非常干净。特别是厨房和卫生间，清洁得很彻底。会继续选择这家服务。
                  </div>
                </div>
                
                <div>
                  <div style="display: flex; align-items: center; margin-bottom: var(--space-sm);">
                    <div style="width: 30px; height: 30px; border-radius: 50%; background: var(--trade-gradient); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; margin-right: var(--space-sm);">王</div>
                    <div style="flex: 1;">
                      <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="font-size: 14px; color: var(--text-primary);">王先生</div>
                        <div style="display: flex; align-items: center;">
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                          <i class="ri-star-fill" style="color: var(--warm-orange); font-size: 12px;"></i>
                        </div>
                      </div>
                      <div style="font-size: 12px; color: var(--text-tertiary);">1周前 • 日常保洁</div>
                    </div>
                  </div>
                  <div style="font-size: 14px; color: var(--text-secondary); line-height: 1.5;">
                    很满意！阿姨准时到达，工作很细致，价格也合理。已经预约了下次服务。
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 底部操作栏 -->
            <div style="position: fixed; bottom: 83px; left: 0; right: 0; background: var(--bg-primary); border-top: 1px solid var(--border-light); padding: var(--space); display: flex; gap: var(--space); align-items: center;">
              <button style="background: var(--bg-secondary); color: var(--text-secondary); border: 1px solid var(--border-color); padding: var(--space); border-radius: var(--radius); font-size: 14px; min-width: 80px;">
                <i class="ri-message-line" style="margin-right: var(--space-xs);"></i>咨询
              </button>
              <button style="flex: 1; background: var(--service-gradient); color: white; border: none; padding: var(--space); border-radius: var(--radius); font-size: 14px; font-weight: 500;">立即预约服务</button>
            </div>
            
            <div class="tab-bar">
              <div class="tab-item">
                <i class="ri-home-line tab-icon"></i>
                <span>首页</span>
              </div>
              <div class="tab-item">
                <i class="ri-heart-line tab-icon"></i>
                <span>邻里互助</span>
              </div>
              <div class="tab-item">
                <i class="ri-exchange-line tab-icon"></i>
                <span>闲置交易</span>
              </div>
              <div class="tab-item">
                <i class="ri-calendar-event-line tab-icon"></i>
                <span>社区活动</span>
              </div>
              <div class="tab-item active">
                <i class="ri-user-line tab-icon"></i>
                <span>我的</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 邻里出行模块 -->
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // 简单的交互效果
    document.addEventListener('DOMContentLoaded', function() {
      // 为卡片添加悬停效果
      const cards = document.querySelectorAll('.card-item');
      cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-4px)';
          this.style.boxShadow = 'var(--shadow-lg)';
        });
        
        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0)';
          this.style.boxShadow = 'var(--shadow-sm)';
        });
      });
      
      // 客服按钮点击效果
      const customerService = document.querySelector('.customer-service');
      if (customerService) {
        customerService.addEventListener('click', function() {
          alert('客服功能演示：\n\n欢迎使用我的邻里暖友小程序！\n\n如需帮助，请通过以下方式联系我们：\n• 在线客服：24小时服务\n• 客服电话：400-123-4567\n• 邮箱：<EMAIL>');
        });
      }
      
      // FAB按钮点击效果
      const fab = document.querySelector('.fab');
      if (fab) {
        fab.addEventListener('click', function() {
          alert('快速发布功能演示：\n\n您可以快速发布：\n• 邻里互助需求\n• 闲置物品交易\n• 社区活动组织\n• 生活服务需求');
        });
      }
    });
  </script>
</body>
</html>